@echo off
chcp 65001 >nul
echo 🔄 جاري حذف الملفات القديمة...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist

echo 🔧 بدء تحويل app.py إلى exe باستخدام ملف المواصفات المحدث...
echo ⚙️ استخدام إعدادات محسنة لحل مشاكل PDF...

pyinstaller app.spec

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء التطبيق!
    pause
    exit /b 1
)

echo 📁 نسخ الملفات الضرورية إلى مجلد dist...

REM إنشاء المجلدات الضرورية
if not exist dist\instance mkdir dist\instance
if not exist dist\uploads mkdir dist\uploads
if not exist dist\uploads\documents mkdir dist\uploads\documents
if not exist dist\uploads\scans mkdir dist\uploads\scans
if not exist dist\backups mkdir dist\backups

REM نسخ قواعد البيانات إذا كانت موجودة
echo 🔧 نسخ قواعد البيانات إذا كانت موجودة...
if exist instance\ajazat.db (
    copy instance\ajazat.db dist\instance\
    echo ✅ تم نسخ ajazat.db
)
if exist instance\employees.db (
    copy instance\employees.db dist\instance\
    echo ✅ تم نسخ employees.db
)

REM نسخ الملفات الثابتة الإضافية
echo 📄 نسخ الملفات الثابتة الإضافية...
if exist icon.ico copy icon.ico dist\

echo ✅ تم إنشاء الملف التنفيذي بنجاح!
echo 📍 الملف موجود في: dist\app.exe
echo 📝 ملاحظات مهمة:
echo    - تم تفعيل وضع التشخيص لرؤية رسائل الخطأ
echo    - تم تحسين إعدادات PyInstaller لحل مشاكل PDF
echo    - تم تضمين جميع المكتبات المطلوبة
echo    - يمكنك الآن تشغيل التطبيق من dist\app.exe
echo.
echo 🚀 لتشغيل التطبيق، انتقل إلى مجلد dist وشغل app.exe
pause
