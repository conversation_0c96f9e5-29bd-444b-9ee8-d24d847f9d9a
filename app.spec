# -*- mode: python ; coding: utf-8 -*-

import os

# الحصول على المسار الحالي
current_dir = os.path.dirname(os.path.abspath(SPEC))

a = Analysis(
    ['app.py'],
    pathex=[current_dir],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
        ('uploads', 'uploads'),
        ('app_files/templates', 'app_files/templates'),
        ('app_files/static', 'app_files/static'),
        ('instance', 'instance'),
        ('backups', 'backups')
    ],
    hiddenimports=[
        'win32com.client',
        'pythoncom',
        'PIL',
        'reportlab',
        'img2pdf',
        'flask',
        'sqlalchemy',
        'werkzeug'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='app',
    debug=True,  # تفعيل وضع التشخيص
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # إيقاف UPX لتجنب مشاكل التوافق
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # تفعيل وحدة التحكم لرؤية رسائل الخطأ
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['icon.ico'],
)
