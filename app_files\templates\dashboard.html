{% extends 'base.html' %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/indicators.css') }}">
<style>
    .bg-gradient-primary-to-secondary {
        background: linear-gradient(135deg, #0d47a1 0%, #002171 100%);
    }

    .icon-circle {
        height: 32px;
        width: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        font-size: 0.75rem;
    }

    .avatar-circle {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.7rem;
    }

    /* تصغير البطاقات */
    .dashboard-stat-card {
        min-height: 100px;
    }

    .dashboard-stat-card .card-body {
        padding: 0.75rem;
    }

    .dashboard-stat-card h1 {
        font-size: 1.5rem;
    }

    /* ألوان إضافية */
    .bg-pink {
        background-color: #ad1457 !important;
    }

    .text-pink {
        color: #ad1457 !important;
    }

    /* تحسين جداول الإجازات */
    .leave-table th {
        font-size: 0.8rem;
        padding: 0.5rem;
        white-space: nowrap;
    }

    .leave-table td {
        font-size: 0.8rem;
        padding: 0.5rem;
    }

    /* تنسيق خلايا التاريخ */
    .leave-table td:nth-child(3),
    .leave-table td:nth-child(4) {
        width: 90px;
        max-width: 90px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .leave-table .btn-sm {
        padding: 0.1rem 0.4rem;
        font-size: 0.7rem;
    }


</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="card mb-3 border-0 shadow-sm">
        <div class="card-body bg-gradient-primary-to-secondary text-white py-2">
            <div class="d-flex align-items-center">
                <div class="avatar-circle bg-white text-primary me-3" style="height: 40px; width: 40px; font-size: 1.2rem;">
                    {{ session.get('employee_name')[0] if session.get('employee_name') else 'A' }}
                </div>
                <div>
                    <h2 class="card-title h5 mb-1">
                        مرحباً {{ session.get('employee_name', 'المستخدم') }}
                    </h2>
                    <p class="mb-0 small">مدير النظام</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-2">
            <div class="card border-0 shadow-sm h-100 dashboard-stat-card">
                <div class="card-body text-center position-relative">
                    <div class="d-flex align-items-center mb-1">
                        <div class="icon-circle bg-primary text-white me-2">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <h3 class="card-title h6 mb-0">رصيد الإجازات</h3>
                    </div>
                    <h1 class="fw-bold text-primary mb-0">36 <small class="text-muted" style="font-size: 0.8rem;">يوم</small></h1>
                </div>
            </div>
        </div>

        <div class="col-md-3 col-sm-6 mb-2">
            <div class="card border-0 shadow-sm h-100 dashboard-stat-card">
                <div class="card-body text-center position-relative">
                    <div class="d-flex align-items-center mb-1">
                        <div class="icon-circle bg-success text-white me-2">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="card-title h6 mb-0">الموظفون في إجازة</h3>
                    </div>
                    <h1 class="fw-bold text-success mb-0">{{ employees_on_leave|length }} <small class="text-muted" style="font-size: 0.8rem;">موظف</small></h1>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-2">
            <div class="card border-0 shadow-sm h-100 dashboard-stat-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-2">
                        <div class="icon-circle bg-info text-white me-2">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <h3 class="card-title h6 mb-0">إحصائيات الإجازات</h3>
                    </div>
                    <div class="d-flex justify-content-around flex-wrap">
                        {% if leave_types_count %}
                            {% for leave_type_name, count in leave_types_count.items() %}
                                {% set colors = {
                                    'اعتيادية': 'primary',
                                    'مرضية': 'danger',
                                    'أمومة': 'pink',
                                    'دراسية': 'warning',
                                    'طويلة': 'secondary'
                                } %}
                                {% set color = colors[leave_type_name] if leave_type_name in colors else 'info' %}
                                <div class="text-center mx-1 mb-1" style="min-width: 60px; max-width: 80px;">
                                    <span class="badge bg-{{ color }} mb-1 d-block w-100" style="font-size: 0.65rem;">{{ leave_type_name }}</span>
                                    <div class="text-{{ color }} border border-{{ color }} rounded py-1" style="font-size: 1rem; font-weight: bold;">{{ count }}</div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted mb-0 small">لا توجد إجازات حالية</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- قائمة الموظفين في إجازة مصنفة حسب النوع -->
    {% if leave_types_dict %}
        <div class="row">
            {% for leave_type_name, employees in leave_types_dict.items() %}
                {% set colors = {
                    'اعتيادية': 'primary',
                    'مرضية': 'danger',
                    'أمومة': 'pink',
                    'دراسية': 'warning',
                    'طويلة': 'secondary'
                } %}
                {% set icons = {
                    'اعتيادية': 'calendar-alt',
                    'مرضية': 'procedures',
                    'أمومة': 'baby',
                    'دراسية': 'graduation-cap',
                    'طويلة': 'hourglass-half'
                } %}
                {% set color = colors[leave_type_name] if leave_type_name in colors else 'info' %}
                {% set icon = icons[leave_type_name] if leave_type_name in icons else 'calendar-check' %}

                <div class="col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-{{ color }} text-white py-2 d-flex justify-content-between align-items-center">
                            <h3 class="card-title h6 mb-0">
                                <i class="fas fa-{{ icon }} me-1 classification-icon"></i> إجازة {{ leave_type_name }}
                            </h3>
                            <span class="badge bg-white text-{{ color }}">{{ employees|length }} موظف</span>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-sm table-hover mb-0 leave-table">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="ps-3">الموظف</th>
                                            <th>محل العمل</th>
                                            <th>تاريخ البداية</th>
                                            <th>تاريخ النهاية</th>
                                            <th>الأيام المتبقية</th>
                                            <th class="text-center">التفاصيل</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in employees %}
                                        <tr class="leave-row {% if item.expired %}table-danger{% endif %}">
                                            <td class="ps-3">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-circle bg-{{ color }} text-white me-2">
                                                        {{ item.employee.full_name[0] }}
                                                    </div>
                                                    <span class="employee-name">
                                                        {{ item.employee.full_name }}
                                                        {% if item.expired %}
                                                            <span class="badge bg-danger ms-1" style="font-size: 0.7rem;">انتهت الإجازة</span>
                                                        {% elif item.days_left <= 3 %}
                                                            <span class="badge bg-warning text-dark ms-1" style="font-size: 0.7rem;">تنتهي قريبًا</span>
                                                        {% endif %}
                                                    </span>
                                                </div>
                                            </td>
                                            <td>{{ item.employee.work_location }}</td>
                                            <td class="date-cell">{{ item.leave.start_date.strftime('%Y-%m-%d') }}</td>
                                            <td class="date-cell">{{ item.leave.end_date.strftime('%Y-%m-%d') }}</td>
                                            <td>
                                                {% if item.expired %}
                                                    <span class="text-danger fw-bold">{{ item.days_left }} يوم</span>
                                                {% elif item.days_left <= 3 %}
                                                    <span class="text-warning fw-bold">{{ item.days_left }} يوم</span>
                                                {% else %}
                                                    {{ item.days_left }} يوم
                                                {% endif %}
                                            </td>
                                            <td class="text-center">
                                                <a href="{{ url_for('employee_leaves', employee_id=item.employee.id) }}" class="btn btn-sm btn-{{ color }}">
                                                    <i class="fas fa-info-circle"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-info text-center border-0 shadow-sm">
            <i class="fas fa-info-circle me-2"></i> لا يوجد موظفون في إجازة حالياً
        </div>
    {% endif %}


</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<style>
/* مؤشر الإجازات المنتهية */
.leave-ended {
    position: relative;
    padding-right: 30px !important;
    color: #d50000 !important;
    font-weight: bold !important;
    animation: blink-red 1s infinite;
}

.leave-ended::before {
    content: '';
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #d50000;
    animation: pulse-red 1s infinite;
}

/* مؤشر الإجازات التي ستنتهي قريبًا */
.leave-ending-soon {
    position: relative;
    padding-right: 30px !important;
    color: #ff6d00 !important;
    font-weight: bold !important;
    animation: blink-orange 1.5s infinite;
}

.leave-ending-soon::before {
    content: '';
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #ff6d00;
    animation: pulse-orange 1.5s infinite;
}

/* تأثيرات الوميض */
@keyframes blink-red {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

@keyframes blink-orange {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

@keyframes pulse-red {
    0% { box-shadow: 0 0 0 0 rgba(213, 0, 0, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(213, 0, 0, 0); }
    100% { box-shadow: 0 0 0 0 rgba(213, 0, 0, 0); }
}

@keyframes pulse-orange {
    0% { box-shadow: 0 0 0 0 rgba(255, 109, 0, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 109, 0, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 109, 0, 0); }
}
</style>

<script>
// تطبيق مؤشرات الإجازات
document.addEventListener('DOMContentLoaded', function() {
    // الحصول على التاريخ الحالي
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // البحث عن جميع صفوف الإجازات
    const rows = document.querySelectorAll('tr');

    rows.forEach(function(row) {
        // البحث عن خلية تاريخ الانتهاء
        const cells = row.querySelectorAll('td');
        if (cells.length >= 4) {
            const endDateCell = cells[3]; // خلية تاريخ الانتهاء (الرابعة)
            const endDateText = endDateCell.textContent.trim();

            if (endDateText) {
                // تحويل النص إلى تاريخ
                const parts = endDateText.split('-');
                if (parts.length === 3) {
                    const endDate = new Date(parts[0], parts[1] - 1, parts[2]);

                    // حساب الفرق بين التاريخين بالأيام
                    const diffTime = endDate - today;
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                    // البحث عن خلية اسم الموظف
                    const nameCell = cells[0]; // خلية اسم الموظف (الأولى)
                    const nameSpan = nameCell.querySelector('span');

                    if (nameSpan) {
                        // إذا كانت الإجازة منتهية
                        if (diffDays < 0) {
                            nameSpan.classList.add('leave-ended');

                            // إضافة شارة "انتهت"
                            const badge = document.createElement('span');
                            badge.className = 'badge bg-danger ms-1';
                            badge.style.fontSize = '0.7rem';
                            badge.textContent = 'انتهت';
                            nameSpan.appendChild(badge);
                        }
                        // إذا كانت الإجازة ستنتهي خلال 3 أيام أو أقل
                        else if (diffDays >= 0 && diffDays <= 3) {
                            nameSpan.classList.add('leave-ending-soon');

                            // إضافة شارة "تنتهي قريبًا"
                            const badge = document.createElement('span');
                            badge.className = 'badge bg-warning text-dark ms-1';
                            badge.style.fontSize = '0.7rem';
                            badge.textContent = `تنتهي قريبًا (${diffDays} يوم)`;
                            nameSpan.appendChild(badge);
                        }
                    }
                }
            }
        }
    });
});
</script>
{% endblock %}