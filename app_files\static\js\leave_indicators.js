// تطبيق مؤشرات الإجازات

document.addEventListener('DOMContentLoaded', function() {
    // الحصول على التاريخ الحالي
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // البحث عن جميع صفوف الإجازات
    const leaveRows = document.querySelectorAll('.leave-row');
    
    leaveRows.forEach(function(row) {
        // الحصول على تاريخ انتهاء الإجازة
        const endDateStr = row.getAttribute('data-end-date');
        const hasReturned = row.getAttribute('data-has-returned') === 'true';
        
        if (endDateStr) {
            // تحويل التاريخ إلى كائن Date
            const endDate = new Date(endDateStr);
            endDate.setHours(0, 0, 0, 0);
            
            // حساب الفرق بين التاريخين بالأيام
            const diffTime = endDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            // الحصول على عنصر اسم الموظف
            const nameElement = row.querySelector('.employee-name');
            
            if (nameElement) {
                // إذا كانت الإجازة منتهية ولم يباشر الموظف
                if (diffDays < 0 && !hasReturned) {
                    nameElement.classList.add('leave-ended');
                    
                    // إضافة شارة "انتهت"
                    const badge = document.createElement('span');
                    badge.className = 'badge bg-danger ms-1';
                    badge.style.fontSize = '0.7rem';
                    badge.textContent = 'انتهت';
                    nameElement.appendChild(badge);
                }
                // إذا كانت الإجازة ستنتهي خلال 3 أيام أو أقل
                else if (diffDays >= 0 && diffDays <= 3) {
                    nameElement.classList.add('leave-ending-soon');
                    
                    // إضافة شارة "تنتهي قريبًا"
                    const badge = document.createElement('span');
                    badge.className = 'badge bg-warning text-dark ms-1';
                    badge.style.fontSize = '0.7rem';
                    badge.textContent = `تنتهي قريبًا (${diffDays} يوم)`;
                    nameElement.appendChild(badge);
                }
            }
        }
    });
});
