{% extends 'base.html' %}

{% block styles %}
<style>
    .bg-gradient-primary-to-secondary {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    }

    .card {
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .form-control:focus, .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
    }

    .btn-primary {
        background-color: #4e73df;
        border-color: #4e73df;
    }

    .btn-primary:hover {
        background-color: #224abe;
        border-color: #224abe;
    }

    /* إصلاح اتجاه النص في حقول التاريخ */
    input[type="date"] {
        direction: ltr;
        text-align: right;
        font-family: Arial, sans-serif !important;
        background-color: #f8f9ff !important;
        border: 2px solid #d1d9ff !important;
        color: #333 !important;
        font-weight: 500 !important;
    }

    input[type="date"]:focus {
        background-color: #fff !important;
        border-color: #4e73df !important;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25) !important;
    }

    /* تحسين مظهر حقول الإدخال الأخرى */
    input[type="number"], select, textarea {
        background-color: #f8f9ff !important;
        border: 2px solid #d1d9ff !important;
        color: #333 !important;
        font-weight: 500 !important;
    }

    input[type="number"]:focus, select:focus, textarea:focus {
        background-color: #fff !important;
        border-color: #4e73df !important;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25) !important;
    }

    /* تحسين مظهر العناوين */
    .form-label {
        font-weight: bold !important;
        color: #2e4094 !important;
        font-size: 1.05rem !important;
    }

    /* إخفاء النص الافتراضي في حقول التاريخ */
    input[type="date"]::before {
        content: attr(placeholder);
        width: 100%;
        color: #666;
        font-weight: normal;
    }

    input[type="date"]:focus::before,
    input[type="date"]:valid::before {
        display: none;
    }

    /* إخفاء أيقونة التقويم الافتراضية وإضافة أيقونة مخصصة */
    input[type="date"]::-webkit-calendar-picker-indicator {
        opacity: 1;
        display: block;
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 24 24"><path fill="%234e73df" d="M20 3h-1V1h-2v2H7V1H5v2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 18H4V8h16v13z"/></svg>');
        width: 20px;
        height: 20px;
        cursor: pointer;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-gradient-primary-to-secondary text-white">
            <h2 class="mb-0"><i class="fas fa-calendar-plus me-2"></i> طلب إجازة جديدة</h2>
        </div>
        <div class="card-body bg-light">
            <form method="POST">
                <div class="mb-3">
                    <label for="leave_type_id" class="form-label">نوع الإجازة:</label>
                    <div class="input-group">
                        <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                            <i class="fas fa-list-alt"></i>
                        </span>
                        <select name="leave_type_id" id="leave_type_id" class="form-select" required>
                            <option value="">اختر نوع الإجازة</option>
                            {% for leave_type in leave_types %}
                            <option value="{{ leave_type.id }}" data-days="{{ leave_type.days_allowed }}">
                                {{ leave_type.name }}
                                {% if leave_type.description %}
                                ({{ leave_type.description }})
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="mb-3" id="children-count-container" style="display: none;">
                    <label for="children-count" class="form-label">عدد الأطفال:</label>
                    <div class="input-group">
                        <span class="input-group-text bg-primary text-white">
                            <i class="fas fa-baby"></i>
                        </span>
                        <input type="number" name="children_count" id="children-count" class="form-control" min="1">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="start-date" class="form-label">تاريخ بدء الإجازة:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                                <i class="fas fa-calendar-day"></i>
                            </span>
                            <input type="date" id="start-date" name="start_date" class="form-control" placeholder="اختر تاريخ البداية" required>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="days-count" class="form-label">مدة الإجازة (بالأيام):</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                                <i class="fas fa-calculator"></i>
                            </span>
                            <input type="number" id="days-count" name="days_count" class="form-control" min="1" value="1" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="end-date" class="form-label">تاريخ انتهاء الإجازة:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                                <i class="fas fa-calendar-check"></i>
                            </span>
                            <input type="date" id="end-date" name="end_date" class="form-control" placeholder="تاريخ النهاية" readonly>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="departure-date" class="form-label">تاريخ الانفكاك من العمل:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                                <i class="fas fa-sign-out-alt"></i>
                            </span>
                            <input type="date" id="departure-date" name="departure_date" class="form-control" placeholder="تاريخ الانفكاك">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="return-date" class="form-label">تاريخ العودة للعمل:</label>
                    <div class="input-group">
                        <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                            <i class="fas fa-calendar-plus"></i>
                        </span>
                        <input type="date" id="return-date" name="return_date" class="form-control" placeholder="تاريخ المباشرة">
                    </div>
                </div>

                <div class="mb-3">
                    <label for="comment" class="form-label">ملاحظات:</label>
                    <div class="input-group">
                        <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                            <i class="fas fa-comment-alt"></i>
                        </span>
                        <textarea name="comment" id="comment" class="form-control" rows="3"></textarea>
                    </div>
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-paper-plane me-2"></i> تقديم الطلب
                    </button>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-secondary btn-lg">
                        <i class="fas fa-times me-2"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

{% block scripts %}
<script src="{{ url_for('static', filename='js/leave_request.js') }}"></script>
{% endblock %}
{% endblock %}

