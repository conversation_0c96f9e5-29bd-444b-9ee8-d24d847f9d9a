from app import app
from models import db, LeaveType

def add_default_leave_types():
    with app.app_context():
        # التحقق من وجود أنواع إجازات
        if LeaveType.query.count() > 0:
            print("أنواع الإجازات موجودة بالفعل")
            return
            
        # إضافة أنواع الإجازات
        leave_types = [
            {'name': 'إجازة اعتيادية', 'days_allowed': 30, 'description': 'إجازة اعتيادية سنوية'},
            {'name': 'إجازة مرضية', 'days_allowed': 30, 'description': 'إجازة مرضية'},
            {'name': 'إجازة المعيل', 'days_allowed': 15, 'description': 'إجازة للمعيل'},
            {'name': 'إجازة خمس سنوات', 'days_allowed': 60, 'description': 'إجازة بعد خمس سنوات من الخدمة'},
            {'name': 'إجازة بدون راتب', 'days_allowed': None, 'description': 'إجازة بدون راتب'},
            {'name': 'إجازة ما قبل الوضع', 'days_allowed': 21, 'description': '21 يوم قبل الوضع'},
            {'name': 'إجازة ما بعد الوضع', 'days_allowed': 51, 'description': '51 يوم بعد الوضع'},
            {'name': 'إجازة أمومة', 'days_allowed': 365, 'description': 'إجازة أمومة لمدة سنة'},
            {'name': 'إجازة دراسية', 'days_allowed': 180, 'description': 'إجازة للدراسة'},
            {'name': 'إجازة طويلة', 'days_allowed': 365, 'description': 'إجازة طويلة الأمد'}
        ]
        
        for lt_data in leave_types:
            leave_type = LeaveType(**lt_data)
            db.session.add(leave_type)
        
        db.session.commit()
        print("تم إضافة أنواع الإجازات بنجاح")

if __name__ == "__main__":
    add_default_leave_types()
