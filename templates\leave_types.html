{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2 class="mb-0">أنواع الإجازات</h2>
            <a href="{{ url_for('add_leave_type') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> إضافة نوع إجازة جديد
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>#</th>
                            <th>اسم نوع الإجازة</th>
                            <th>الوصف</th>
                            <th>عدد الأيام المسموح بها</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if leave_types %}
                            {% for leave_type in leave_types %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ leave_type.name }}</td>
                                <td>{{ leave_type.description or '-' }}</td>
                                <td>{{ leave_type.days_allowed or 'غير محدد' }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('edit_leave_type', leave_type_id=leave_type.id) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        <a href="{{ url_for('delete_leave_type', leave_type_id=leave_type.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا النوع من الإجازات؟')">
                                            <i class="fas fa-trash"></i> حذف
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="5" class="text-center">لا توجد أنواع إجازات مسجلة</td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
