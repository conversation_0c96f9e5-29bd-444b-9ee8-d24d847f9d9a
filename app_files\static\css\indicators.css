/* أنماط مؤشرات الإجازات */

/* مؤشر الإجازات المنتهية */
.leave-ended {
    position: relative;
    padding-right: 30px !important;
    color: #d50000 !important;
    font-weight: bold !important;
    animation: blink-red 1s infinite;
}

.leave-ended::before {
    content: '';
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #d50000;
    animation: pulse-red 1s infinite;
}

/* مؤشر الإجازات التي ستنتهي قريبًا */
.leave-ending-soon {
    position: relative;
    padding-right: 30px !important;
    color: #ff6d00 !important;
    font-weight: bold !important;
    animation: blink-orange 1.5s infinite;
}

.leave-ending-soon::before {
    content: '';
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #ff6d00;
    animation: pulse-orange 1.5s infinite;
}

/* تأثيرات الوميض */
@keyframes blink-red {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

@keyframes blink-orange {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

@keyframes pulse-red {
    0% { box-shadow: 0 0 0 0 rgba(213, 0, 0, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(213, 0, 0, 0); }
    100% { box-shadow: 0 0 0 0 rgba(213, 0, 0, 0); }
}

@keyframes pulse-orange {
    0% { box-shadow: 0 0 0 0 rgba(255, 109, 0, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 109, 0, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 109, 0, 0); }
}
