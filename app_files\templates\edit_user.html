{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
            <h2 class="mb-0">
                <i class="fas fa-user-edit me-2"></i> تعديل بيانات المستخدم
            </h2>
            <span class="badge bg-light text-dark">{{ user.username }}</span>
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="full_name" class="form-label">الاسم الكامل:</label>
                        <input type="text" id="full_name" name="full_name" class="form-control" value="{{ user.full_name }}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني:</label>
                        <input type="email" id="email" name="email" class="form-control" value="{{ user.email or '' }}">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="job_title" class="form-label">المسمى الوظيفي:</label>
                        <input type="text" id="job_title" name="job_title" class="form-control" value="{{ user.job_title }}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="work_location" class="form-label">مكان العمل:</label>
                        <input type="text" id="work_location" name="work_location" class="form-control" value="{{ user.work_location }}" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="department" class="form-label">القسم:</label>
                        <input type="text" id="department" name="department" class="form-control" value="{{ user.department or '' }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="leave_balance" class="form-label">رصيد الإجازات:</label>
                        <input type="number" id="leave_balance" name="leave_balance" class="form-control" value="{{ user.leave_balance }}" min="0" required>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">نوع الحساب:</label>
                    <div class="form-check">
                        <input type="checkbox" id="is_admin" name="is_admin" class="form-check-input" {% if user.is_admin %}checked{% endif %}>
                        <label for="is_admin" class="form-check-label">مدير النظام (صلاحيات كاملة)</label>
                    </div>
                </div>

                <div class="mb-3" id="permissions_section">
                    <label class="form-label">الصلاحيات:</label>
                    <div class="form-check mb-2">
                        <input type="checkbox" id="perm_all" name="permissions" value="all" class="form-check-input" {% if user.permissions and 'all' in user.permissions %}checked{% endif %}>
                        <label for="perm_all" class="form-check-label"><strong>جميع الصلاحيات</strong></label>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" id="perm_view_employees" name="permissions" value="view_employees" class="form-check-input permission-checkbox" {% if user.permissions and 'view_employees' in user.permissions %}checked{% endif %}>
                                <label for="perm_view_employees" class="form-check-label">عرض الموظفين</label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" id="perm_add_employees" name="permissions" value="add_employees" class="form-check-input permission-checkbox" {% if user.permissions and 'add_employees' in user.permissions %}checked{% endif %}>
                                <label for="perm_add_employees" class="form-check-label">إضافة موظفين</label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" id="perm_edit_employees" name="permissions" value="edit_employees" class="form-check-input permission-checkbox" {% if user.permissions and 'edit_employees' in user.permissions %}checked{% endif %}>
                                <label for="perm_edit_employees" class="form-check-label">تعديل بيانات الموظفين</label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" id="perm_delete_employees" name="permissions" value="delete_employees" class="form-check-input permission-checkbox" {% if user.permissions and 'delete_employees' in user.permissions %}checked{% endif %}>
                                <label for="perm_delete_employees" class="form-check-label">حذف الموظفين</label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" id="perm_view_leaves" name="permissions" value="view_leaves" class="form-check-input permission-checkbox" {% if user.permissions and 'view_leaves' in user.permissions %}checked{% endif %}>
                                <label for="perm_view_leaves" class="form-check-label">عرض الإجازات</label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" id="perm_add_leaves" name="permissions" value="add_leaves" class="form-check-input permission-checkbox" {% if user.permissions and 'add_leaves' in user.permissions %}checked{% endif %}>
                                <label for="perm_add_leaves" class="form-check-label">إضافة إجازات</label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" id="perm_edit_leaves" name="permissions" value="edit_leaves" class="form-check-input permission-checkbox" {% if user.permissions and 'edit_leaves' in user.permissions %}checked{% endif %}>
                                <label for="perm_edit_leaves" class="form-check-label">تعديل الإجازات</label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" id="perm_delete_leaves" name="permissions" value="delete_leaves" class="form-check-input permission-checkbox" {% if user.permissions and 'delete_leaves' in user.permissions %}checked{% endif %}>
                                <label for="perm_delete_leaves" class="form-check-label">حذف الإجازات</label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" id="perm_manage_leave_types" name="permissions" value="manage_leave_types" class="form-check-input permission-checkbox" {% if user.permissions and 'manage_leave_types' in user.permissions %}checked{% endif %}>
                                <label for="perm_manage_leave_types" class="form-check-label">إدارة أنواع الإجازات</label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" id="perm_manage_documents" name="permissions" value="manage_documents" class="form-check-input permission-checkbox" {% if user.permissions and 'manage_documents' in user.permissions %}checked{% endif %}>
                                <label for="perm_manage_documents" class="form-check-label">إدارة المستندات</label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" id="perm_view_documents" name="permissions" value="view_documents" class="form-check-input permission-checkbox" {% if user.permissions and 'view_documents' in user.permissions %}checked{% endif %}>
                                <label for="perm_view_documents" class="form-check-label">عرض المستندات</label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" id="perm_search_leaves" name="permissions" value="search_leaves" class="form-check-input permission-checkbox" {% if user.permissions and 'search_leaves' in user.permissions %}checked{% endif %}>
                                <label for="perm_search_leaves" class="form-check-label">البحث في الإجازات</label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" id="perm_backup_database" name="permissions" value="backup_database" class="form-check-input permission-checkbox" {% if user.permissions and 'backup_database' in user.permissions %}checked{% endif %}>
                                <label for="perm_backup_database" class="form-check-label">النسخ الاحتياطي</label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" id="perm_update_return_status" name="permissions" value="update_return_status" class="form-check-input permission-checkbox" {% if user.permissions and 'update_return_status' in user.permissions %}checked{% endif %}>
                                <label for="perm_update_return_status" class="form-check-label">تحديث حالة العودة</label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" id="perm_manage_users" name="permissions" value="manage_users" class="form-check-input permission-checkbox" {% if user.permissions and 'manage_users' in user.permissions %}checked{% endif %}>
                                <label for="perm_manage_users" class="form-check-label">إدارة المستخدمين</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-1"></i> حفظ التغييرات
                    </button>
                    <a href="{{ url_for('user_management') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const permAll = document.getElementById('perm_all');
        const permissionCheckboxes = document.querySelectorAll('.permission-checkbox');

        function togglePermissions() {
            permissionCheckboxes.forEach(function(checkbox) {
                checkbox.checked = permAll.checked;
                checkbox.disabled = permAll.checked;
            });
        }

        permAll.addEventListener('change', togglePermissions);

        // تشغيل عند تحميل الصفحة
        if (permAll.checked) {
            togglePermissions();
        }
    });
</script>
{% endblock %}
