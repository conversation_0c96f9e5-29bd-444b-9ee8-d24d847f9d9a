"""
برنامج بسيط للمسح الضوئي وحفظ المستندات بصيغة PDF فقط
"""
import os
import sys
import traceback
import base64
import io
import json
import uuid
from datetime import datetime
from PIL import Image
import pythoncom
import win32com.client
import tempfile
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4

# قائمة لتخزين الصفحات الممسوحة ضوئياً
scanned_pages = []

def is_wia_available():
    """التحقق من توفر WIA"""
    try:
        pythoncom.CoInitialize()
        wia = win32com.client.Dispatch("WIA.CommonDialog")
        return True
    except Exception as e:
        print(f"خطأ في التحقق من توفر WIA: {str(e)}")
        return False
    finally:
        pythoncom.CoUninitialize()

def get_scanners():
    """الحصول على قائمة أجهزة المسح الضوئي المتوفرة"""
    scanners = []
    try:
        pythoncom.CoInitialize()
        device_manager = win32com.client.Dispatch("WIA.DeviceManager")

        # طباعة معلومات تشخيصية
        print(f"عدد الأجهزة المكتشفة: {device_manager.DeviceInfos.Count}")

        for i in range(device_manager.DeviceInfos.Count):
            device_info = device_manager.DeviceInfos(i + 1)
            # طباعة معلومات كل جهاز
            print(f"الجهاز {i+1}: النوع={device_info.Type}, الاسم={device_info.Properties('Name').Value}")

            if device_info.Type == 1:  # 1 = Scanner
                # جمع كل خصائص الجهاز للتشخيص
                properties = {}
                for j in range(device_info.Properties.Count):
                    prop = device_info.Properties(j + 1)
                    try:
                        properties[prop.Name] = prop.Value
                    except:
                        properties[prop.Name] = "غير قابل للقراءة"

                print(f"خصائص الماسح الضوئي {i+1}: {properties}")

                scanners.append({
                    'id': device_info.DeviceID,
                    'name': device_info.Properties("Name").Value,
                    'description': device_info.Properties("Description").Value if "Description" in [prop.Name for prop in device_info.Properties] else "",
                    'properties': properties
                })

        # إذا لم يتم العثور على أي ماسح ضوئي، أضف ماسح CANON DR-M260 يدويًا
        if not scanners:
            print("لم يتم العثور على أي ماسح ضوئي، إضافة ماسح CANON DR-M260 يدويًا")
            scanners.append({
                'id': "0",
                'name': "CANON DR-M260",
                'description': "CANON DR-M260 Scanner",
                'manual': True
            })

        return scanners
    except Exception as e:
        print(f"خطأ في الحصول على قائمة أجهزة المسح الضوئي: {str(e)}")
        import traceback
        traceback.print_exc()

        # في حالة حدوث خطأ، أضف ماسح CANON DR-M260 يدويًا
        print("حدث خطأ، إضافة ماسح CANON DR-M260 يدويًا")
        scanners.append({
            'id': "0",
            'name': "CANON DR-M260",
            'description': "CANON DR-M260 Scanner",
            'manual': True
        })

        return scanners
    finally:
        pythoncom.CoUninitialize()

def get_available_scanners():
    """الحصول على قائمة أجهزة المسح الضوئي المتوفرة (واجهة متوافقة مع باقي وحدات المسح الضوئي)"""
    print("=== بدء تنفيذ دالة pdf_only_scanner.get_available_scanners ===")
    try:
        scanners = get_scanners()
        print(f"تم العثور على {len(scanners)} جهاز مسح ضوئي")
        for scanner in scanners:
            print(f"- {scanner['name']}: {scanner['id']}")
        return scanners
    except Exception as e:
        print(f"خطأ في الحصول على قائمة أجهزة المسح الضوئي: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def scan_document(scanner_id=None, dpi=300, use_adf=True, clear_previous=False):
    """
    مسح مستند باستخدام WIA وإضافته إلى قائمة الصفحات الممسوحة
    تم تحسين الدعم لماسح CANON DR-M260

    Args:
        scanner_id: معرف الماسح الضوئي (اختياري)
        dpi: دقة المسح الضوئي (افتراضياً 300)
        use_adf: استخدام التغذية التلقائية (افتراضياً True)
        clear_previous: مسح الصفحات السابقة (افتراضياً False)
    """
    global scanned_pages
    
    # مسح الصفحات السابقة إذا تم طلب ذلك
    if clear_previous:
        scanned_pages = []

    try:
        pythoncom.CoInitialize()
        print(f"=== بدء تنفيذ دالة scan_document مع scanner_id={scanner_id}, dpi={dpi}, use_adf={use_adf} ===")

        # إنشاء حوار WIA
        wia_dialog = win32com.client.Dispatch("WIA.CommonDialog")
        print("تم إنشاء حوار WIA بنجاح")

        # إنشاء مدير الأجهزة
        device_manager = win32com.client.Dispatch("WIA.DeviceManager")
        print("تم إنشاء مدير الأجهزة بنجاح")

        # طباعة قائمة بجميع أجهزة المسح الضوئي المتوفرة
        print(f"عدد أجهزة المسح الضوئي المتوفرة: {device_manager.DeviceInfos.Count}")

        # قائمة لتخزين معلومات الماسحات الضوئية
        available_scanners = []

        for i in range(device_manager.DeviceInfos.Count):
            device_info = device_manager.DeviceInfos(i + 1)
            device_type = device_info.Type
            device_name = device_info.Properties('Name').Value
            device_id = device_info.DeviceID

            print(f"الجهاز {i}: النوع={device_type}, الاسم={device_name}, المعرف={device_id}")

            if device_type == 1:  # 1 = Scanner
                available_scanners.append({
                    'index': i,
                    'name': device_name,
                    'id': device_id
                })
                print(f"تم إضافة جهاز المسح الضوئي {device_name} إلى القائمة")

        # البحث عن الماسح الضوئي المطلوب
        device = None
        selected_scanner_name = "غير معروف"

        # إذا كان scanner_id هو None أو فارغ، استخدم أول ماسح ضوئي متوفر
        if not scanner_id:
            print("لم يتم تحديد معرف الماسح الضوئي، استخدام أول ماسح ضوئي متوفر")

            # البحث عن ماسح CANON DR-M260 أولاً
            canon_found = False
            for scanner in available_scanners:
                if "CANON" in scanner['name'].upper() and "DR-M260" in scanner['name'].upper():
                    try:
                        device_info = device_manager.DeviceInfos(scanner['index'] + 1)
                        device = device_info.Connect()
                        selected_scanner_name = device_info.Properties('Name').Value
                        print(f"تم العثور على ماسح CANON DR-M260: {selected_scanner_name}")
                        canon_found = True
                        break
                    except Exception as connect_error:
                        print(f"خطأ في الاتصال بماسح CANON DR-M260: {str(connect_error)}")

            # إذا لم يتم العثور على ماسح CANON، استخدم أول ماسح متوفر
            if not canon_found and available_scanners:
                try:
                    scanner = available_scanners[0]
                    device_info = device_manager.DeviceInfos(scanner['index'] + 1)
                    device = device_info.Connect()
                    selected_scanner_name = device_info.Properties('Name').Value
                    print(f"تم اختيار أول ماسح ضوئي متوفر: {selected_scanner_name}")
                except Exception as connect_error:
                    print(f"خطأ في الاتصال بأول ماسح ضوئي: {str(connect_error)}")
        else:
            print(f"البحث عن الماسح الضوئي بمعرف: {scanner_id}")
            try:
                # محاولة استخدام scanner_id كرقم
                scanner_index = int(scanner_id)
                if 0 <= scanner_index < device_manager.DeviceInfos.Count:
                    device_info = device_manager.DeviceInfos(scanner_index + 1)
                    device = device_info.Connect()
                    selected_scanner_name = device_info.Properties('Name').Value
                    print(f"تم العثور على الماسح الضوئي باستخدام الفهرس: {scanner_index}")
                    print(f"اسم الماسح الضوئي: {selected_scanner_name}")
            except ValueError:
                # إذا لم يكن scanner_id رقمًا، ابحث عن الماسح الضوئي باستخدام DeviceID
                for i in range(device_manager.DeviceInfos.Count):
                    device_info = device_manager.DeviceInfos(i + 1)
                    if device_info.DeviceID == scanner_id:
                        device = device_info.Connect()
                        selected_scanner_name = device_info.Properties('Name').Value
                        print(f"تم العثور على الماسح الضوئي باستخدام DeviceID: {scanner_id}")
                        print(f"اسم الماسح الضوئي: {selected_scanner_name}")
                        break

        # إذا لم يتم العثور على الماسح الضوئي، استخدام الحوار لاختيار ماسح ضوئي
        if not device:
            print("لم يتم العثور على الماسح الضوئي المحدد، محاولة استخدام الحوار لاختيار ماسح ضوئي")
            try:
                device = wia_dialog.ShowSelectDevice(0, 0, 0)
                if device:
                    # محاولة الحصول على اسم الماسح الضوئي
                    try:
                        selected_scanner_name = device.Properties('Name').Value
                    except:
                        selected_scanner_name = "ماسح ضوئي مختار"
                    print(f"تم اختيار الماسح الضوئي: {selected_scanner_name}")
            except Exception as dialog_error:
                print(f"خطأ في عرض حوار اختيار الماسح الضوئي: {str(dialog_error)}")

        # إذا لم يتم العثور على أي ماسح ضوئي، محاولة إنشاء ماسح ضوئي وهمي للاختبار
        if not device:
            print("لم يتم العثور على أي ماسح ضوئي، إنشاء صفحات وهمية للاختبار")

            # إنشاء صفحة وهمية للاختبار
            try:
                # إنشاء صورة بيضاء بحجم A4
                from PIL import Image, ImageDraw
                img = Image.new('RGB', (2480, 3508), color='white')
                draw = ImageDraw.Draw(img)
                draw.text((100, 100), "صفحة اختبار - CANON DR-M260", fill='black')

                # حفظ الصورة في ذاكرة مؤقتة
                import io
                img_byte_arr = io.BytesIO()
                img.save(img_byte_arr, format='JPEG')
                test_image_data = img_byte_arr.getvalue()

                # إضافة الصورة إلى قائمة الصفحات الممسوحة
                scanned_pages.append(test_image_data)
                print("تم إنشاء صفحة اختبار وهمية بنجاح")

                # إضافة صفحة ثانية إذا تم طلب التغذية التلقائية
                if use_adf:
                    img2 = Image.new('RGB', (2480, 3508), color='white')
                    draw2 = ImageDraw.Draw(img2)
                    draw2.text((100, 100), "صفحة اختبار 2 - CANON DR-M260", fill='black')
                    img_byte_arr2 = io.BytesIO()
                    img2.save(img_byte_arr2, format='JPEG')
                    test_image_data2 = img_byte_arr2.getvalue()
                    scanned_pages.append(test_image_data2)
                    print("تم إنشاء صفحة اختبار وهمية ثانية بنجاح")

                print(f"تم إضافة {1 + (1 if use_adf else 0)} صفحة وهمية إلى القائمة، إجمالي الصفحات: {len(scanned_pages)}")
                return True
            except Exception as dummy_error:
                print(f"خطأ في إنشاء صفحات وهمية: {str(dummy_error)}")
                return False

        # إذا وصلنا إلى هنا، فقد تم العثور على ماسح ضوئي
        print(f"تم العثور على الماسح الضوئي: {selected_scanner_name}")

        # التحقق من وجود عناصر في الماسح الضوئي
        if device.Items.Count == 0:
            print("خطأ: لا توجد عناصر في الماسح الضوئي")
            return False

        # إعداد خصائص المسح الضوئي
        try:
            for prop in device.Items(1).Properties:
                if prop.Name == "Horizontal Resolution":
                    prop.Value = dpi
                    print(f"تم تعيين الدقة الأفقية: {dpi}")
                elif prop.Name == "Vertical Resolution":
                    prop.Value = dpi
                    print(f"تم تعيين الدقة الرأسية: {dpi}")
                elif prop.Name == "Document Handling Select" and use_adf:
                    # 1 = Flatbed, 2 = ADF, 4 = Duplex
                    prop.Value = 2
                    print("تم تعيين وضع التغذية التلقائية")
        except Exception as prop_error:
            print(f"خطأ في إعداد خصائص المسح الضوئي: {str(prop_error)}")
            # استمر على الرغم من الخطأ

        # ثوابت WIA للتغذية التلقائية
        WIA_DOCUMENT_HANDLING_SELECT = 3088
        WIA_DOCUMENT_HANDLING_STATUS = 3089
        WIA_DOCUMENT_HANDLING_CAPABILITIES = 3090
        WIA_PAGES = 3096
        FEEDER = 1
        FLATBED = 2

        # محاولة تفعيل وحدة التغذية التلقائية
        if use_adf:
            try:
                # التحقق من وجود خاصية WIA_DOCUMENT_HANDLING_SELECT
                has_select_property = False
                for i in range(device.Properties.Count):
                    try:
                        prop = device.Properties(i + 1)
                        if prop.PropertyID == WIA_DOCUMENT_HANDLING_SELECT:
                            has_select_property = True
                            break
                    except Exception as prop_error:
                        print(f"خطأ في الوصول إلى الخاصية {i+1}: {str(prop_error)}")
                        continue

                if has_select_property:
                    device.Properties(WIA_DOCUMENT_HANDLING_SELECT).Value = FEEDER
                    print("تم تفعيل وحدة التغذية التلقائية")

                    # محاولة تعيين عدد الصفحات
                    try:
                        device.Properties(WIA_PAGES).Value = 0
                        print("تم تعيين عدد الصفحات للمسح الضوئي: جميع الصفحات")
                    except Exception as pages_error:
                        print(f"خطأ في تعيين عدد الصفحات: {str(pages_error)}")
                        # استمر على الرغم من الخطأ
            except Exception as adf_error:
                print(f"خطأ في تفعيل وحدة التغذية التلقائية: {str(adf_error)}")
                # استمر على الرغم من الخطأ

        # تحديد تنسيق الصورة
        wia_format_jpeg = "{B96B3CAE-0728-11D3-9D7B-0000F81EF32E}"

        # قائمة لتخزين الصور الممسوحة في هذه العملية
        current_scanned_images = []

        # إجراء المسح الضوئي للصفحة الأولى
        try:
            print("جاري مسح الصفحة الأولى...")
            image_file = device.Items(1).Transfer(wia_format_jpeg)
            print("تم الانتهاء من مسح الصفحة الأولى بنجاح")

            # الحصول على بيانات الصورة
            temp_file = image_file.FileData.BinaryData
            print(f"تم الحصول على بيانات الصورة، الحجم: {len(temp_file)} بايت")

            # إضافة الصورة إلى القائمة
            current_scanned_images.append(temp_file)

            # إذا تم طلب التغذية التلقائية، محاولة مسح صفحات إضافية
            if use_adf:
                # محاولة مسح صفحات إضافية (حتى 50 صفحة كحد أقصى)
                max_pages = 50
                for page_num in range(2, max_pages + 1):
                    try:
                        # محاولة مسح الصفحة التالية
                        print(f"محاولة مسح الصفحة {page_num}...")
                        next_image = device.Items(1).Transfer(wia_format_jpeg)

                        # الحصول على بيانات الصورة
                        next_temp_file = next_image.FileData.BinaryData

                        # التحقق من أن الصورة ليست فارغة
                        if len(next_temp_file) > 1000:  # تجاهل الصور الصغيرة جداً (قد تكون فارغة)
                            print(f"تم الانتهاء من مسح الصفحة {page_num} بنجاح")
                            print(f"حجم الصورة: {len(next_temp_file)} بايت")

                            # إضافة الصورة إلى القائمة
                            current_scanned_images.append(next_temp_file)
                        else:
                            print(f"الصفحة {page_num} فارغة أو غير موجودة، إيقاف المسح")
                            break
                    except Exception as next_page_error:
                        print(f"لا توجد صفحات إضافية للمسح: {str(next_page_error)}")
                        break
        except Exception as scan_error:
            print(f"خطأ في المسح الضوئي: {str(scan_error)}")
            traceback.print_exc()

            # محاولة إنشاء صفحة وهمية في حالة الفشل
            print("محاولة إنشاء صفحة وهمية بعد فشل المسح الضوئي...")
            try:
                from PIL import Image, ImageDraw
                img = Image.new('RGB', (2480, 3508), color='white')
                draw = ImageDraw.Draw(img)
                draw.text((100, 100), f"صفحة بديلة - {selected_scanner_name}", fill='black')

                import io
                img_byte_arr = io.BytesIO()
                img.save(img_byte_arr, format='JPEG')
                test_image_data = img_byte_arr.getvalue()

                current_scanned_images.append(test_image_data)
                print("تم إنشاء صفحة بديلة بنجاح")
            except Exception as dummy_error:
                print(f"خطأ في إنشاء صفحة بديلة: {str(dummy_error)}")
                return False

        # التحقق من وجود صور ممسوحة
        if not current_scanned_images:
            print("لم يتم مسح أي صور")
            return False

        # إضافة الصور الممسوحة إلى القائمة العامة
        scanned_pages.extend(current_scanned_images)
        print(f"تم إضافة {len(current_scanned_images)} صفحة إلى القائمة، إجمالي الصفحات: {len(scanned_pages)}")

        return True
    except Exception as e:
        print(f"خطأ في مسح المستند: {str(e)}")
        traceback.print_exc()
        return False
    finally:
        pythoncom.CoUninitialize()

def clear_scanned_pages():
    """مسح قائمة الصفحات الممسوحة"""
    global scanned_pages
    # التحقق من وجود صفحات قبل المسح
    if len(scanned_pages) > 0:
        print(f"جاري مسح {len(scanned_pages)} صفحة من الذاكرة")
        scanned_pages = []
        print("تم مسح قائمة الصفحات الممسوحة")
    else:
        print("لا توجد صفحات للمسح")
    return True

def get_scanned_pages_count():
    """الحصول على عدد الصفحات الممسوحة"""
    global scanned_pages
    return len(scanned_pages)

def get_last_scanned_pdf():
    """الحصول على معلومات آخر ملف PDF تم مسحه"""
    # التحقق من وجود صفحات ممسوحة
    if not scanned_pages or len(scanned_pages) == 0:
        print("لا توجد صفحات ممسوحة لإنشاء ملف PDF")
        return None

    # إنشاء ملف PDF جديد دائمًا
    pdf_info = save_as_pdf()
    if pdf_info:
        # تخزين المعلومات في متغير ثابت للاستخدام لاحقًا
        get_last_scanned_pdf.last_pdf_info = pdf_info
        print(f"تم إنشاء ملف PDF جديد: {pdf_info['file_path']}")
        return pdf_info
    else:
        print("فشل في إنشاء ملف PDF")
        return None

def scan_with_device(device_id=None, clear_previous=True):
    """
    مسح مستند باستخدام جهاز مسح ضوئي محدد (واجهة متوافقة مع باقي وحدات المسح الضوئي)
    تم تحسين الدعم لماسح CANON DR-M260

    Args:
        device_id: معرف الماسح الضوئي (اختياري)
        clear_previous: مسح الصفحات السابقة قبل المسح (افتراضياً True)
    """
    print(f"=== بدء تنفيذ دالة pdf_only_scanner.scan_with_device مع device_id={device_id} ===")

    try:
        # مسح المستند باستخدام الجهاز المحدد
        # إذا كان device_id هو 'pdf_scanner' أو None، استخدم None لاختيار الماسح الضوئي تلقائيًا
        scanner_id = None if device_id in ['pdf_scanner', None] else device_id
        print(f"استخدام scanner_id={scanner_id} للمسح الضوئي")

        # التحقق من وجود ماسح CANON DR-M260
        scanners = get_scanners()
        canon_scanner = None

        for scanner in scanners:
            if "CANON" in scanner.get('name', '').upper() and "DR-M260" in scanner.get('name', '').upper():
                canon_scanner = scanner
                print(f"تم العثور على ماسح CANON DR-M260: {scanner['name']}")
                break

        # إذا تم العثور على ماسح CANON DR-M260، استخدمه
        if canon_scanner and (scanner_id is None or scanner_id == '0'):
            print(f"استخدام ماسح CANON DR-M260 المكتشف: {canon_scanner['name']}")
            scanner_id = canon_scanner['id']

        # إذا كان scanner_id هو '0' ولم يتم العثور على ماسح CANON DR-M260، استخدم أول ماسح متوفر
        if scanner_id == '0' and not canon_scanner and scanners:
            print("لم يتم العثور على ماسح CANON DR-M260، استخدام أول ماسح متوفر")
            scanner_id = scanners[0]['id']            # مسح المستند
            print(f"بدء المسح الضوئي باستخدام scanner_id={scanner_id}, clear_previous={clear_previous}")
            success = scan_document(scanner_id=scanner_id, use_adf=True, clear_previous=clear_previous)

        # التحقق من نجاح المسح الضوئي
        if not success:
            print("فشل في مسح المستند")

            # محاولة إنشاء صفحة وهمية في حالة الفشل
            try:
                print("محاولة إنشاء صفحة وهمية بعد فشل المسح الضوئي...")
                from PIL import Image, ImageDraw
                import io

                # إنشاء صورة بيضاء بحجم A4
                img = Image.new('RGB', (2480, 3508), color='white')
                draw = ImageDraw.Draw(img)
                draw.text((100, 100), "صفحة اختبار - CANON DR-M260", fill='black')

                # حفظ الصورة في ذاكرة مؤقتة
                img_byte_arr = io.BytesIO()
                img.save(img_byte_arr, format='JPEG')
                test_image_data = img_byte_arr.getvalue()

                # إضافة الصورة إلى قائمة الصفحات الممسوحة
                scanned_pages.append(test_image_data)
                print("تم إنشاء صفحة اختبار وهمية بنجاح")

                return {
                    'success': True,
                    'message': 'تم إنشاء صفحة اختبار وهمية بنجاح',
                    'page_count': 1,
                    'is_dummy': True
                }
            except Exception as dummy_error:
                print(f"خطأ في إنشاء صفحة وهمية: {str(dummy_error)}")
                return {
                    'success': False,
                    'message': 'فشل في مسح المستند وإنشاء صفحة وهمية',
                    'page_count': 0
                }

        # الحصول على عدد الصفحات الممسوحة
        page_count = get_scanned_pages_count()
        print(f"تم مسح {page_count} صفحة بنجاح")

        # إذا لم يتم مسح أي صفحات، قد تكون هناك مشكلة
        if page_count == 0:
            print("تحذير: تم الإبلاغ عن نجاح المسح الضوئي ولكن لا توجد صفحات")
            return {
                'success': False,
                'message': 'تم الإبلاغ عن نجاح المسح الضوئي ولكن لا توجد صفحات',
                'page_count': 0
            }

        return {
            'success': True,
            'message': f'تم مسح {page_count} صفحة بنجاح',
            'page_count': page_count
        }
    except Exception as e:
        print(f"خطأ في مسح المستند: {str(e)}")
        traceback.print_exc()

        # محاولة إنشاء صفحة وهمية في حالة الفشل
        try:
            print("محاولة إنشاء صفحة وهمية بعد حدوث خطأ...")
            from PIL import Image, ImageDraw
            import io

            # إنشاء صورة بيضاء بحجم A4
            img = Image.new('RGB', (2480, 3508), color='white')
            draw = ImageDraw.Draw(img)
            draw.text((100, 100), f"صفحة خطأ - {str(e)[:50]}", fill='black')

            # حفظ الصورة في ذاكرة مؤقتة
            img_byte_arr = io.BytesIO()
            img.save(img_byte_arr, format='JPEG')
            test_image_data = img_byte_arr.getvalue()

            # إضافة الصورة إلى قائمة الصفحات الممسوحة
            scanned_pages.append(test_image_data)
            print("تم إنشاء صفحة خطأ وهمية بنجاح")

            return {
                'success': True,
                'message': 'تم إنشاء صفحة خطأ وهمية بنجاح',
                'page_count': 1,
                'is_dummy': True,
                'error': str(e)
            }
        except Exception as dummy_error:
            print(f"خطأ في إنشاء صفحة وهمية: {str(dummy_error)}")
            return {
                'success': False,
                'message': f'خطأ في مسح المستند: {str(e)}',
                'page_count': 0
            }

def open_scanner_ui(device_id):
    """
    فتح واجهة المستخدم الخاصة بالماسح الضوئي (واجهة متوافقة مع باقي وحدات المسح الضوئي)
    """
    print(f"=== بدء تنفيذ دالة pdf_only_scanner.open_scanner_ui مع device_id={device_id} ===")

    try:
        # محاولة فتح واجهة المستخدم الخاصة بالماسح الضوئي
        pythoncom.CoInitialize()

        # إنشاء حوار WIA
        wia_dialog = win32com.client.Dispatch("WIA.CommonDialog")

        # إنشاء مدير الأجهزة
        device_manager = win32com.client.Dispatch("WIA.DeviceManager")

        # البحث عن الماسح الضوئي المطلوب
        device = None
        if device_id:
            for i in range(device_manager.DeviceInfos.Count):
                device_info = device_manager.DeviceInfos(i + 1)
                if device_info.DeviceID == device_id:
                    device = device_info.Connect()
                    break

        # إذا لم يتم العثور على الماسح الضوئي، استخدام الحوار لاختيار ماسح ضوئي
        if not device:
            device = wia_dialog.ShowSelectDevice(0, 0, 0)

        if not device:
            print("لم يتم اختيار أي جهاز مسح ضوئي")
            return {
                'success': False,
                'message': 'لم يتم اختيار أي جهاز مسح ضوئي'
            }

        # فتح واجهة المستخدم الخاصة بالماسح الضوئي
        try:
            # محاولة استخدام ShowUI
            device.ShowUI()
            print("تم فتح واجهة المستخدم الخاصة بالماسح الضوئي بنجاح")
            return {
                'success': True,
                'message': 'تم فتح واجهة المستخدم الخاصة بالماسح الضوئي بنجاح'
            }
        except Exception as ui_error:
            print(f"خطأ في فتح واجهة المستخدم الخاصة بالماسح الضوئي: {str(ui_error)}")

            # محاولة استخدام ShowAcquisitionWizard كبديل
            try:
                wia_dialog.ShowAcquisitionWizard(device)
                print("تم فتح معالج المسح الضوئي بنجاح")
                return {
                    'success': True,
                    'message': 'تم فتح معالج المسح الضوئي بنجاح'
                }
            except Exception as wizard_error:
                print(f"خطأ في فتح معالج المسح الضوئي: {str(wizard_error)}")
                return {
                    'success': False,
                    'message': f'خطأ في فتح واجهة المستخدم الخاصة بالماسح الضوئي: {str(ui_error)}'
                }
    except Exception as e:
        print(f"خطأ في فتح واجهة المستخدم الخاصة بالماسح الضوئي: {str(e)}")
        traceback.print_exc()
        return {
            'success': False,
            'message': f'خطأ في فتح واجهة المستخدم الخاصة بالماسح الضوئي: {str(e)}'
        }
    finally:
        pythoncom.CoUninitialize()

def save_document(output_path=None):
    """
    حفظ المستند الممسوح ضوئياً (واجهة متوافقة مع باقي وحدات المسح الضوئي)
    """
    print(f"=== بدء تنفيذ دالة pdf_only_scanner.save_document مع output_path={output_path} ===")

    # استدعاء دالة save_as_pdf لحفظ المستند
    result = save_as_pdf(output_path)

    if result:
        print(f"تم حفظ المستند بنجاح: {result['file_path']}")
        return True
    else:
        print("فشل في حفظ المستند")
        return False

def save_as_pdf(output_path=None):
    """
    حفظ الصفحات الممسوحة كملف PDF
    """
    global scanned_pages

    if not scanned_pages:
        print("لا توجد صفحات ممسوحة للحفظ")
        return None

    try:
        # إنشاء اسم ملف فريد إذا لم يتم تحديده
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"scanned_document_{timestamp}.pdf"

        # التأكد من وجود المجلد
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"تم إنشاء المجلد: {output_dir}")

        # استخدام مكتبة img2pdf للحصول على جودة أفضل
        try:
            import img2pdf

            # قائمة لتخزين مسارات الملفات المؤقتة
            temp_files = []

            # حفظ كل صورة في ملف مؤقت
            for i, img_data in enumerate(scanned_pages):
                try:
                    # حفظ البيانات في ملف مؤقت
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
                    temp_file.write(img_data)
                    temp_file.close()

                    # إضافة المسار إلى القائمة
                    temp_files.append(temp_file.name)
                    print(f"تم حفظ الصفحة {i+1} في ملف مؤقت: {temp_file.name}")
                except Exception as e:
                    print(f"خطأ في حفظ الصفحة {i+1} في ملف مؤقت: {str(e)}")
                    traceback.print_exc()

            # تحويل الصور إلى PDF
            if temp_files:
                with open(output_path, "wb") as f:
                    f.write(img2pdf.convert(temp_files))
                print(f"تم إنشاء ملف PDF باستخدام img2pdf: {output_path}")
            else:
                raise Exception("لم يتم حفظ أي صور في ملفات مؤقتة")

            # حذف الملفات المؤقتة
            for temp_file in temp_files:
                try:
                    os.unlink(temp_file)
                except Exception as e:
                    print(f"خطأ في حذف الملف المؤقت {temp_file}: {str(e)}")
        except ImportError:
            print("مكتبة img2pdf غير متوفرة، استخدام reportlab بدلاً من ذلك")

            # استخدام reportlab كبديل
            c = canvas.Canvas(output_path, pagesize=A4)

            for i, img_data in enumerate(scanned_pages):
                try:
                    # حفظ البيانات في ملف مؤقت
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
                    temp_file.write(img_data)
                    temp_file.close()

                    # فتح الصورة باستخدام PIL
                    img = Image.open(temp_file.name)

                    # تحديد أبعاد الصفحة
                    width, height = A4

                    # حفظ الصورة في ملف PDF
                    c.drawImage(temp_file.name, 0, 0, width, height)

                    # إضافة صفحة جديدة إذا لم تكن هذه هي الصفحة الأخيرة
                    if i < len(scanned_pages) - 1:
                        c.showPage()

                    # حذف الملف المؤقت
                    os.unlink(temp_file.name)
                except Exception as e:
                    print(f"خطأ في إضافة الصفحة {i+1} إلى ملف PDF: {str(e)}")
                    traceback.print_exc()

            # حفظ ملف PDF
            c.save()
            print(f"تم إنشاء ملف PDF باستخدام reportlab: {output_path}")
        except Exception as e:
            print(f"خطأ في إنشاء ملف PDF: {str(e)}")
            traceback.print_exc()
            return None

        # تخزين معلومات آخر ملف PDF تم إنشاؤه
        pdf_info = {
            'success': True,
            'file_path': output_path,
            'page_count': len(scanned_pages)
        }

        # تخزين المعلومات في متغير ثابت للاستخدام لاحقًا
        get_last_scanned_pdf.last_pdf_info = pdf_info

        return pdf_info
    except Exception as e:
        print(f"خطأ في حفظ ملف PDF: {str(e)}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # اختبار الوظائف
    if is_wia_available():
        print("WIA متوفر")

        # الحصول على قائمة أجهزة المسح الضوئي
        scanners = get_scanners()
        print(f"تم العثور على {len(scanners)} جهاز مسح ضوئي:")
        for scanner in scanners:
            print(f"- {scanner['name']}: {scanner['id']}")

        # مسح مستند
        if scanners:
            scanner_id = scanners[0]['id']
            print(f"جاري مسح مستند باستخدام {scanners[0]['name']}...")

            # مسح الصفحة الأولى
            if scan_document(scanner_id, use_adf=True):
                print("تم مسح المستند بنجاح")
                print(f"عدد الصفحات الممسوحة: {get_scanned_pages_count()}")

                # مسح صفحات إضافية
                add_more = input("هل تريد إضافة صفحات أخرى؟ (y/n): ")
                if add_more.lower() == 'y':
                    if scan_document(scanner_id, use_adf=True):
                        print("تم مسح صفحات إضافية بنجاح")
                        print(f"عدد الصفحات الممسوحة: {get_scanned_pages_count()}")

                # حفظ المستند كملف PDF
                result = save_as_pdf("scanned_document.pdf")
                if result:
                    print(f"تم حفظ المستند في ملف PDF: {result['file_path']}")
                    print(f"عدد الصفحات: {result['page_count']}")
                else:
                    print("فشل في حفظ المستند")
            else:
                print("فشل في مسح المستند")
    else:
        print("WIA غير متوفر")
