{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header bg-info text-white">
            <h2 class="mb-0">
                <i class="fas fa-key me-2"></i> تغيير كلمة المرور
            </h2>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> تغيير كلمة المرور للمستخدم: <strong>{{ user.full_name }}</strong>
            </div>

            <form method="POST">
                <div class="mb-3">
                    <label for="new_password" class="form-label">كلمة المرور الجديدة:</label>
                    <input type="password" id="new_password" name="new_password" class="form-control" required>
                </div>

                <div class="mb-3">
                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور:</label>
                    <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> تغيير كلمة المرور
                    </button>
                    <a href="{{ url_for('user_management') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const newPasswordInput = document.getElementById('new_password');
        const confirmPasswordInput = document.getElementById('confirm_password');
        const form = document.querySelector('form');

        form.addEventListener('submit', function(event) {
            if (newPasswordInput.value !== confirmPasswordInput.value) {
                event.preventDefault();
                alert('كلمة المرور وتأكيدها غير متطابقين');
            }
        });
    });
</script>
{% endblock %}
