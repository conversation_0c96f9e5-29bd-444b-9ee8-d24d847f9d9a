"""
اختبار وحدة المسح الضوئي المحسنة
"""
import os
import sys
import traceback
from datetime import datetime

# استيراد وحدة المسح الضوئي المحسنة
import improved_scanner

def test_scanner():
    """
    اختبار وظائف المسح الضوئي المحسنة
    """
    print("=== بدء اختبار وحدة المسح الضوئي المحسنة ===")
    
    # الحصول على قائمة أجهزة المسح الضوئي
    scanners = improved_scanner.get_available_scanners()
    print(f"تم العثور على {len(scanners)} جهاز مسح ضوئي:")
    for scanner in scanners:
        print(f"- {scanner['name']}: {scanner['id']} ({scanner.get('source', 'unknown')})")
    
    # إذا لم يتم العثور على أي ماسح ضوئي، إنهاء الاختبار
    if not scanners:
        print("لم يتم العثور على أي ماسح ضوئي، إنهاء الاختبار")
        return False
    
    # اختيار أول ماسح ضوئي
    scanner_id = scanners[0]['id']
    scanner_name = scanners[0]['name']
    print(f"استخدام الماسح الضوئي: {scanner_name} (ID: {scanner_id})")
    
    # مسح المستند
    print("جاري مسح المستند...")
    success = improved_scanner.scan_document(scanner_id=scanner_id, dpi=300, use_adf=True)
    
    # التحقق من نجاح المسح الضوئي
    if not success:
        print("فشل في مسح المستند")
        return False
    
    # الحصول على عدد الصفحات الممسوحة
    page_count = improved_scanner.get_scanned_pages_count()
    print(f"تم مسح {page_count} صفحة بنجاح")
    
    # حفظ المستند كملف PDF
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"test_scan_{timestamp}.pdf"
    
    print(f"جاري حفظ المستند في ملف PDF: {output_path}")
    result = improved_scanner.save_as_pdf(output_path)
    
    # التحقق من نجاح حفظ المستند
    if not result:
        print("فشل في حفظ المستند")
        return False
    
    print(f"تم حفظ المستند بنجاح: {result['file_path']}")
    print(f"عدد الصفحات: {result['page_count']}")
    
    # مسح قائمة الصفحات الممسوحة
    improved_scanner.clear_scanned_pages()
    print("تم مسح قائمة الصفحات الممسوحة")
    
    print("=== انتهاء اختبار وحدة المسح الضوئي المحسنة بنجاح ===")
    return True

if __name__ == "__main__":
    try:
        test_scanner()
    except Exception as e:
        print(f"خطأ في اختبار وحدة المسح الضوئي المحسنة: {str(e)}")
        traceback.print_exc()
