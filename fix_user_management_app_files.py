"""
سكريبت لإصلاح أزرار الحذف في واجهة إدارة المستخدمين في مجلد app_files
"""
import os
import shutil

def backup_template(template_path):
    """
    عمل نسخة احتياطية من القالب
    """
    if os.path.exists(template_path):
        backup_dir = 'templates_backup'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        backup_path = os.path.join(backup_dir, os.path.basename(template_path))
        shutil.copy2(template_path, backup_path)
        print(f"تم عمل نسخة احتياطية من {template_path} إلى {backup_path}")
        return True
    else:
        print(f"القالب {template_path} غير موجود")
        return False

def fix_user_management_template():
    """
    إصلاح قالب إدارة المستخدمين في مجلد app_files
    """
    template_path = 'app_files/templates/user_management.html'
    
    # عمل نسخة احتياطية من القالب
    if not backup_template(template_path):
        return False
    
    # قراءة محتوى القالب
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن جزء الإجراءات في القالب
    actions_start = content.find('<td>\n                                    <div class="btn-group">')
    if actions_start == -1:
        print("لم يتم العثور على جزء الإجراءات في القالب")
        return False
    
    # البحث عن نهاية جزء الإجراءات
    actions_end = content.find('</div>\n                                </td>', actions_start)
    if actions_end == -1:
        print("لم يتم العثور على نهاية جزء الإجراءات في القالب")
        return False
    
    # استخراج جزء الإجراءات
    actions_content = content[actions_start:actions_end + 6]
    
    # التحقق من وجود زر الحذف
    if 'delete_user' in actions_content and 'حذف' in actions_content:
        print("زر الحذف موجود بالفعل في القالب")
        return True
    
    # إضافة زر الحذف
    new_actions_content = actions_content.replace(
        '<a href="{{ url_for(\'set_permissions\', user_id=user.id) }}" class="btn btn-sm btn-info">\n                                            <i class="fas fa-key"></i> الصلاحيات\n                                        </a>',
        '<a href="{{ url_for(\'set_permissions\', user_id=user.id) }}" class="btn btn-sm btn-info">\n                                            <i class="fas fa-key"></i> الصلاحيات\n                                        </a>\n                                        {% if user.id != session.get(\'employee_id\') %}\n                                            <a href="{{ url_for(\'delete_user\', user_id=user.id) }}" class="btn btn-sm btn-danger" onclick="return confirm(\'هل أنت متأكد من حذف هذا المستخدم؟\')">\n                                                <i class="fas fa-trash"></i> حذف\n                                            </a>\n                                        {% endif %}'
    )
    
    # استبدال جزء الإجراءات في المحتوى الكامل
    new_content = content.replace(actions_content, new_actions_content)
    
    # حفظ التغييرات
    with open(template_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("تم إضافة زر الحذف إلى قالب إدارة المستخدمين")
    return True

def main():
    """
    الدالة الرئيسية
    """
    print("=== بدء إصلاح أزرار الحذف في واجهة إدارة المستخدمين (مجلد app_files) ===")
    
    # إصلاح قالب إدارة المستخدمين
    if fix_user_management_template():
        print("تم إصلاح قالب إدارة المستخدمين بنجاح")
    else:
        print("فشل في إصلاح قالب إدارة المستخدمين")
    
    print("=== انتهاء إصلاح أزرار الحذف في واجهة إدارة المستخدمين (مجلد app_files) ===")

if __name__ == "__main__":
    main()
