<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ document_name }}</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
            background-color: #f0f0f0;
        }

        .toolbar {
            background-color: #0d47a1;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 100;
        }

        .toolbar h3 {
            margin: 0;
            font-size: 18px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 70%;
        }

        .toolbar-actions {
            display: flex;
            gap: 10px;
        }

        .toolbar a {
            color: white;
            text-decoration: none;
            padding: 6px 12px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .toolbar a:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .pdf-container {
            flex: 1;
            height: calc(100vh - 50px);
            overflow: hidden;
        }

        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* زر الحذف */
        .delete-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background-color: #d32f2f;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .delete-btn:hover {
            background-color: #b71c1c;
        }

        /* زر الطباعة */
        .print-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #0d47a1;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .print-btn:hover {
            background-color: #0a3880;
        }

        @media print {
            .toolbar, .delete-btn, .print-btn {
                display: none !important;
            }

            body, html {
                margin: 0 !important;
                padding: 0 !important;
                height: 100%;
                background-color: white;
            }

            .pdf-container {
                height: 100%;
                margin: 0;
                padding: 0;
            }

            iframe {
                width: 100%;
                height: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="toolbar">
        <h3>{{ document_name }}</h3>
        <div class="toolbar-actions">
            <a href="javascript:window.history.back()">رجوع</a>
        </div>
    </div>

    <div class="pdf-container">
        <iframe src="{{ document_url }}" frameborder="0"></iframe>
    </div>

    <!-- زر الطباعة -->
    <button class="print-btn" onclick="printPDF()" title="طباعة">
        <i class="fas fa-print"></i>
    </button>

    <!-- زر الحذف -->
    {% if document_id %}
    <a href="{{ url_for('delete_document', document_id=document_id) }}" class="delete-btn" onclick="return confirm('هل أنت متأكد من حذف هذا المستند؟');" title="حذف المستند">
        <i class="fas fa-trash-alt"></i>
    </a>
    {% endif %}

    <script>
        function printPDF() {
            // طباعة الصفحة مباشرة
            window.print();
        }

        // التأكد من تحميل الصفحة بالكامل
        window.onload = function() {
            // إضافة تأخير بسيط للتأكد من تحميل PDF
            setTimeout(function() {
                document.querySelector('iframe').focus();
            }, 1000);
        };
    </script>
</body>
</html>
