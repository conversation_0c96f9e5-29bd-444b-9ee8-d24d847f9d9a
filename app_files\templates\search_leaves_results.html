{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2 class="mb-0">نتائج البحث عن الإجازات</h2>
            <div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
                </a>
            </div>
        </div>
        <div class="card-body">
            <form action="{{ url_for('search_leaves') }}" method="post" class="mb-4">
                <div class="row">
                    <div class="col-md-5">
                        <div class="input-group">
                            <input type="text" name="query" class="form-control" placeholder="البحث عن موظف..." value="{{ query }}">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <select name="leave_type_id" class="form-select">
                            <option value="">-- جميع أنواع الإجازات --</option>
                            {% for leave_type in leave_types %}
                                <option value="{{ leave_type.id }}" {% if leave_type_id|int == leave_type.id %}selected{% endif %}>
                                    {{ leave_type.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('search_leaves') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>#</th>
                            <th>اسم الموظف</th>
                            <th>نوع الإجازة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>المدة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if leaves %}
                            {% for item in leaves %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>
                                    <a href="{{ url_for('employee_leaves', employee_id=item.employee.id) }}">
                                        {{ item.employee.full_name }}
                                    </a>
                                </td>
                                <td>{{ item.leave_type.name }}</td>
                                <td class="date-cell">{{ item.leave.start_date.strftime('%Y-%m-%d') }}</td>
                                <td class="date-cell">{{ item.leave.end_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ item.leave.days_count }} يوم</td>
                                <td>
                                    {% if item.leave.status == 'approved' %}
                                        <span class="badge bg-success">معتمدة</span>
                                    {% elif item.leave.status == 'pending' %}
                                        <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif item.leave.status == 'rejected' %}
                                        <span class="badge bg-danger">مرفوضة</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ item.leave.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('edit_leave', leave_id=item.leave.id) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        <a href="{{ url_for('delete_leave', leave_id=item.leave.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه الإجازة؟')">
                                            <i class="fas fa-trash"></i> حذف
                                        </a>
                                        <a href="{{ url_for('view_documents', leave_id=item.leave.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-file"></i> المستندات
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="8" class="text-center">لا توجد إجازات مطابقة لمعايير البحث</td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
