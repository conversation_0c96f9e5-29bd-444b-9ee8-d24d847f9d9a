<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>المتمتعون بالإجازات - نظام الإجازات</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    {% include 'nav.html' %}

    <div class="container mt-4">
        <div class="card">
            <div class="card-header">
                <h2>المتمتعون بالإجازات حالياً</h2>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>اسم الموظف</th>
                                <th>نوع الإجازة</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ النهاية</th>
                                <th>عدد الأيام</th>
                                <th>القسم</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for leave in current_leaves %}
                            <tr>
                                <td>{{ leave.employee.full_name }}</td>
                                <td>{{ leave.leave_type.name }}</td>
                                <td class="date-cell">{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                                <td class="date-cell">{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ (leave.end_date - leave.start_date).days + 1 }}</td>
                                <td>{{ leave.employee.department }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>