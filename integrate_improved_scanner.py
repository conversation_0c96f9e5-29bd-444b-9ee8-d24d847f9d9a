"""
دمج وحدة المسح الضوئي المحسنة مع التطبيق الرئيسي
"""
import os
import sys
import traceback
from datetime import datetime

# استيراد وحدة المسح الضوئي المحسنة
import improved_scanner

def integrate_with_app():
    """
    دمج وحدة المسح الضوئي المحسنة مع التطبيق الرئيسي
    """
    print("=== بدء دمج وحدة المسح الضوئي المحسنة مع التطبيق الرئيسي ===")
    
    # التحقق من وجود ملف app.py
    if not os.path.exists("app.py") and not os.path.exists("app_new.py"):
        print("لم يتم العثور على ملف التطبيق الرئيسي (app.py أو app_new.py)")
        return False
    
    # تحديد اسم ملف التطبيق الرئيسي
    app_file = "app_new.py" if os.path.exists("app_new.py") else "app.py"
    print(f"استخدام ملف التطبيق الرئيسي: {app_file}")
    
    # إضافة استيراد وحدة المسح الضوئي المحسنة إلى ملف التطبيق الرئيسي
    try:
        with open(app_file, "r", encoding="utf-8") as f:
            app_content = f.read()
        
        # التحقق من وجود استيراد وحدة المسح الضوئي المحسنة
        if "import improved_scanner" not in app_content:
            # إضافة استيراد وحدة المسح الضوئي المحسنة بعد استيراد وحدة pdf_only_scanner
            if "import pdf_only_scanner" in app_content:
                app_content = app_content.replace("import pdf_only_scanner", "import pdf_only_scanner\nimport improved_scanner")
                print("تم إضافة استيراد وحدة المسح الضوئي المحسنة بعد استيراد وحدة pdf_only_scanner")
            else:
                # إضافة استيراد وحدة المسح الضوئي المحسنة في بداية الملف
                app_content = "import improved_scanner\n" + app_content
                print("تم إضافة استيراد وحدة المسح الضوئي المحسنة في بداية الملف")
            
            # حفظ التغييرات
            with open(app_file, "w", encoding="utf-8") as f:
                f.write(app_content)
            
            print(f"تم تحديث ملف التطبيق الرئيسي: {app_file}")
        else:
            print("وحدة المسح الضوئي المحسنة مستوردة بالفعل في ملف التطبيق الرئيسي")
    except Exception as e:
        print(f"خطأ في تحديث ملف التطبيق الرئيسي: {str(e)}")
        traceback.print_exc()
        return False
    
    # إضافة مسار للمسح الضوئي المحسن
    try:
        # التحقق من وجود مسار للمسح الضوئي المحسن
        if "'/scan_with_improved_scanner'" not in app_content:
            # إضافة مسار للمسح الضوئي المحسن
            route_code = """
@app.route('/scan_with_improved_scanner', methods=['POST'])
def scan_with_improved_scanner():
    """
    مسح مستند باستخدام وحدة المسح الضوئي المحسنة
    """
    if not session.get('employee_id'):
        return jsonify({'error': 'غير مصرح'}), 401

    # التحقق من صلاحيات المستخدم (فقط المدير يمكنه استخدام الماسح الضوئي)
    if not session.get('is_admin'):
        return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه الوظيفة'}), 403

    # الحصول على بيانات الطلب
    data = request.json
    scanner_id = data.get('scanner_id', '0')
    dpi = int(data.get('dpi', 300))
    use_adf = data.get('use_adf', True)

    print(f"بيانات الطلب: scanner_id={scanner_id}, dpi={dpi}, use_adf={use_adf}")

    try:
        # مسح المستند باستخدام وحدة المسح الضوئي المحسنة
        success = improved_scanner.scan_document(
            scanner_id=scanner_id,
            dpi=dpi,
            use_adf=use_adf
        )

        if not success:
            print("فشل في مسح المستند باستخدام وحدة المسح الضوئي المحسنة")
            return jsonify({'error': 'فشل في مسح المستند'}), 500

        # الحصول على عدد الصفحات الممسوحة
        page_count = improved_scanner.get_scanned_pages_count()
        print(f"تم مسح {page_count} صفحة بنجاح باستخدام وحدة المسح الضوئي المحسنة")

        # إرجاع معلومات عن الصفحات الممسوحة
        return jsonify({
            'success': True,
            'page_count': page_count,
            'message': f'تم مسح {page_count} صفحة بنجاح',
            'method': 'improved_scanner'
        })
    except Exception as e:
        print(f"خطأ في مسح المستند: {str(e)}")
        traceback.print_exc()
        return jsonify({'error': f'حدث خطأ أثناء المسح الضوئي: {str(e)}'}), 500
"""
            
            # إضافة المسار بعد مسار scan_with_device
            if "@app.route('/scan_with_device'" in app_content:
                # البحث عن نهاية مسار scan_with_device
                scan_with_device_end = app_content.find("@app.route", app_content.find("@app.route('/scan_with_device'") + 1)
                if scan_with_device_end > 0:
                    # إضافة المسار بعد مسار scan_with_device
                    app_content = app_content[:scan_with_device_end] + route_code + app_content[scan_with_device_end:]
                    print("تم إضافة مسار للمسح الضوئي المحسن بعد مسار scan_with_device")
                else:
                    # إضافة المسار في نهاية الملف
                    app_content += "\n" + route_code
                    print("تم إضافة مسار للمسح الضوئي المحسن في نهاية الملف")
            else:
                # إضافة المسار في نهاية الملف
                app_content += "\n" + route_code
                print("تم إضافة مسار للمسح الضوئي المحسن في نهاية الملف")
            
            # حفظ التغييرات
            with open(app_file, "w", encoding="utf-8") as f:
                f.write(app_content)
            
            print(f"تم تحديث ملف التطبيق الرئيسي: {app_file}")
        else:
            print("مسار المسح الضوئي المحسن موجود بالفعل في ملف التطبيق الرئيسي")
    except Exception as e:
        print(f"خطأ في إضافة مسار للمسح الضوئي المحسن: {str(e)}")
        traceback.print_exc()
        return False
    
    print("=== انتهاء دمج وحدة المسح الضوئي المحسنة مع التطبيق الرئيسي بنجاح ===")
    return True

if __name__ == "__main__":
    try:
        integrate_with_app()
    except Exception as e:
        print(f"خطأ في دمج وحدة المسح الضوئي المحسنة مع التطبيق الرئيسي: {str(e)}")
        traceback.print_exc()
