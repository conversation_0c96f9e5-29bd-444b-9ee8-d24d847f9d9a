{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h2 class="mb-0"><i class="fas fa-users me-2"></i> تقارير الموظفين</h2>
            <a href="{{ url_for('reports') }}" class="btn btn-light btn-sm">
                <i class="fas fa-arrow-right me-1"></i> العودة للتقارير
            </a>
        </div>
        <div class="card-body">
            <form method="GET" id="reportForm" class="mb-4">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="employee_id" class="form-label">الموظف:</label>
                        <select name="employee_id" id="employee_id" class="form-select">
                            <option value="">جميع الموظفين</option>
                            {% for emp in employees %}
                            <option value="{{ emp.id }}" {% if selected_employee_id == emp.id|string %}selected{% endif %}>
                                {{ emp.full_name }} ({{ emp.employee_number or 'بدون رقم وظيفي' }})
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">من تاريخ:</label>
                        <input type="date" id="start_date" name="start_date" class="form-control" value="{{ start_date }}" lang="en">
                    </div>
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">إلى تاريخ:</label>
                        <input type="date" id="end_date" name="end_date" class="form-control" value="{{ end_date }}" lang="en">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-1"></i> عرض التقرير
                        </button>
                    </div>
                </div>
            </form>

            {% if report_data %}
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 class="h5 mb-0">نتائج التقرير</h3>
                <div class="btn-group">
                    <button type="button" class="btn btn-success" onclick="printReport()">
                        <i class="fas fa-print me-1"></i> طباعة
                    </button>
                    <button type="button" class="btn btn-info" onclick="exportToExcel()">
                        <i class="fas fa-file-excel me-1"></i> تصدير Excel
                    </button>
                </div>
            </div>

            <div class="table-responsive" id="reportTable">
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>ت</th>
                            <th>اسم الموظف</th>
                            <th>الرقم الوظيفي</th>
                            <th>نوع الإجازة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>المدة (أيام)</th>
                            <th>تاريخ المباشرة</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in report_data %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ item.employee.full_name }}</td>
                            <td>{{ item.employee.employee_number or '-' }}</td>
                            <td>{{ item.leave_type.name }}</td>
                            <td class="date-cell">{{ item.leave.start_date.strftime('%Y-%m-%d') }}</td>
                            <td class="date-cell">{{ item.leave.end_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ item.leave.days_count }}</td>
                            <td class="date-cell">{{ item.leave.return_date.strftime('%Y-%m-%d') if item.leave.return_date else '-' }}</td>
                            <td>
                                {% if 'تمت المباشرة' in item.leave.comment|default('') %}
                                <span class="badge bg-success">تمت المباشرة</span>
                                {% elif 'لم يباشر' in item.leave.comment|default('') %}
                                <span class="badge bg-danger">لم يباشر</span>
                                {% elif item.leave.end_date < current_date %}
                                <span class="badge bg-warning">انتهت</span>
                                {% else %}
                                <span class="badge bg-primary">جارية</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <td colspan="6" class="text-start"><strong>إجمالي أيام الإجازات:</strong></td>
                            <td><strong>{{ total_days }}</strong></td>
                            <td colspan="2"></td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            {% if selected_employee_id %}
            <div class="card mt-4 border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h3 class="h5 mb-0"><i class="fas fa-chart-pie me-2"></i> ملخص إجازات الموظف</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h4 class="h6 mb-3">توزيع الإجازات حسب النوع</h4>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>نوع الإجازة</th>
                                                    <th>عدد الإجازات</th>
                                                    <th>إجمالي الأيام</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for leave_type, stats in leave_type_summary.items() %}
                                                <tr>
                                                    <td>{{ leave_type }}</td>
                                                    <td>{{ stats.count }}</td>
                                                    <td>{{ stats.total_days }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="h6 mb-3">معلومات الموظف</h4>
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>الاسم:</span>
                                            <strong>{{ employee_info.full_name }}</strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>الرقم الوظيفي:</span>
                                            <strong>{{ employee_info.employee_number or '-' }}</strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>المسمى الوظيفي:</span>
                                            <strong>{{ employee_info.job_title }}</strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>مكان العمل:</span>
                                            <strong>{{ employee_info.work_location }}</strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>رصيد الإجازات:</span>
                                            <strong>{{ employee_info.leave_balance }} يوم</strong>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> يرجى تحديد معايير البحث وعرض التقرير.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    /* تنسيقات حقول التاريخ */
    input[type="date"] {
        direction: ltr;
    }

    /* تنسيقات جدول التقارير */
    .report-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 1rem;
        border: 1px solid #000;
    }

    /* تنسيق رأس الجدول */
    .report-table thead {
        background-color: #e6e6e6;
    }

    .report-table th {
        background-color: #e6e6e6;
        color: #000;
        font-weight: bold;
        padding: 8px;
        text-align: right;
        border: 1px solid #000;
    }

    /* تنسيق خلايا الجدول */
    .report-table td {
        background-color: #fff;
        color: #000;
        padding: 8px;
        text-align: right;
        border: 1px solid #000;
    }

    /* تنسيق الحدود */
    .report-table, .report-table th, .report-table td {
        border: 1px solid #000;
    }

    /* تنسيق تذييل الجدول */
    .report-table tfoot {
        background-color: #fff;
    }

    /* تنسيقات الطباعة */
    @media print {
        body * {
            visibility: hidden;
        }

        #reportTable, #reportTable * {
            visibility: visible;
        }

        #reportTable {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }

        /* إخفاء العناصر غير المطلوبة في الطباعة */
        .no-print, button, .btn, tfoot, .report-table tfoot {
            display: none !important;
        }

        /* تنسيق الجدول للطباعة */
        .report-table {
            width: 100% !important;
            border-collapse: collapse !important;
            border: 1px solid #000 !important;
        }

        /* تنسيق رأس الجدول للطباعة */
        .report-table thead, .report-table th {
            background-color: #e6e6e6 !important;
            color: #000 !important;
            border: 1px solid #000 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            color-adjust: exact !important;
        }

        /* تنسيق خلايا الجدول للطباعة */
        .report-table td {
            background-color: #fff !important;
            color: #000 !important;
            border: 1px solid #000 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            color-adjust: exact !important;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
<script src="{{ url_for('static', filename='js/report-utils.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تعيين أحداث الأزرار
        document.querySelector('.btn-success').addEventListener('click', function() {
            printReport('تقرير إجازات الموظفين');
        });

        document.querySelector('.btn-info').addEventListener('click', function() {
            exportToExcel('تقرير الموظفين', 'تقرير_إجازات_الموظفين.xlsx');
        });

        // إضافة زر تصدير PDF
        const exportButtons = document.querySelector('.btn-group');
        const pdfButton = document.createElement('button');
        pdfButton.type = 'button';
        pdfButton.className = 'btn btn-danger';
        pdfButton.innerHTML = '<i class="fas fa-file-pdf me-1"></i> تصدير PDF';
        pdfButton.addEventListener('click', function() {
            exportToPDF('تقرير_إجازات_الموظفين.pdf');
        });
        exportButtons.appendChild(pdfButton);
    });
</script>
{% endblock %}
