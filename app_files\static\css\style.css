:root {
    --primary-color: #1a237e;
    --secondary-color: #0d47a1;
    --accent-color: #2962ff;
    --text-color: #212121;
    --background-color: #f5f5f5;
    --white: #ffffff;
    --error-color: #f44336;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.login-container {
    background: var(--white);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-container h2 {
    color: var(--primary-color);
    margin-bottom: 2rem;
    font-size: 1.8rem;
}

.form-group {
    margin-bottom: 1.5rem;
    text-align: right;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-color);
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    border-color: var(--accent-color);
    outline: none;
}

button {
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 0.8rem 2rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    width: 100%;
    transition: transform 0.2s ease;
}

button:hover {
    transform: translateY(-2px);
}

.error-message {
    background-color: #ffebee;
    color: var(--error-color);
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
    font-weight: 500;
}

.login-logo {
    width: 120px;
    margin-bottom: 1.5rem;
}

.login-title {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: #666;
    margin-bottom: 2rem;
    font-size: 0.9rem;
}

/* Navigation Styles */
nav {
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    padding: 1rem 0;
}

nav ul {
    display: flex;
    justify-content: space-around;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

nav li {
    list-style: none;
    margin-left: 2rem;
}

nav a {
    color: var(--white);
    text-decoration: none;
    font-weight: 600;
    padding: 0.7rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

nav a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* Main Container Styles */
.main-container {
    max-width: 1200px;
    margin: 80px auto 20px;
    padding: 20px;
}

/* Card Styles */
.card {
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

th, td {
    padding: 15px;
    text-align: right;
    border-bottom: 1px solid #ddd;
}

th {
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    color: var(--white);
    font-weight: 600;
}

tr:hover {
    background-color: #f5f5f5;
}

/* Form Styles */
.form-container {
    background: var(--white);
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(41, 98, 255, 0.1);
}

.form-group textarea {
    height: 100px;
    resize: vertical;
}

/* Button Styles */
.btn {
    display: inline-block;
    font-weight: 600;
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--secondary-color);
    color: var(--white);
}

.btn-danger {
    background-color: var(--accent-color);
    color: var(--white);
}

.btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

/* Stats Cards */
.stats-card {
    background: var(--white);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.stats-card h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.stats-card .number {
    font-size: 2em;
    color: var(--secondary-color);
    font-weight: bold;
}

/* Alert Messages */
.alert {
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* إضافة تصميم البحث */
.search-container {
    margin-bottom: 2rem;
}

.search-container input {
    width: 300px;
    padding: 12px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    margin-left: 10px;
}

.search-container button {
    border-radius: 25px;
}

/* إضافة للأنماط الموجودة */
.btn-secondary {
    background: #757575;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
}

.btn-secondary:hover {
    background: #616161;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* تنسيق البطاقات */
.card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* تنسيق الجدول */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

/* تنسيق الأزرار */
.btn-group .btn {
    margin: 0 2px;
}

/* تنسيق البطاقات الإحصائية */
.card .fa-2x {
    opacity: 0.8;
}

/* تنسيق حقل البحث */
.input-group {
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* تحسين المظهر العام */
.container-fluid {
    padding: 20px;
}

.top-actions {
    position: fixed;
    top: 60px;
    right: 0;
    left: 0;
    background: white;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.main-content {
    margin-top: 120px;
    padding: 20px;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.action-buttons .btn {
    min-width: 120px;
}


