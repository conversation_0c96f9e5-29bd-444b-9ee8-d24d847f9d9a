"""
سكريبت لتشغيل تطبيق Flask مع Ngrok للوصول عبر الإنترنت
يجب تثبيت Ngrok أولاً من https://ngrok.com/download
ثم تثبيت مكتبة pyngrok باستخدام: pip install pyngrok
"""

from app import app
import os
import sys
import threading
import webbrowser
import socket
import time
from pyngrok import ngrok

# البحث عن منفذ متاح
def find_free_port():
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(('', 0))
        return s.getsockname()[1]

# فتح المتصفح بعد تأخير قصير
def open_browser(url):
    # انتظار 1.5 ثانية لبدء تشغيل الخادم
    time.sleep(1.5)
    print(f"فتح المتصفح على العنوان: {url}")
    webbrowser.open(url)

# تعديل المسارات للعمل مع PyInstaller
if getattr(sys, 'frozen', False):
    # إذا كان التطبيق مجمدًا (exe)
    template_folder = os.path.join(sys._MEIPASS, 'templates')
    static_folder = os.path.join(sys._MEIPASS, 'static')
    instance_path = os.path.join(sys._MEIPASS, 'instance')

    # تعيين المسارات الجديدة
    app.template_folder = template_folder
    app.static_folder = static_folder
    app.instance_path = instance_path

    # تعطيل وضع التصحيح في الإصدار النهائي
    app.debug = False

    # التأكد من وجود قاعدة البيانات
    db_path = os.path.join(instance_path, 'ajazat.db')
    if not os.path.exists(db_path):
        print(f"تحذير: ملف قاعدة البيانات غير موجود في {db_path}")
else:
    # في وضع التطوير
    app.debug = True

if __name__ == '__main__':
    # التأكد من وجود مجلدات التحميل
    try:
        from app_new import app
    except ImportError:
        from app import app

    # طباعة مجلدات التحميل للتأكد من وجودها
    print("مجلدات التحميل:")
    print(f"UPLOAD_FOLDER: {app.config['UPLOAD_FOLDER']}")
    print(f"DOCUMENTS_FOLDER: {app.config['DOCUMENTS_FOLDER']}")
    print(f"SCANS_FOLDER: {app.config['SCANS_FOLDER']}")
    print(f"BACKUP_FOLDER: {app.config['BACKUP_FOLDER']}")

    # التأكد من وجود المجلدات
    for folder in [app.config['UPLOAD_FOLDER'], app.config['DOCUMENTS_FOLDER'], app.config['SCANS_FOLDER'], app.config['BACKUP_FOLDER']]:
        try:
            if not os.path.exists(folder):
                os.makedirs(folder)
                print(f"تم إنشاء المجلد: {folder}")
            else:
                print(f"المجلد موجود: {folder}")
        except Exception as e:
            print(f"خطأ في إنشاء المجلد {folder}: {str(e)}")

    # استخدام المنفذ 5000 افتراضيًا، أو البحث عن منفذ متاح
    try:
        port = int(os.environ.get('PORT', 5000))
        # التحقق مما إذا كان المنفذ متاحًا
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        test_socket.bind(('127.0.0.1', port))
        test_socket.close()
    except (OSError, socket.error):
        # إذا كان المنفذ مشغولاً، البحث عن منفذ آخر
        port = find_free_port()
        print(f"المنفذ 5000 مشغول، استخدام المنفذ {port} بدلاً من ذلك")

    # إنشاء نفق Ngrok
    public_url = ngrok.connect(port).public_url
    print(f"تم إنشاء نفق Ngrok: {public_url}")
    print(f"يمكنك الوصول إلى التطبيق من أي جهاز باستخدام العنوان: {public_url}")

    # فتح المتصفح تلقائيًا
    url = f"http://localhost:{port}"
    threading.Thread(target=open_browser, args=(url,)).start()

    print(f"بدء تشغيل التطبيق على المنفذ {port}...")
    # تشغيل التطبيق على جميع الواجهات (0.0.0.0) ليكون متاحًا على الشبكة المحلية
    app.run(host='0.0.0.0', port=port, threaded=True)
