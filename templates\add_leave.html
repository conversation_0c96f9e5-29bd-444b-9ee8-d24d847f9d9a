{% extends 'base.html' %}

{% block styles %}
<style>
    .bg-gradient-primary-to-secondary {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    }

    .card {
        transition: all 0.3s ease;
        overflow: hidden;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    /* تحسين مظهر حقول الإدخال الأخرى */
    input[type="number"], select, textarea {
        background-color: #f8f9ff !important;
        border: 2px solid #d1d9ff !important;
        color: #333 !important;
        font-weight: 500 !important;
    }

    input[type="number"]:focus, select:focus, textarea:focus {
        background-color: #fff !important;
        border-color: #4e73df !important;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25) !important;
    }

    /* تحسين مظهر العناوين */
    .form-label {
        font-weight: bold !important;
        color: #2e4094 !important;
        font-size: 1.05rem !important;
    }

    /* تحسين مظهر الأزرار */
    .btn-primary {
        background-color: #4e73df;
        border-color: #4e73df;
    }

    .btn-primary:hover {
        background-color: #224abe;
        border-color: #224abe;
    }

    /* تنسيقات إضافية لحقول التاريخ في هذه الصفحة */
    input[type="date"] {
        -webkit-appearance: none;
        appearance: none;
        direction: rtl !important;
        unicode-bidi: bidi-override !important;
        text-align: right !important;
        padding-right: 10px !important;
        font-family: Arial, sans-serif !important;
        background-color: #f8f9ff !important;
        border: 2px solid #d1d9ff !important;
        color: #333 !important;
        font-weight: 500 !important;
        position: relative;
        display: flex !important;
        align-items: center !important;
        justify-content: flex-end !important;
    }

    /* عرض التاريخ بالتنسيق المطلوب yyyy/mm/dd */
    input[type="date"]::before {
        content: attr(data-date);
        display: inline-block;
        width: 100%;
        text-align: right;
        position: absolute;
        top: 0;
        bottom: 0;
        right: 10px;
        left: 30px;
        line-height: calc(1.5em + 0.75rem + 2px);
        pointer-events: none;
    }

    /* تطبيق الانعكاس لحقول التاريخ في WebKit */
    input[type="date"]::-webkit-datetime-edit,
    input[type="date"]::-webkit-datetime-edit-year-field,
    input[type="date"]::-webkit-datetime-edit-month-field,
    input[type="date"]::-webkit-datetime-edit-day-field,
    input[type="date"]::-webkit-datetime-edit-text {
        direction: rtl !important;
        text-align: right !important;
    }

    /* تغيير ترتيب عناصر التاريخ لتظهر بالشكل yyyy/mm/dd */
    input[type="date"]::-webkit-datetime-edit {
        display: flex !important;
        flex-direction: row-reverse !important;
    }

    input[type="date"]::-webkit-datetime-edit-year-field {
        order: 1;
        font-weight: bold;
    }
    input[type="date"]::-webkit-datetime-edit-month-field {
        order: 2;
    }
    input[type="date"]::-webkit-datetime-edit-day-field {
        order: 3;
    }
    input[type="date"]::-webkit-datetime-edit-text {
        content: '/';
        margin: 0 2px;
    }

    /* إخفاء القيمة الأصلية عندما يكون هناك قيمة data-date */
    input[type="date"]:not([data-date=""])::-webkit-datetime-edit {
        color: transparent;
    }

    /* تنسيق حقول التاريخ عند التركيز */
    input[type="date"]:focus {
        background-color: #fff !important;
        border-color: #4e73df !important;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25) !important;
    }

    /* تنسيق أيقونة التقويم */
    input[type="date"]::-webkit-calendar-picker-indicator {
        opacity: 1;
        display: block;
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 24 24"><path fill="%234e73df" d="M20 3h-1V1h-2v2H7V1H5v2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 18H4V8h16v13z"/></svg>');
        width: 20px;
        height: 20px;
        cursor: pointer;
        position: absolute;
        left: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-gradient-primary-to-secondary text-white">
            <h2 class="mb-0"><i class="fas fa-calendar-plus me-2"></i> إضافة إجازة للموظف: {{ employee.full_name }}</h2>
        </div>
        <div class="card-body bg-light">
            <form method="POST">
                <div class="mb-3">
                    <label for="leave_type_id" class="form-label">نوع الإجازة:</label>
                    <div class="input-group">
                        <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                            <i class="fas fa-list-alt"></i>
                        </span>
                        <select name="leave_type_id" id="leave_type_id" class="form-select" required>
                            <option value="">اختر نوع الإجازة</option>
                            {% for leave_type in leave_types %}
                            <option value="{{ leave_type.id }}" data-is-maternity="{{ '1' if 'أمومة' in leave_type.name or 'وضع' in leave_type.name else '0' }}">
                                {{ leave_type.name }}
                                {% if leave_type.description %}
                                ({{ leave_type.description }})
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <!-- حقل عدد الأطفال (يظهر فقط لإجازات الأمومة وما بعد الوضع) -->
                <div id="children-count-container" class="mb-3" style="display: none;">
                    <label for="children_count" class="form-label">عدد الأطفال:</label>
                    <div class="input-group">
                        <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                            <i class="fas fa-baby"></i>
                        </span>
                        <input type="number" name="children_count" id="children_count" class="form-control" min="1" value="1">
                    </div>
                    <div class="form-text text-muted">
                        <i class="fas fa-info-circle"></i> يرجى تحديد عدد الأطفال للإجازات المتعلقة بالأمومة
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="start_date" class="form-label">تاريخ بدء الإجازة:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                                <i class="fas fa-calendar-day"></i>
                            </span>
                            <input type="date" name="start_date" id="start_date" class="form-control" placeholder="اختر تاريخ البداية" required>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="days_count" class="form-label">مدة الإجازة (بالأيام):</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                                <i class="fas fa-calculator"></i>
                            </span>
                            <input type="number" name="days_count" id="days_count" class="form-control" min="1" value="1" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="end_date" class="form-label">تاريخ انتهاء الإجازة:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                                <i class="fas fa-calendar-check"></i>
                            </span>
                            <input type="date" name="end_date" id="end_date" class="form-control" placeholder="تاريخ النهاية" readonly>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="departure_date" class="form-label">تاريخ الانفكاك من العمل:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                                <i class="fas fa-sign-out-alt"></i>
                            </span>
                            <input type="date" name="departure_date" id="departure_date" class="form-control" placeholder="تاريخ الانفكاك">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="return_date" class="form-label">تاريخ العودة للعمل:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                                <i class="fas fa-calendar-plus"></i>
                            </span>
                            <input type="date" name="return_date" id="return_date" class="form-control" placeholder="تاريخ المباشرة">
                        </div>
                    </div>
                </div>

                <!-- حقول الأمر الإداري -->
                <hr>
                <h5 class="mb-3 text-primary"><i class="fas fa-file-alt me-2"></i> بيانات الأمر الإداري</h5>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="order_number" class="form-label">رقم الأمر الإداري:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                                <i class="fas fa-hashtag"></i>
                            </span>
                            <input type="text" name="order_number" id="order_number" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="order_date" class="form-label">تاريخ الأمر الإداري:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                                <i class="fas fa-calendar-alt"></i>
                            </span>
                            <input type="date" name="order_date" id="order_date" class="form-control" placeholder="تاريخ الأمر">
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="order_source" class="form-label">الجهة المصدرة للأمر:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                                <i class="fas fa-building"></i>
                            </span>
                            <input type="text" name="order_source" id="order_source" class="form-control">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="comment" class="form-label">ملاحظات:</label>
                    <div class="input-group">
                        <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width: 45px;">
                            <i class="fas fa-comment-alt"></i>
                        </span>
                        <textarea name="comment" id="comment" class="form-control" rows="3"></textarea>
                    </div>
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-paper-plane me-2"></i> إضافة الإجازة
                    </button>
                    <a href="{{ url_for('employee_leaves', employee_id=employee.id) }}" class="btn btn-secondary btn-lg">
                        <i class="fas fa-times me-2"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/weekend_helper.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const leaveTypeSelect = document.getElementById('leave_type_id');
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');
        const daysCountInput = document.getElementById('days_count');
        const departureDateInput = document.getElementById('departure_date');
        const returnDateInput = document.getElementById('return_date');
        const orderDateInput = document.getElementById('order_date');
        const childrenCountContainer = document.getElementById('children-count-container');
        const childrenCountInput = document.getElementById('children_count');

        // تطبيق flatpickr على حقول التاريخ
        let startDateFp, endDateFp, departureDateFp, returnDateFp, orderDateFp;

        // تحديث حقول التاريخ عند تغيير تاريخ البداية
        startDateInput.addEventListener('change', function() {
            updateEndDate();
        });

        // تعيين حقل تاريخ النهاية للقراءة فقط
        endDateInput.setAttribute('readonly', 'readonly');

        // قاموس لتخزين مدة الإجازة لكل نوع
        const leaveDaysMap = {
            {% for leave_type in leave_types %}
                {% if leave_type.days_allowed %}
                    "{{ leave_type.id }}": {{ leave_type.days_allowed }},
                {% endif %}
            {% endfor %}
        };

        function calculateEndDate(startDate, days) {
            if (!startDate || !days) return '';
            const start = new Date(startDate);
            const end = new Date(start);
            end.setDate(start.getDate() + parseInt(days) - 1);
            return end.toISOString().split('T')[0];
        }

        function updateEndDate() {
            const startDate = startDateInput.value;
            const daysCount = daysCountInput.value;

            if (startDate && daysCount) {
                // حساب تاريخ النهاية
                const calculatedEndDate = calculateEndDate(startDate, daysCount);
                endDateInput.value = calculatedEndDate;

                // تحديث عرض تاريخ النهاية
                const endDateEvent = new Event('change');
                endDateInput.dispatchEvent(endDateEvent);

                // اقتراح تاريخ الانفكاك (نفس تاريخ البداية)
                if (!departureDateInput.value) {
                    departureDateInput.value = startDate;

                    // تحديث عرض تاريخ الانفكاك
                    const departureDateEvent = new Event('change');
                    departureDateInput.dispatchEvent(departureDateEvent);
                }

                // اقتراح تاريخ المباشرة (اليوم التالي لتاريخ النهاية)
                const endDate = new Date(calculatedEndDate);
                let returnDate = new Date(endDate);
                returnDate.setDate(endDate.getDate() + 1);

                // التحقق مما إذا كان تاريخ العودة يصادف يوم الجمعة أو السبت
                // وتعديله إلى يوم الأحد التالي إذا كان كذلك
                returnDate = adjustReturnDateForWeekend(returnDate);

                // تنسيق التاريخ بالشكل الصحيح YYYY-MM-DD
                const formattedReturnDate = formatDate(returnDate);

                // تعيين تاريخ العودة إذا كان فارغًا
                if (!returnDateInput.value) {
                    returnDateInput.value = formattedReturnDate;

                    // تحديث عرض تاريخ العودة
                    const returnDateEvent = new Event('change');
                    returnDateInput.dispatchEvent(returnDateEvent);

                    console.log('تم تعيين تاريخ العودة إلى:', formattedReturnDate);
                }
            }
        }

        // دالة للتحقق مما إذا كان نوع الإجازة المحدد هو إجازة أمومة أو ما بعد الوضع
        function checkIfMaternityLeave() {
            const selectedOption = leaveTypeSelect.options[leaveTypeSelect.selectedIndex];
            if (selectedOption && selectedOption.getAttribute('data-is-maternity') === '1') {
                childrenCountContainer.style.display = 'block';
                childrenCountInput.setAttribute('required', 'required');
            } else {
                childrenCountContainer.style.display = 'none';
                childrenCountInput.removeAttribute('required');
            }
        }

        // تحديث مدة الإجازة عند اختيار نوع الإجازة
        leaveTypeSelect.addEventListener('change', function() {
            const selectedLeaveTypeId = this.value;

            // التحقق مما إذا كان نوع الإجازة المحدد هو إجازة أمومة أو ما بعد الوضع
            checkIfMaternityLeave();

            if (selectedLeaveTypeId && leaveDaysMap[selectedLeaveTypeId]) {
                daysCountInput.value = leaveDaysMap[selectedLeaveTypeId];
                updateEndDate();
            }
        });

        // تحديث عند تغيير عدد الأيام
        daysCountInput.addEventListener('change', function() {
            // إعادة تعيين قيمة تاريخ العودة عند تغيير عدد الأيام
            returnDateInput.value = '';
            console.log('تم إعادة تعيين تاريخ العودة بسبب تغيير عدد الأيام');
            updateEndDate();
        });

        daysCountInput.addEventListener('input', function() {
            // إعادة تعيين قيمة تاريخ العودة عند تغيير عدد الأيام
            returnDateInput.value = '';
            console.log('تم إعادة تعيين تاريخ العودة بسبب تغيير عدد الأيام');
            updateEndDate();
        });

        // تطبيق تنسيقات على حقول التاريخ
        const dateInputs = [startDateInput, endDateInput, departureDateInput, returnDateInput, orderDateInput];

        // تطبيق تنسيقات على كل حقل
        dateInputs.forEach(input => {
            if (input) {
                // تعيين اتجاه النص من اليمين إلى اليسار
                input.style.direction = 'rtl';
                input.style.textAlign = 'right';
                input.style.unicodeBidi = 'bidi-override';

                // إضافة سمات للحقول
                input.setAttribute('dir', 'rtl');
                input.setAttribute('lang', 'ar');

                // تعيين تنسيق التاريخ المطلوب
                input.setAttribute('placeholder', 'yyyy/mm/dd');

                // إضافة مستمع لتنسيق التاريخ عند التغيير
                input.addEventListener('change', function() {
                    if (this.value) {
                        // تحويل التاريخ إلى التنسيق المطلوب (yyyy/mm/dd) للعرض فقط
                        const dateParts = this.value.split('-');
                        if (dateParts.length === 3) {
                            // تخزين التنسيق المطلوب في سمة مخصصة للعرض
                            const formattedDate = `${dateParts[0]}/${dateParts[1]}/${dateParts[2]}`;
                            this.setAttribute('data-date', formattedDate);
                            console.log(`تم تنسيق التاريخ: ${formattedDate}`);
                        }
                    }
                });

                // تطبيق التنسيق على القيم الموجودة
                if (input.value) {
                    const dateParts = input.value.split('-');
                    if (dateParts.length === 3) {
                        const formattedDate = `${dateParts[0]}/${dateParts[1]}/${dateParts[2]}`;
                        input.setAttribute('data-date', formattedDate);
                    }
                }

                console.log('تم تطبيق التنسيقات على الحقل:', input.id);
            }
        });

        // تشغيل عند تحميل الصفحة للتأكد من الحالة الأولية
        setTimeout(function() {
            updateEndDate();
            checkIfMaternityLeave();
        }, 200);
    });
</script>
{% endblock %}
