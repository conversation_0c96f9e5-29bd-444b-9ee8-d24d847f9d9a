"""
سكريبت لإعادة تعيين قاعدة البيانات بالكامل وإنشاء مستخدم مدير جديد
"""
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash
import os
import sys
import shutil
from datetime import datetime

# إنشاء تطبيق Flask
app = Flask(__name__)

# تكوين قاعدة البيانات
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///ajazat.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة قاعدة البيانات
db = SQLAlchemy(app)

# تعريف نموذج الموظف
class Employee(db.Model):
    __tablename__ = 'employee'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True)
    password_hash = db.Column(db.String(120))
    full_name = db.Column(db.String(120))
    employee_number = db.Column(db.String(50), nullable=True, unique=True)
    department = db.Column(db.String(120), nullable=True)
    job_title = db.Column(db.String(120))
    work_location = db.Column(db.String(120))
    leave_balance = db.Column(db.Integer, default=36)
    is_admin = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    permissions = db.Column(db.String(255))
    children_count = db.Column(db.Integer, default=0)
    
    def set_password(self, password):
        if password:
            self.password_hash = generate_password_hash(password)

# تعريف نموذج نوع الإجازة
class LeaveType(db.Model):
    __tablename__ = 'leave_type'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    days_allowed = db.Column(db.Integer)
    description = db.Column(db.String(200))

def reset_database():
    """إعادة تعيين قاعدة البيانات بالكامل"""
    print("جاري إعادة تعيين قاعدة البيانات...")
    
    # التأكد من وجود مجلد instance
    instance_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance')
    if not os.path.exists(instance_dir):
        os.makedirs(instance_dir)
        print(f"تم إنشاء مجلد قاعدة البيانات: {instance_dir}")
    
    # تحديد مسار قاعدة البيانات
    db_path = os.path.join(instance_dir, 'ajazat.db')
    
    # إنشاء نسخة احتياطية من قاعدة البيانات الحالية إذا كانت موجودة
    if os.path.exists(db_path):
        backup_dir = 'backups'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = os.path.join(backup_dir, f'ajazat_backup_{timestamp}.db')
        
        try:
            shutil.copy2(db_path, backup_path)
            print(f"تم إنشاء نسخة احتياطية من قاعدة البيانات: {backup_path}")
        except Exception as e:
            print(f"خطأ في إنشاء نسخة احتياطية: {str(e)}")
        
        # حذف قاعدة البيانات الحالية
        try:
            os.remove(db_path)
            print("تم حذف قاعدة البيانات الحالية")
        except Exception as e:
            print(f"خطأ في حذف قاعدة البيانات: {str(e)}")
    
    # إنشاء جميع الجداول
    with app.app_context():
        db.create_all()
        print("تم إنشاء جداول قاعدة البيانات الجديدة")
        
        # إنشاء مستخدم مدير جديد
        admin = Employee(
            username='admin',
            full_name='مدير النظام',
            job_title='مدير النظام',
            work_location='المقر الرئيسي',
            is_admin=True,
            leave_balance=36,
            is_active=True,
            permissions='all',
            children_count=0
        )
        
        # تعيين كلمة المرور
        admin.set_password('admin')
        
        # حفظ المستخدم في قاعدة البيانات
        db.session.add(admin)
        
        # إضافة أنواع الإجازات الافتراضية
        leave_types = [
            LeaveType(name='اعتيادية', description='إجازة اعتيادية'),
            LeaveType(name='مرضية', description='إجازة مرضية'),
            LeaveType(name='أمومة', days_allowed=365, description='إجازة أمومة'),
            LeaveType(name='دراسية', description='إجازة دراسية'),
            LeaveType(name='طويلة', description='إجازة طويلة')
        ]
        
        # إضافة أنواع الإجازات إلى قاعدة البيانات
        for leave_type in leave_types:
            db.session.add(leave_type)
        
        # حفظ التغييرات
        db.session.commit()
        
        print("تم إنشاء مستخدم مدير افتراضي وأنواع الإجازات بنجاح")
        print("معلومات تسجيل الدخول:")
        print("اسم المستخدم: admin")
        print("كلمة المرور: admin")

if __name__ == '__main__':
    reset_database()
