"""
سكريبت بسيط لاختبار الاتصال بالشبكة والتحقق من إمكانية الوصول إلى التطبيق
"""

import socket
import requests
import time
import sys
import os
import subprocess
import platform

def get_local_ip():
    """الحصول على عنوان IP المحلي للجهاز"""
    try:
        # إنشاء اتصال مؤقت للحصول على عنوان IP المحلي
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        print(f"خطأ في الحصول على عنوان IP المحلي: {str(e)}")
        return "127.0.0.1"

def check_port_open(ip, port):
    """التحقق مما إذا كان المنفذ مفتوحًا على عنوان IP محدد"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex((ip, port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"خطأ في التحقق من المنفذ: {str(e)}")
        return False

def check_app_running(url):
    """التحقق مما إذا كان التطبيق يعمل على عنوان URL محدد"""
    try:
        response = requests.get(url, timeout=5)
        return response.status_code == 200
    except Exception as e:
        print(f"خطأ في الاتصال بالتطبيق: {str(e)}")
        return False

def check_firewall():
    """التحقق من إعدادات جدار الحماية"""
    system = platform.system()
    
    if system == "Windows":
        try:
            # التحقق من حالة جدار الحماية في Windows
            result = subprocess.run(["netsh", "advfirewall", "show", "currentprofile"], 
                                   capture_output=True, text=True)
            return "State                      ON" not in result.stdout
        except Exception as e:
            print(f"خطأ في التحقق من جدار الحماية: {str(e)}")
            return "غير معروف"
    elif system == "Linux":
        try:
            # التحقق من وجود iptables في Linux
            result = subprocess.run(["iptables", "-L"], capture_output=True, text=True)
            return len(result.stdout.strip()) > 0
        except Exception:
            return "غير معروف"
    else:
        return "غير معروف"

def main():
    """الدالة الرئيسية"""
    print("=== اختبار الاتصال بالشبكة ===")
    
    # الحصول على عنوان IP المحلي
    local_ip = get_local_ip()
    print(f"عنوان IP المحلي: {local_ip}")
    
    # التحقق من المنافذ الشائعة
    ports_to_check = [5000, 8080]
    for port in ports_to_check:
        is_open = check_port_open(local_ip, port)
        print(f"المنفذ {port}: {'مفتوح' if is_open else 'مغلق'}")
        
        if is_open:
            # التحقق من إمكانية الوصول إلى التطبيق
            url = f"http://{local_ip}:{port}"
            app_running = check_app_running(url)
            print(f"التطبيق على {url}: {'يعمل' if app_running else 'لا يعمل'}")
    
    # التحقق من جدار الحماية
    firewall_status = check_firewall()
    print(f"حالة جدار الحماية: {firewall_status}")
    
    # نصائح للمستخدم
    print("\n=== نصائح لحل مشاكل الاتصال ===")
    print("1. تأكد من أن التطبيق يعمل على المنفذ الصحيح (5000 أو 8080)")
    print("2. تأكد من أن التطبيق يستمع على جميع واجهات الشبكة (host='0.0.0.0')")
    print("3. تأكد من أن جدار الحماية يسمح بالاتصالات على المنفذ المستخدم")
    print("4. تأكد من أن الموبايل متصل بنفس الشبكة المحلية")
    print("5. جرب استخدام Ngrok للوصول عبر الإنترنت (python run_with_ngrok.py)")
    print("6. تأكد من عدم وجود برامج أخرى تستخدم نفس المنفذ")
    
    print("\n=== روابط للوصول إلى التطبيق ===")
    for port in ports_to_check:
        if check_port_open(local_ip, port):
            print(f"الشبكة المحلية: http://{local_ip}:{port}")

if __name__ == "__main__":
    main()
