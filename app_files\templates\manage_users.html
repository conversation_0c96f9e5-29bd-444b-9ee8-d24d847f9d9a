{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card mb-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h2 class="mb-0">
                <i class="fas fa-users-cog me-2"></i> إدارة حسابات المستخدمين والصلاحيات
            </h2>
            <a href="{{ url_for('add_user') }}" class="btn btn-light">
                <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>#</th>
                            <th>اسم المستخدم</th>
                            <th>الاسم الكامل</th>
                            <th>المسمى الوظيفي</th>
                            <th>نوع الحساب</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if users %}
                            {% for user in users %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ user.username }}</td>
                                <td>{{ user.full_name }}</td>
                                <td>{{ user.job_title }}</td>
                                <td>
                                    {% if user.is_admin %}
                                        <span class="badge bg-danger">مدير النظام</span>
                                    {% else %}
                                        <span class="badge bg-info">مستخدم عادي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('edit_user', user_id=user.id) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-user-edit"></i> تعديل
                                        </a>
                                        <a href="{{ url_for('reset_password', user_id=user.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-key"></i> تغيير كلمة المرور
                                        </a>
                                        {% if user.is_active %}
                                            <a href="{{ url_for('toggle_user_status', user_id=user.id) }}" class="btn btn-sm btn-danger">
                                                <i class="fas fa-user-slash"></i> تعطيل
                                            </a>
                                        {% else %}
                                            <a href="{{ url_for('toggle_user_status', user_id=user.id) }}" class="btn btn-sm btn-success">
                                                <i class="fas fa-user-check"></i> تفعيل
                                            </a>
                                        {% endif %}
                                        {% if user.id != session.get('employee_id') %}
                                            <a href="{{ url_for('delete_user', user_id=user.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                                <i class="fas fa-trash"></i> حذف
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="7" class="text-center">لا يوجد مستخدمين</td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}