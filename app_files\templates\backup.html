{% extends 'base.html' %}

{% block title %}إدارة النسخ الاحتياطية{% endblock %}

{% block styles %}
<style>
    .backup-card {
        border-radius: 10px;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        margin-bottom: 20px;
    }

    .backup-card .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
        padding: 1rem 1.25rem;
        font-weight: bold;
        color: #4e73df;
    }

    .backup-info {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fc;
        border-radius: 5px;
        border-right: 4px solid #4e73df;
    }

    .backup-info p {
        margin-bottom: 5px;
    }

    .backup-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .backup-item {
        padding: 10px 15px;
        border-bottom: 1px solid #e3e6f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .backup-item:last-child {
        border-bottom: none;
    }

    .backup-item:hover {
        background-color: #f8f9fc;
    }

    .backup-date {
        font-weight: bold;
        color: #5a5c69;
    }

    .backup-size {
        color: #858796;
        font-size: 0.9rem;
    }

    .backup-actions {
        display: flex;
        gap: 5px;
    }

    .backup-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .backup-warning {
        background-color: #fff3cd;
        color: #856404;
        border-right: 4px solid #ffc107;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">إدارة النسخ الاحتياطية</h1>

    <div class="row">
        <div class="col-lg-6">
            <div class="card backup-card">
                <div class="card-header">
                    <i class="fas fa-download me-1"></i> إنشاء نسخة احتياطية
                </div>
                <div class="card-body">
                    <div class="backup-info">
                        <p><strong>النسخة الاحتياطية تشمل:</strong></p>
                        <p><i class="fas fa-database me-1"></i> قاعدة البيانات (بيانات الموظفين والإجازات)</p>
                        <p><i class="fas fa-file-alt me-1"></i> المستندات المرفقة</p>
                        <p><i class="fas fa-cog me-1"></i> إعدادات النظام</p>
                    </div>

                    <form action="{{ url_for('create_backup') }}" method="post">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-download me-1"></i> إنشاء نسخة احتياطية جديدة
                        </button>
                    </form>

                    <hr>

                    <div class="mt-3">
                        <a href="{{ url_for('reset_system') }}" class="btn btn-danger">
                            <i class="fas fa-trash-alt me-1"></i> تصفير النظام
                        </a>
                        <small class="d-block mt-2 text-muted">تصفير النظام وإعادته إلى حالته الأولية. <strong>تحذير:</strong> سيتم حذف جميع البيانات.</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card backup-card">
                <div class="card-header">
                    <i class="fas fa-upload me-1"></i> استعادة نسخة احتياطية
                </div>
                <div class="card-body">
                    <div class="backup-warning">
                        <p><strong>تحذير:</strong> استعادة نسخة احتياطية سيؤدي إلى استبدال جميع البيانات الحالية بالبيانات الموجودة في النسخة الاحتياطية.</p>
                        <p>تأكد من إنشاء نسخة احتياطية للبيانات الحالية قبل الاستعادة.</p>
                    </div>

                    <form action="{{ url_for('restore_backup') }}" method="post" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="backup_file" class="form-label">اختر ملف النسخة الاحتياطية</label>
                            <input type="file" class="form-control" id="backup_file" name="backup_file" accept=".zip" required>
                            <div class="form-text">يجب أن يكون الملف بتنسيق ZIP وتم إنشاؤه بواسطة هذا النظام.</div>
                        </div>
                        <button type="submit" class="btn btn-warning" onclick="return confirm('هل أنت متأكد من رغبتك في استعادة هذه النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.')">
                            <i class="fas fa-upload me-1"></i> استعادة النسخة الاحتياطية
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card backup-card">
                <div class="card-header">
                    <i class="fas fa-history me-1"></i> النسخ الاحتياطية السابقة
                </div>
                <div class="card-body">
                    {% if backups %}
                        <div class="backup-list">
                            {% for backup in backups %}
                                <div class="backup-item">
                                    <div>
                                        <div class="backup-date">{{ backup.date }}</div>
                                        <div class="backup-size">{{ backup.size }}</div>
                                    </div>
                                    <div class="backup-actions">
                                        <a href="{{ url_for('download_backup', filename=backup.filename) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-download"></i> تنزيل
                                        </a>
                                        <a href="{{ url_for('delete_backup', filename=backup.filename) }}" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من رغبتك في حذف هذه النسخة الاحتياطية؟')">
                                            <i class="fas fa-trash-alt"></i> حذف
                                        </a>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-folder-open fa-3x text-gray-300 mb-3"></i>
                            <p>لا توجد نسخ احتياطية سابقة</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
