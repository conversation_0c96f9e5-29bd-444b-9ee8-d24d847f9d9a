/**
 * أنماط مخصصة لتحسين مظهر التطبيق
 */

/* تحسين التباين والألوان */
:root {
    --primary-color: #0d47a1;
    --primary-dark: #002171;
    --primary-light: #5472d3;
    --secondary-color: #e65100;
    --secondary-dark: #ac1900;
    --secondary-light: #ff833a;
    --success-color: #1b5e20;
    --info-color: #006064;
    --warning-color: #e65100;
    --danger-color: #b71c1c;
    --light-color: #f5f5f5;
    --dark-color: #212121;
    --gray-color: #757575;
}

/* تحسين الخلفية */
body {
    background-color: #f0f2f5 !important;
    color: #333 !important;
}

/* تحسين البطاقات */
.card {
    border-radius: 8px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    border: none !important;
    margin-bottom: 20px;
}

.card-header {
    border-radius: 8px 8px 0 0 !important;
    font-weight: 600;
    padding: 0.75rem 1.25rem;
}

/* تصغير بطاقات لوحة التحكم */
.dashboard-card {
    max-height: 150px;
    overflow: hidden;
}

.dashboard-card .card-body {
    padding: 1rem;
}

.dashboard-card .card-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.dashboard-card .card-text {
    font-size: 0.9rem;
}

/* تحسين الأزرار */
.btn {
    border-radius: 5px;
    font-weight: 500;
    padding: 0.375rem 1rem;
}

.btn-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.btn-success {
    background-color: var(--success-color) !important;
    border-color: var(--success-color) !important;
}

.btn-info {
    background-color: var(--info-color) !important;
    border-color: var(--info-color) !important;
    color: white !important;
}

.btn-warning {
    background-color: var(--warning-color) !important;
    border-color: var(--warning-color) !important;
    color: white !important;
}

.btn-danger {
    background-color: var(--danger-color) !important;
    border-color: var(--danger-color) !important;
}

/* تحسين الجداول */
.table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #ddd;
}

.table th {
    background-color: #f0f0f0;
    font-weight: 600;
    border-bottom: 2px solid #ddd;
    color: #333;
}

.table td, .table th {
    border: 1px solid #ddd;
    padding: 0.75rem;
    vertical-align: middle;
}

/* تنسيق خلايا التاريخ في جميع الجداول */
.table td:nth-child(3),
.table td:nth-child(4),
.table td:nth-child(5) {
    width: 110px;
    max-width: 110px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.85rem;
}

/* تنسيق خاص للخلايا التي تحتوي على تواريخ */
.date-cell {
    width: 110px !important;
    max-width: 110px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    font-size: 0.85rem !important;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* تحسين مربعات الاختيار في الجداول */
.table .form-check-input {
    margin: 0 auto !important;
    display: block !important;
    float: none !important;
}

/* تحسين مربعات الاختيار في الجداول عند العرض في صفوف */
.table .form-check {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 0 !important;
}

/* إضافة حدود واضحة للخلايا التي تحتوي على مربعات اختيار */
.table td:has(.form-check-input) {
    background-color: rgba(13, 71, 161, 0.03) !important;
}

/* تصغير أيقونات التصنيف */
.classification-icon {
    font-size: 1rem !important;
    width: 24px !important;
    height: 24px !important;
}

/* تحسين النماذج */
.form-control {
    border-radius: 5px;
    border: 1px solid #ddd;
    padding: 0.5rem 0.75rem;
}

.form-control:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 0.2rem rgba(13, 71, 161, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* تحسين مربعات الاختيار */
.form-check-input {
    width: 1.25rem !important;
    height: 1.25rem !important;
    border: 2px solid #0d47a1 !important;
    border-radius: 4px !important;
    margin-top: 0.25rem !important;
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
    float: right !important;
    cursor: pointer !important;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.2) !important;
    position: relative !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    background-color: white !important;
}

.form-check-input:checked {
    background-color: #0d47a1 !important;
    border-color: #0d47a1 !important;
}

.form-check-input:checked::after {
    content: '✓';
    position: absolute;
    top: -2px;
    right: 3px;
    color: white;
    font-size: 1rem;
    font-weight: bold;
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 71, 161, 0.25) !important;
}

.form-check-label {
    cursor: pointer !important;
    padding-right: 0.5rem !important;
    font-weight: 500 !important;
    margin-right: 0.5rem !important;
}

/* تحسين مربعات الاختيار في صفحة الصلاحيات */
.permission-checkbox {
    width: 1.5rem !important;
    height: 1.5rem !important;
    border: 2px solid #e65100 !important;
    float: right !important;
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
}

.permission-checkbox:checked {
    background-color: #e65100 !important;
    border-color: #e65100 !important;
}

.permission-checkbox:checked::after {
    right: 4px !important;
}

/* تحسين مربع "جميع الصلاحيات" */
#perm_all, #all_permissions {
    width: 1.75rem !important;
    height: 1.75rem !important;
    border: 3px solid #b71c1c !important;
    float: right !important;
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
}

#perm_all:checked, #all_permissions:checked {
    background-color: #b71c1c !important;
    border-color: #b71c1c !important;
}

#perm_all:checked::after, #all_permissions:checked::after {
    right: 5px !important;
    top: -1px !important;
    font-size: 1.2rem !important;
}

/* إضافة تأثير عند تمرير المؤشر */
.form-check {
    padding: 0.5rem !important;
    border-radius: 6px !important;
    transition: background-color 0.2s !important;
}

.form-check:hover {
    background-color: rgba(13, 71, 161, 0.05) !important;
}

/* تحسين شريط التنقل */
.navbar {
    background-color: var(--primary-color) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9);
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #fff;
}

/* تحسين التنبيهات */
.alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* تحسين الشارات */
.badge {
    font-weight: 500;
    padding: 0.135em 0.65em;
    border-radius: 4px;
}

/* تحسين القوائم */
.list-group {
    border-radius: 8px;
    overflow: hidden;
}

.list-group-item {
    border-left: none;
    border-right: none;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}

/* تحسين الطباعة */
@media print {
    body {
        background-color: white !important;
    }

    .container {
        width: 100% !important;
        max-width: 100% !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    .no-print {
        display: none !important;
    }

    .table {
        width: 100% !important;
    }

    .table th, .table td {
        padding: 0.5rem !important;
    }
}
