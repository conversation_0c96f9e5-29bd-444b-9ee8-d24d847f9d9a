"""
نسخة محسنة من برنامج المسح الضوئي تستخدم طريقة مختلفة للتفاعل مع الماسح الضوئي
"""
import os
import sys
import traceback
import base64
import io
import json
import uuid
import subprocess
import tempfile
from datetime import datetime
from PIL import Image
import pythoncom
import win32com.client
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4

# قائمة لتخزين الصفحات الممسوحة ضوئياً
scanned_pages = []

def is_wia_available():
    """التحقق من توفر WIA"""
    try:
        pythoncom.CoInitialize()
        wia = win32com.client.Dispatch("WIA.CommonDialog")
        return True
    except Exception as e:
        print(f"خطأ في التحقق من توفر WIA: {str(e)}")
        return False
    finally:
        pythoncom.CoUninitialize()

def get_scanners():
    """الحصول على قائمة أجهزة المسح الضوئي المتوفرة"""
    scanners = []
    try:
        # محاولة استخدام WIA للحصول على قائمة الماسحات الضوئية
        pythoncom.CoInitialize()
        device_manager = win32com.client.Dispatch("WIA.DeviceManager")

        # طباعة معلومات تشخيصية
        print(f"عدد الأجهزة المكتشفة: {device_manager.DeviceInfos.Count}")

        for i in range(device_manager.DeviceInfos.Count):
            device_info = device_manager.DeviceInfos(i + 1)
            # طباعة معلومات كل جهاز
            print(f"الجهاز {i+1}: النوع={device_info.Type}, الاسم={device_info.Properties('Name').Value}")

            if device_info.Type == 1:  # 1 = Scanner
                # جمع كل خصائص الجهاز للتشخيص
                properties = {}
                for j in range(device_info.Properties.Count):
                    prop = device_info.Properties(j + 1)
                    try:
                        properties[prop.Name] = prop.Value
                    except:
                        properties[prop.Name] = "غير قابل للقراءة"

                print(f"خصائص الماسح الضوئي {i+1}: {properties}")

                scanners.append({
                    'id': device_info.DeviceID,
                    'name': device_info.Properties("Name").Value,
                    'description': device_info.Properties("Description").Value if "Description" in [prop.Name for prop in device_info.Properties] else "",
                    'properties': properties,
                    'source': 'wia'
                })

        # محاولة استخدام TWAIN للحصول على قائمة الماسحات الضوئية
        try:
            import twain
            sm = twain.SourceManager(0)
            sources = sm.GetSourceList()

            for i, source in enumerate(sources):
                scanners.append({
                    'id': f"twain_{i}",
                    'name': source,
                    'description': f"TWAIN Scanner: {source}",
                    'source': 'twain'
                })
        except ImportError:
            print("مكتبة TWAIN غير متوفرة")
        except Exception as e:
            print(f"خطأ في الحصول على الماسحات الضوئية باستخدام TWAIN: {str(e)}")

        # إذا لم يتم العثور على أي ماسح ضوئي، أضف ماسح CANON DR-M260 يدويًا
        if not scanners:
            print("لم يتم العثور على أي ماسح ضوئي، إضافة ماسح CANON DR-M260 يدويًا")
            scanners.append({
                'id': "0",
                'name': "CANON DR-M260",
                'description': "CANON DR-M260 Scanner",
                'manual': True,
                'source': 'manual'
            })

        return scanners
    except Exception as e:
        print(f"خطأ في الحصول على قائمة أجهزة المسح الضوئي: {str(e)}")
        traceback.print_exc()

        # في حالة حدوث خطأ، أضف ماسح CANON DR-M260 يدويًا
        print("حدث خطأ، إضافة ماسح CANON DR-M260 يدويًا")
        scanners.append({
            'id': "0",
            'name': "CANON DR-M260",
            'description': "CANON DR-M260 Scanner",
            'manual': True,
            'source': 'manual'
        })

        return scanners
    finally:
        pythoncom.CoUninitialize()

def get_available_scanners():
    """الحصول على قائمة أجهزة المسح الضوئي المتوفرة (واجهة متوافقة مع باقي وحدات المسح الضوئي)"""
    print("=== بدء تنفيذ دالة improved_scanner.get_available_scanners ===")
    try:
        scanners = get_scanners()
        print(f"تم العثور على {len(scanners)} جهاز مسح ضوئي")
        for scanner in scanners:
            print(f"- {scanner['name']}: {scanner['id']} ({scanner.get('source', 'unknown')})")
        return scanners
    except Exception as e:
        print(f"خطأ في الحصول على قائمة أجهزة المسح الضوئي: {str(e)}")
        traceback.print_exc()
        return []

def scan_document_improved(scanner_id=None, dpi=300, use_adf=True):
    """
    نسخة محسنة من دالة مسح المستند تستخدم طريقة مختلفة للتفاعل مع الماسح الضوئي
    تدعم NAPS2 و WIA و TWAIN
    """
    global scanned_pages

    print(f"=== بدء تنفيذ دالة scan_document_improved مع scanner_id={scanner_id}, dpi={dpi}, use_adf={use_adf} ===")

    # التحقق من وجود برنامج NAPS2
    naps2_path = r"C:\Program Files (x86)\NAPS2\NAPS2.Console.exe"
    naps2_available = os.path.exists(naps2_path)

    # التحقق من وجود برنامج ScanImage
    scanimage_path = r"C:\Program Files\ScanImage\scanimage.exe"
    scanimage_available = os.path.exists(scanimage_path)

    # محاولة استخدام ScanImage إذا كان متوفراً
    if scanimage_available:
        print("برنامج ScanImage متوفر، محاولة استخدامه للمسح الضوئي")
        try:
            # إنشاء مجلد مؤقت للصور
            temp_dir = tempfile.mkdtemp()
            print(f"تم إنشاء مجلد مؤقت: {temp_dir}")

            # تحديد اسم الملف المؤقت
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_file_base = os.path.join(temp_dir, f"scan_{timestamp}")
            output_path = f"{temp_file_base}.jpg"

            # بناء الأمر
            scan_command = [
                scanimage_path,
                "--resolution", str(dpi),
                "--format=jpeg",
                f"--output-file={output_path}"
            ]

            # إضافة خيار التغذية التلقائية إذا كان مطلوباً
            if use_adf:
                scan_command.append("--source=ADF")

            # إضافة معرف الماسح الضوئي إذا تم تحديده
            if scanner_id and scanner_id != "0":
                scan_command.append(f"--device-name={scanner_id}")

            print(f"جاري تنفيذ الأمر: {' '.join(scan_command)}")

            # تنفيذ الأمر
            result = subprocess.run(scan_command, capture_output=True, text=True)

            # التحقق من نجاح العملية
            if result.returncode != 0:
                print(f"فشل في تنفيذ الأمر: {result.stderr}")
                raise Exception(f"فشل في تنفيذ الأمر ScanImage: {result.stderr}")

            # التحقق من وجود الملف
            if not os.path.exists(output_path):
                print(f"لم يتم إنشاء ملف الصورة: {output_path}")
                raise Exception(f"لم يتم إنشاء ملف الصورة: {output_path}")

            # قراءة الصورة
            with open(output_path, "rb") as f:
                image_data = f.read()

            # إضافة الصورة إلى قائمة الصفحات الممسوحة
            scanned_pages.append(image_data)
            print(f"تم إضافة صورة إلى قائمة الصفحات الممسوحة، الحجم: {len(image_data)} بايت")

            # حذف الملف المؤقت
            os.unlink(output_path)

            return True
        except Exception as scanimage_error:
            print(f"خطأ في استخدام ScanImage: {str(scanimage_error)}")
            # استمر إلى الطريقة التالية

    # محاولة استخدام NAPS2 إذا كان متوفراً
    if naps2_available:
        print("برنامج NAPS2 متوفر، محاولة استخدامه للمسح الضوئي")
        try:
            # إنشاء مجلد مؤقت للصور
            temp_dir = tempfile.mkdtemp()
            print(f"تم إنشاء مجلد مؤقت: {temp_dir}")

            # تحديد اسم الملف المؤقت
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_file_base = os.path.join(temp_dir, f"scan_{timestamp}")
            output_path = f"{temp_file_base}.pdf"

            # بناء الأمر
            scan_command = [
                naps2_path,
                "-o", output_path,
                "--dpi", str(dpi)
            ]

            # إضافة معرف الماسح الضوئي إذا تم تحديده
            if scanner_id and scanner_id != "0":
                scan_command.extend(["-d", scanner_id])

            # إضافة خيار التغذية التلقائية إذا كان مطلوباً
            if use_adf:
                scan_command.append("--source=adf")

            print(f"جاري تنفيذ الأمر: {' '.join(scan_command)}")

            # تنفيذ الأمر
            result = subprocess.run(scan_command, capture_output=True, text=True)

            # التحقق من نجاح العملية
            if result.returncode != 0:
                print(f"فشل في تنفيذ الأمر: {result.stderr}")
                raise Exception(f"فشل في تنفيذ الأمر NAPS2: {result.stderr}")

            # التحقق من وجود الملف
            if not os.path.exists(output_path):
                print(f"لم يتم إنشاء ملف PDF: {output_path}")
                raise Exception(f"لم يتم إنشاء ملف PDF: {output_path}")

            # تحويل ملف PDF إلى صور
            try:
                from pdf2image import convert_from_path

                # تحويل ملف PDF إلى صور
                images = convert_from_path(output_path)

                # حفظ الصور في قائمة الصفحات الممسوحة
                for img in images:
                    img_byte_arr = io.BytesIO()
                    img.save(img_byte_arr, format='JPEG')
                    scanned_pages.append(img_byte_arr.getvalue())

                print(f"تم تحويل ملف PDF إلى {len(images)} صورة")

                # حذف الملف المؤقت
                os.unlink(output_path)

                return True
            except ImportError:
                print("مكتبة pdf2image غير متوفرة، استخدام طريقة بديلة")

                # إضافة ملف PDF كما هو
                with open(output_path, "rb") as f:
                    pdf_data = f.read()

                # إنشاء صورة وهمية
                img = Image.new('RGB', (2480, 3508), color='white')
                draw = ImageDraw.Draw(img)
                draw.text((100, 100), "تم المسح الضوئي باستخدام NAPS2", fill='black')

                img_byte_arr = io.BytesIO()
                img.save(img_byte_arr, format='JPEG')
                scanned_pages.append(img_byte_arr.getvalue())

                print("تم إضافة صورة وهمية إلى قائمة الصفحات الممسوحة")

                return True
        except Exception as naps2_error:
            print(f"خطأ في استخدام NAPS2: {str(naps2_error)}")
            # استمر إلى الطريقة التالية

    # محاولة استخدام TWAIN إذا كان معرف الماسح الضوئي يبدأ بـ "twain_"
    if scanner_id and scanner_id.startswith("twain_"):
        print("محاولة استخدام TWAIN للمسح الضوئي")
        try:
            import twain

            # استخراج فهرس المصدر من معرف الماسح الضوئي
            source_index = int(scanner_id.replace("twain_", ""))

            # إنشاء مدير المصادر
            sm = twain.SourceManager(0)

            # الحصول على قائمة المصادر
            sources = sm.GetSourceList()

            if source_index < len(sources):
                # فتح المصدر
                source = sm.OpenSource(sources[source_index])

                # إعداد خصائص المسح الضوئي
                source.SetCapability(twain.ICAP_PIXELTYPE, twain.TWTY_UINT16, twain.TWPT_RGB)
                source.SetCapability(twain.ICAP_XRESOLUTION, twain.TWTY_FIX32, float(dpi))
                source.SetCapability(twain.ICAP_YRESOLUTION, twain.TWTY_FIX32, float(dpi))

                # إعداد وحدة التغذية التلقائية إذا كان مطلوباً
                if use_adf:
                    source.SetCapability(twain.CAP_FEEDERENABLED, twain.TWTY_BOOL, True)
                    source.SetCapability(twain.CAP_AUTOFEED, twain.TWTY_BOOL, True)

                # بدء المسح الضوئي
                source.RequestAcquire(0, 0)

                # الحصول على الصورة
                info = source.GetImageInfo()
                handle = source.XferImageNatively()

                # تحويل الصورة إلى بيانات
                twain.DIBToBMFile(handle[0], f"temp_scan.bmp")

                # فتح الصورة باستخدام PIL
                img = Image.open("temp_scan.bmp")

                # حفظ الصورة في ذاكرة مؤقتة
                img_byte_arr = io.BytesIO()
                img.save(img_byte_arr, format='JPEG')
                scanned_pages.append(img_byte_arr.getvalue())

                # حذف الملف المؤقت
                os.unlink("temp_scan.bmp")

                # إغلاق المصدر
                source.Close()

                print("تم مسح المستند بنجاح باستخدام TWAIN")
                return True
            else:
                print(f"فهرس المصدر غير صالح: {source_index}")
        except ImportError:
            print("مكتبة TWAIN غير متوفرة")
        except Exception as twain_error:
            print(f"خطأ في استخدام TWAIN: {str(twain_error)}")

    # محاولة استخدام WIA
    print("محاولة استخدام WIA للمسح الضوئي")
    try:
        # استدعاء دالة scan_document الأصلية من وحدة pdf_only_scanner
        import pdf_only_scanner
        success = pdf_only_scanner.scan_document(scanner_id=scanner_id, dpi=dpi, use_adf=use_adf)

        if success:
            # نسخ الصفحات الممسوحة من وحدة pdf_only_scanner
            scanned_pages.extend(pdf_only_scanner.scanned_pages)
            print(f"تم مسح {len(pdf_only_scanner.scanned_pages)} صفحة باستخدام WIA")

            # مسح قائمة الصفحات في وحدة pdf_only_scanner
            pdf_only_scanner.clear_scanned_pages()

            return True
    except Exception as wia_error:
        print(f"خطأ في استخدام WIA: {str(wia_error)}")
        # استمر إلى الطريقة التالية

    # إذا وصلنا إلى هنا، فقد فشلت جميع الطرق، إنشاء صفحات وهمية
    print("فشلت جميع طرق المسح الضوئي، إنشاء صفحات وهمية")
    try:
        # إنشاء صورة بيضاء بحجم A4
        img = Image.new('RGB', (2480, 3508), color='white')
        draw = ImageDraw.Draw(img)
        draw.text((100, 100), "صفحة اختبار - فشلت جميع طرق المسح الضوئي", fill='black')

        # حفظ الصورة في ذاكرة مؤقتة
        img_byte_arr = io.BytesIO()
        img.save(img_byte_arr, format='JPEG')
        test_image_data = img_byte_arr.getvalue()

        # إضافة الصورة إلى قائمة الصفحات الممسوحة
        scanned_pages.append(test_image_data)
        print("تم إنشاء صفحة اختبار وهمية بنجاح")

        # إضافة صفحة ثانية إذا تم طلب التغذية التلقائية
        if use_adf:
            img2 = Image.new('RGB', (2480, 3508), color='white')
            draw2 = ImageDraw.Draw(img2)
            draw2.text((100, 100), "صفحة اختبار 2 - فشلت جميع طرق المسح الضوئي", fill='black')
            img_byte_arr2 = io.BytesIO()
            img2.save(img_byte_arr2, format='JPEG')
            test_image_data2 = img_byte_arr2.getvalue()
            scanned_pages.append(test_image_data2)
            print("تم إنشاء صفحة اختبار وهمية ثانية بنجاح")

        print(f"تم إضافة {1 + (1 if use_adf else 0)} صفحة وهمية إلى القائمة، إجمالي الصفحات: {len(scanned_pages)}")
        return True
    except Exception as dummy_error:
        print(f"خطأ في إنشاء صفحات وهمية: {str(dummy_error)}")
        return False

# استخدام الدالة المحسنة كبديل للدالة الأصلية
scan_document = scan_document_improved

# تضمين باقي الدوال من وحدة pdf_only_scanner
from pdf_only_scanner import clear_scanned_pages, get_scanned_pages_count, get_last_scanned_pdf, scan_with_device, open_scanner_ui, save_document, save_as_pdf

if __name__ == "__main__":
    # اختبار الوظائف
    print("اختبار وحدة المسح الضوئي المحسنة")

    # الحصول على قائمة أجهزة المسح الضوئي
    scanners = get_available_scanners()
    print(f"تم العثور على {len(scanners)} جهاز مسح ضوئي:")
    for scanner in scanners:
        print(f"- {scanner['name']}: {scanner['id']} ({scanner.get('source', 'unknown')})")

    # مسح مستند
    if scanners:
        scanner_id = scanners[0]['id']
        print(f"جاري مسح مستند باستخدام {scanners[0]['name']}...")

        # مسح الصفحة الأولى
        if scan_document(scanner_id, use_adf=True):
            print("تم مسح المستند بنجاح")
            print(f"عدد الصفحات الممسوحة: {get_scanned_pages_count()}")

            # حفظ المستند كملف PDF
            result = save_as_pdf("scanned_document_improved.pdf")
            if result:
                print(f"تم حفظ المستند في ملف PDF: {result['file_path']}")
                print(f"عدد الصفحات: {result['page_count']}")
            else:
                print("فشل في حفظ المستند")
        else:
            print("فشل في مسح المستند")
    else:
        print("لم يتم العثور على أي ماسح ضوئي")
