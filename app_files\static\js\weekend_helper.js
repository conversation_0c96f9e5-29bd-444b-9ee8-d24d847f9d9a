/**
 * ملف مساعد للتعامل مع أيام العطلة الأسبوعية (الجمعة والسبت)
 */

/**
 * التحقق مما إذا كان التاريخ يصادف يوم الجمعة أو السبت
 * @param {Date} date - كائن التاريخ للتحقق منه
 * @returns {boolean} - يعيد true إذا كان التاريخ يصادف يوم الجمعة أو السبت
 */
function isWeekend(date) {
    const day = date.getDay();
    // في JavaScript، 5 = الجمعة، 6 = السبت
    return day === 5 || day === 6;
}

/**
 * تعديل تاريخ العودة إذا صادف يوم الجمعة أو السبت
 * @param {Date} returnDate - كائن تاريخ العودة
 * @returns {Date} - تاريخ العودة المعدل (يوم الأحد التالي إذا كان التاريخ الأصلي يصادف الجمعة أو السبت)
 */
function adjustReturnDateForWeekend(returnDate) {
    if (!returnDate) return returnDate;
    
    const date = new Date(returnDate);
    
    // التحقق مما إذا كان التاريخ يصادف يوم الجمعة أو السبت
    if (isWeekend(date)) {
        // إذا كان يوم الجمعة (5)، أضف 2 أيام للوصول إلى الأحد
        // إذا كان يوم السبت (6)، أضف يوم واحد للوصول إلى الأحد
        const day = date.getDay();
        if (day === 5) { // الجمعة
            date.setDate(date.getDate() + 2);
        } else if (day === 6) { // السبت
            date.setDate(date.getDate() + 1);
        }
    }
    
    return date;
}

/**
 * تنسيق التاريخ بتنسيق YYYY-MM-DD
 * @param {Date} date - كائن التاريخ
 * @returns {string} - التاريخ بتنسيق YYYY-MM-DD
 */
function formatDate(date) {
    if (!date) return '';
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
}
