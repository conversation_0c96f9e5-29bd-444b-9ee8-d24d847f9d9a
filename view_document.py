from flask import Flask, render_template, redirect, url_for, send_file, abort, request
import os
from datetime import datetime

app = Flask(__name__)

@app.route('/embedded_document')
def embedded_document():
    """عرض المستند داخل البرنامج"""
    # الحصول على مسار الملف من المعاملات
    file_path = request.args.get('file_path')
    document_name = request.args.get('document_name', 'مستند')
    back_url = request.args.get('back_url', '/')
    
    if not file_path or not os.path.exists(file_path):
        abort(404)
    
    # تحديد نوع المستند
    file_ext = file_path.rsplit('.', 1)[1].lower() if '.' in file_path else ''
    
    # إنشاء روابط للملف
    document_url = f"/raw_document?file_path={file_path}"
    download_url = f"/download_document?file_path={file_path}&document_name={document_name}"
    
    return render_template('embedded_document.html',
                          document_url=document_url,
                          download_url=download_url,
                          back_url=back_url,
                          document_name=document_name,
                          document_type=file_ext)

@app.route('/raw_document')
def raw_document():
    """عرض المستند الخام"""
    file_path = request.args.get('file_path')
    
    if not file_path or not os.path.exists(file_path):
        abort(404)
    
    # تحديد نوع المحتوى
    file_ext = file_path.rsplit('.', 1)[1].lower() if '.' in file_path else ''
    mimetype = None
    
    if file_ext == 'pdf':
        mimetype = 'application/pdf'
    elif file_ext in ['jpg', 'jpeg']:
        mimetype = 'image/jpeg'
    elif file_ext == 'png':
        mimetype = 'image/png'
    
    # إرسال الملف مباشرة
    return send_file(file_path, mimetype=mimetype)

@app.route('/download_document')
def download_document():
    """تنزيل المستند"""
    file_path = request.args.get('file_path')
    document_name = request.args.get('document_name', 'document')
    
    if not file_path or not os.path.exists(file_path):
        abort(404)
    
    # إرسال الملف للتنزيل
    return send_file(file_path, as_attachment=True, download_name=document_name)

if __name__ == '__main__':
    app.run(debug=True)
