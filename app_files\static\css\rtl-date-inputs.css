/* تنسيقات حقول التاريخ بالعربية (RTL) - حل مخصص */

/* تنسيقات عامة لحقول التاريخ */
/* إصلاح اتجاه التاريخ ليصبح RTL */
input[type="date"] {
    direction: rtl !important;
    text-align: left;   /* يضع اليوم أولاً عند الرؤية */
    /* تستطيع ضبط اللون، الخط، إلخ كما في كودك السابق */
    font-family: Arial, sans-serif !important;
    background-color: #f8f9ff !important;
    border: 2px solid #d1d9ff !important;
    color: #333 !important;
    font-weight: 500 !important;
}
}

/* تنسيق الحقل المخفي */
.flatpickr-input[type="hidden"],
input[type="text"].flatpickr-input {
    display: none !important;
}

.rtl-date-input:focus,
.flatpickr-input:focus,
.date-display:hover {
    background-color: #f0f4ff !important;
    border-color: #4e73df !important;
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25) !important;
}

/* تنسيق عنصر عرض التاريخ المخصص */
.date-display {
    display: flex !important;
    align-items: center !important;
    height: calc(1.5em + 0.75rem + 2px) !important;
    cursor: pointer !important;
    transition: all 0.2s ease-in-out !important;
    padding-left: 40px !important; /* مساحة لزر التقويم */
}

.date-display i {
    margin-left: 8px !important;
    color: #4e73df !important;
}

.date-display .date-text {
    flex: 1 !important;
}

/* تنسيق زر التقويم */
.calendar-button {
    width: 40px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: #4e73df !important;
    border-color: #4e73df !important;
}

.calendar-button:hover {
    background-color: #224abe !important;
    border-color: #224abe !important;
}

/* تنسيق حاوية حقل التاريخ */
.input-group {
    position: relative !important;
}

/* تنسيقات خاصة بـ flatpickr */
.flatpickr-calendar {
    direction: rtl !important;
    font-family: Arial, sans-serif !important;
}

.flatpickr-calendar.arrowTop:before,
.flatpickr-calendar.arrowTop:after {
    right: 22px !important;
    left: auto !important;
}

.flatpickr-months {
    direction: rtl !important;
}

.flatpickr-months .flatpickr-month {
    direction: rtl !important;
}

.flatpickr-current-month {
    right: 12.5% !important;
    left: auto !important;
}

.flatpickr-months .flatpickr-prev-month {
    left: 0 !important;
    right: auto !important;
}

.flatpickr-months .flatpickr-next-month {
    right: 0 !important;
    left: auto !important;
}

.flatpickr-weekdays {
    direction: rtl !important;
}

.flatpickr-days {
    direction: rtl !important;
}

.flatpickr-day {
    direction: rtl !important;
}

/* تنسيق إضافي لحقول التاريخ بالعربية */
.rtl-date-input {
    letter-spacing: 1px;
}

/* تنسيقات لضمان عمل RTL في جميع المتصفحات */
[dir="rtl"] .flatpickr-input,
.flatpickr-input[dir="rtl"],
.flatpickr-input[lang="ar"],
.input-group .flatpickr-input,
.input-group input.flatpickr-input,
.input-group .form-control.flatpickr-input {
    direction: rtl !important;
    text-align: right !important;
    unicode-bidi: bidi-override !important;
}

/* تنسيق أيقونة التقويم */
.flatpickr-calendar-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #4e73df;
    pointer-events: none;
}

/* تنسيق حقل التاريخ داخل مجموعة الإدخال */
.input-group .flatpickr-input,
.input-group input.form-control.flatpickr-input {
    padding-left: 30px !important;
}
