import base64
import io
from PIL import Image
import json
import os
import sys
import traceback

# Importar la biblioteca wia_scan
WIA_AVAILABLE = False
WIA_ERROR_MESSAGE = ""

try:
    # Intentar importar pywin32 primero
    import win32com.client

    # Luego intentar importar wia_scan
    try:
        import wia_scan
        # Verificar que las funciones necesarias estén disponibles
        required_functions = ['list_devices', 'prompt_choose_device_and_connect', 'scan_side']
        missing_functions = [func for func in required_functions if not hasattr(wia_scan, func)]

        if missing_functions:
            WIA_ERROR_MESSAGE = f"Faltan funciones en wia_scan: {', '.join(missing_functions)}"
            print(WIA_ERROR_MESSAGE, file=sys.stderr)
        else:
            WIA_AVAILABLE = True
    except ImportError as e:
        WIA_ERROR_MESSAGE = f"Error al importar wia_scan: {str(e)}"
        print(WIA_ERROR_MESSAGE, file=sys.stderr)
    except Exception as e:
        WIA_ERROR_MESSAGE = f"Error inesperado al importar wia_scan: {str(e)}"
        print(WIA_ERROR_MESSAGE, file=sys.stderr)
        traceback.print_exc()
except ImportError as e:
    WIA_ERROR_MESSAGE = f"Error al importar win32com.client: {str(e)}"
    print(WIA_ERROR_MESSAGE, file=sys.stderr)
except Exception as e:
    WIA_ERROR_MESSAGE = f"Error inesperado al importar win32com.client: {str(e)}"
    print(WIA_ERROR_MESSAGE, file=sys.stderr)
    traceback.print_exc()

def get_available_scanners():
    """
    Obtiene una lista de los escáneres disponibles en el sistema.

    Returns:
        list: Lista de diccionarios con información de los escáneres disponibles.
              Cada diccionario contiene 'id' y 'name'.
    """
    if not WIA_AVAILABLE:
        # Si la biblioteca wia_scan no está disponible, devolver una lista vacía
        return []

    try:
        # Obtener la lista de dispositivos disponibles
        devices = wia_scan.list_devices()

        # Convertir la lista de dispositivos a un formato adecuado
        scanners = []
        for i, device in enumerate(devices):
            scanners.append({
                'id': str(i),  # Usar el índice como ID
                'name': device.name
            })

        return scanners
    except Exception as e:
        print(f"Error al obtener escáneres: {str(e)}")
        traceback.print_exc()
        return []

def scan_document(scanner_id, dpi=200, brightness=0, contrast=0, mode="RGB"):
    """
    Escanea un documento utilizando el escáner seleccionado.

    Args:
        scanner_id (str): ID del escáner seleccionado.
        dpi (int): Resolución en puntos por pulgada.
        brightness (int): Brillo (-1000 a 1000).
        contrast (int): Contraste (-1000 a 1000).
        mode (str): Modo de color ('RGB' para color, 'L' para escala de grises).

    Returns:
        str: Imagen escaneada en formato base64.
    """
    if not WIA_AVAILABLE:
        # Si la biblioteca wia_scan no está disponible, devolver None
        return None

    try:
        # Obtener la lista de dispositivos
        devices = wia_scan.list_devices()

        # Convertir scanner_id a índice
        scanner_index = int(scanner_id)

        if scanner_index < 0 or scanner_index >= len(devices):
            raise ValueError(f"ID de escáner inválido: {scanner_id}")

        # Conectar al dispositivo
        device = wia_scan.connect_device(devices[scanner_index])

        # Escanear el documento
        image = wia_scan.scan_side(
            device=device,
            dpi=dpi,
            brightness=brightness,
            contrast=contrast,
            mode=mode
        )

        # Cambiar el tamaño de la imagen para que se ajuste al tamaño A4 manteniendo la proporción
        # Tamaño A4 en píxeles a 300 DPI: 2480 × 3508 píxeles
        a4_width = 2480
        a4_height = 3508

        # Calcular la proporción para mantener la relación de aspecto
        img_ratio = image.width / image.height
        a4_ratio = a4_width / a4_height

        if img_ratio > a4_ratio:
            # La imagen es más ancha que A4
            new_width = a4_width
            new_height = int(a4_width / img_ratio)
        else:
            # La imagen es más alta que A4
            new_height = a4_height
            new_width = int(a4_height * img_ratio)

        # Redimensionar la imagen
        image = image.resize((new_width, new_height), Image.LANCZOS)

        # Crear una nueva imagen con tamaño A4 (fondo blanco)
        a4_image = Image.new('RGB', (a4_width, a4_height), (255, 255, 255))

        # Calcular la posición para centrar la imagen
        x_offset = (a4_width - new_width) // 2
        y_offset = (a4_height - new_height) // 2

        # Pegar la imagen en el centro
        a4_image.paste(image, (x_offset, y_offset))

        # Usar la nueva imagen
        image = a4_image

        # Convertir la imagen a base64 con mayor compresión
        buffered = io.BytesIO()
        # Usar calidad más baja (60 en lugar de 85) para reducir significativamente el tamaño
        image.save(buffered, format="JPEG", quality=60, optimize=True, progressive=True)
        img_str = base64.b64encode(buffered.getvalue()).decode()

        return f"data:image/jpeg;base64,{img_str}"
    except Exception as e:
        print(f"Error al escanear documento: {str(e)}")
        traceback.print_exc()
        return None

def is_wia_available():
    """
    Verifica si la biblioteca wia_scan está disponible.

    Returns:
        bool: True si la biblioteca está disponible, False en caso contrario.
    """
    return WIA_AVAILABLE

def get_wia_error_message():
    """
    Obtiene el mensaje de error si la biblioteca wia_scan no está disponible.

    Returns:
        str: Mensaje de error o cadena vacía si no hay error.
    """
    return WIA_ERROR_MESSAGE
