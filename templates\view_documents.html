{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2 class="mb-0">مستندات الإجازة</h2>
            <div>
                <span class="badge bg-primary">الموظف: {{ employee.full_name }}</span>
                <span class="badge bg-info">نوع الإجازة: {{ leave.leave_type.name if leave.leave_type else 'غير محدد' }}</span>
            </div>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <a href="{{ url_for('upload_document', leave_id=leave.id) }}" class="btn btn-primary">
                    <i class="fas fa-upload me-1"></i> رفع مستند جديد
                </a>
                <a href="{{ url_for('scan_document_page', leave_id=leave.id) }}" class="btn btn-secondary">
                    <i class="fas fa-scanner me-1"></i> مسح مستند جديد
                </a>
                <a href="{{ url_for('employee_leaves', employee_id=employee.id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i> عودة إلى إجازات الموظف
                </a>
            </div>

            {% if documents %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>#</th>
                                <th>اسم المستند</th>
                                <th>نوع الملف</th>
                                <th>تاريخ الرفع</th>
                                <th>الوصف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for document in documents %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ document.original_filename }}</td>
                                <td>
                                    {% if document.file_type == 'pdf' %}
                                        <span class="badge bg-danger">PDF</span>
                                    {% elif document.file_type in ['jpg', 'jpeg', 'png', 'gif'] %}
                                        <span class="badge bg-success">صورة</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ document.file_type }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ document.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>{{ document.description or '-' }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('raw_document_by_document_id', document_id=document.id) }}" class="btn btn-sm btn-info" target="_blank">
                                            <i class="fas fa-eye me-1"></i> عرض
                                        </a>
                                        <a href="{{ url_for('download_document', document_id=document.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-download me-1"></i> تنزيل
                                        </a>
                                        <a href="{{ url_for('delete_document', document_id=document.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المستند؟')">
                                            <i class="fas fa-trash me-1"></i> حذف
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> لا توجد مستندات مرفقة بهذه الإجازة.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
