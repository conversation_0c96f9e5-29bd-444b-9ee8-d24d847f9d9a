import os
from app import app, db
from models import Employee, LeaveType
from datetime import datetime

def migrate_database():
    with app.app_context():
        # حذف قاعدة البيانات القديمة
        try:
            db.drop_all()
            print("تم حذف قاعدة البيانات القديمة")
        except Exception as e:
            print(f"خطأ في حذف قاعدة البيانات: {str(e)}")
            return

        # إنشاء قاعدة البيانات الجديدة
        try:
            db.create_all()
            print("تم إنشاء قاعدة البيانات الجديدة")

            # إضافة موظف تجريبي
            test_employee = Employee(
                username='admin',
                full_name='موظف تجريبي',
                work_location='المكتب الرئيسي',
                job_title='موظف',
                leave_balance=30,
                children_count=0
            )
            db.session.add(test_employee)

            # إضافة أنواع الإجازات
            leave_types = [
                {'name': 'إجازة اعتيادية', 'days_allowed': 30},
                {'name': 'إجازة مرضية', 'days_allowed': 30},
                {'name': 'إجازة المعيل', 'days_allowed': 15},
                {'name': 'إجازة خمس سنوات', 'days_allowed': 60},
                {'name': 'إجازة بدون راتب', 'days_allowed': None},
                {'name': 'إجازة ما قبل الوضع', 'days_allowed': 21},
                {'name': 'إجازة ما بعد الوضع', 'days_allowed': 51},
                {'name': 'إجازة أمومة', 'days_allowed': 72}
            ]
            
            for leave_type in leave_types:
                lt = LeaveType(**leave_type)
                db.session.add(lt)

            db.session.commit()
            print("تم إضافة البيانات الأولية بنجاح")

        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {str(e)}")
            db.session.rollback()

if __name__ == '__main__':
    # تأكد من إغلاق التطبيق قبل تشغيل الترحيل
    if os.path.exists('leaves.db'):
        try:
            os.remove('leaves.db')
            print("تم حذف ملف قاعدة البيانات القديم")
        except Exception as e:
            print(f"خطأ في حذف ملف قاعدة البيانات: {str(e)}")
    
    migrate_database()


