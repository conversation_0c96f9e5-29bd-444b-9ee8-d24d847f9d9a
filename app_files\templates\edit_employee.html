{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
            <h2 class="mb-0">
                <i class="fas fa-user-edit me-2"></i> تعديل بيانات الموظف
            </h2>
            <span class="badge bg-light text-dark">{{ employee.full_name }}</span>
        </div>
        <div class="card-body">
            <form method="POST">


                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="full_name" class="form-label">الاسم الكامل:</label>
                        <input type="text" id="full_name" name="full_name" class="form-control" value="{{ employee.full_name }}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="employee_number" class="form-label">الرقم الوظيفي:</label>
                        <input type="text" id="employee_number" name="employee_number" class="form-control" value="{{ employee.employee_number or '' }}">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="job_title" class="form-label">المسمى الوظيفي:</label>
                        <input type="text" id="job_title" name="job_title" class="form-control" value="{{ employee.job_title }}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="work_location" class="form-label">مكان العمل:</label>
                        <input type="text" id="work_location" name="work_location" class="form-control" value="{{ employee.work_location }}" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="department" class="form-label">القسم:</label>
                        <input type="text" id="department" name="department" class="form-control" value="{{ employee.department or '' }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="leave_balance" class="form-label">رصيد الإجازات:</label>
                        <input type="number" id="leave_balance" name="leave_balance" class="form-control" value="{{ employee.leave_balance }}" min="0" required>
                    </div>
                </div>

                <div class="mb-3 form-check">
                    <input type="checkbox" id="is_admin" name="is_admin" class="form-check-input" {% if employee.is_admin %}checked{% endif %}>
                    <label for="is_admin" class="form-check-label">مدير النظام</label>
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-1"></i> حفظ التغييرات
                    </button>
                    <a href="{{ url_for('employees') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
