{% extends 'base.html' %}

{% block title %}عرض المستند{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .document-container {
        width: 100%;
        height: calc(100vh - 200px);
        min-height: 500px;
        border: 1px solid #ddd;
        border-radius: 5px;
        overflow: hidden;
        margin-bottom: 20px;
        background-color: #f8f9fa;
    }
    
    .document-header {
        background-color: #0d47a1;
        color: white;
        padding: 10px 20px;
        border-radius: 5px 5px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .document-header h3 {
        margin: 0;
        font-size: 1.2rem;
    }
    
    .document-viewer {
        width: 100%;
        height: 100%;
        border: none;
    }
    
    .document-actions {
        margin-top: 15px;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
    
    .image-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        background-color: #f0f0f0;
    }
    
    .image-container img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h2>عرض المستند</h2>
        </div>
        <div class="card-body">
            <div class="document-container">
                <div class="document-header">
                    <h3>{{ document_name }}</h3>
                </div>
                
                {% if document_type == 'pdf' %}
                    <iframe class="document-viewer" src="{{ document_url }}" type="application/pdf"></iframe>
                {% elif document_type in ['jpg', 'jpeg', 'png'] %}
                    <div class="image-container">
                        <img src="{{ document_url }}" alt="{{ document_name }}">
                    </div>
                {% else %}
                    <div class="p-5 text-center">
                        <p class="mb-4">لا يمكن عرض هذا النوع من الملفات مباشرة. يرجى تنزيل الملف.</p>
                        <a href="{{ download_url }}" class="btn btn-primary" download>
                            <i class="fas fa-download"></i> تنزيل الملف
                        </a>
                    </div>
                {% endif %}
            </div>
            
            <div class="document-actions">
                <a href="{{ download_url }}" class="btn btn-primary" download>
                    <i class="fas fa-download"></i> تنزيل
                </a>
                <a href="{{ back_url }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> رجوع
                </a>
            </div>
            
            <div class="mt-3">
                <h5>معلومات المستند:</h5>
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>اسم الملف:</span>
                        <span>{{ document_name }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>نوع الملف:</span>
                        <span>{{ document_type }}</span>
                    </li>
                    {% if upload_date %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>تاريخ الرفع:</span>
                        <span>{{ upload_date }}</span>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
{% endblock %}
