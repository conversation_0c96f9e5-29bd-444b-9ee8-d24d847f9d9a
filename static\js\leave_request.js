// استيراد دوال مساعدة للتعامل مع أيام العطلة
// يجب إضافة ملف weekend_helper.js في صفحات HTML التي تستخدم هذا الملف

// دوال احتياطية في حالة عدم وجود ملف weekend_helper.js
if (typeof isWeekend !== 'function') {
    function isWeekend(date) {
        const day = date.getDay();
        return day === 5 || day === 6; // 5 = الجمعة، 6 = السبت
    }
}

if (typeof adjustReturnDateForWeekend !== 'function') {
    function adjustReturnDateForWeekend(returnDate) {
        if (!returnDate) return returnDate;

        const date = new Date(returnDate);

        if (isWeekend(date)) {
            const day = date.getDay();
            if (day === 5) { // الجمعة
                date.setDate(date.getDate() + 2);
            } else if (day === 6) { // السبت
                date.setDate(date.getDate() + 1);
            }
        }

        return date;
    }
}

if (typeof formatDate !== 'function') {
    function formatDate(date) {
        if (!date) return '';

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const leaveTypeSelect = document.getElementById('leave_type_id');
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const daysCountInput = document.getElementById('days-count');
    const departureDateInput = document.getElementById('departure-date');
    const returnDateInput = document.getElementById('return-date');

    // إضافة سمة lang للحقول لضمان عرض التاريخ بشكل صحيح
    startDateInput.setAttribute('lang', 'en');
    endDateInput.setAttribute('lang', 'en');
    departureDateInput.setAttribute('lang', 'en');
    returnDateInput.setAttribute('lang', 'en');

    function calculateEndDate(startDate, days) {
        if (!startDate || !days) return '';
        const start = new Date(startDate);
        const end = new Date(start);
        end.setDate(start.getDate() + parseInt(days) - 1);

        // تنسيق التاريخ بالشكل الصحيح YYYY-MM-DD
        const year = end.getFullYear();
        const month = String(end.getMonth() + 1).padStart(2, '0');
        const day = String(end.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    function updateEndDate() {
        const startDate = startDateInput.value;
        const daysCount = daysCountInput.value;

        if (startDate && daysCount) {
            // حساب تاريخ النهاية
            endDateInput.value = calculateEndDate(startDate, daysCount);

            // اقتراح تاريخ الانفكاك (نفس تاريخ البداية)
            if (!departureDateInput.value) {
                departureDateInput.value = startDate;
            }

            // اقتراح تاريخ المباشرة (اليوم التالي لتاريخ النهاية)
            const endDate = new Date(endDateInput.value);
            let returnDate = new Date(endDate);
            returnDate.setDate(endDate.getDate() + 1);

            // التحقق مما إذا كان تاريخ العودة يصادف يوم الجمعة أو السبت
            // وتعديله إلى يوم الأحد التالي إذا كان كذلك
            returnDate = adjustReturnDateForWeekend(returnDate);

            if (!returnDateInput.value) {
                // تنسيق التاريخ بالشكل الصحيح YYYY-MM-DD
                returnDateInput.value = formatDate(returnDate);
            }
        }
    }

    function handleLeaveTypeChange() {
        const leaveTypeId = parseInt(leaveTypeSelect.value);
        const selectedOption = leaveTypeSelect.options[leaveTypeSelect.selectedIndex];
        const daysAllowed = selectedOption.getAttribute('data-days');

        // إذا كان نوع الإجازة له عدد أيام محدد
        if (daysAllowed && !isNaN(parseInt(daysAllowed))) {
            daysCountInput.value = daysAllowed;
            daysCountInput.readOnly = true;
        } else {
            daysCountInput.readOnly = false;
        }

        // تحديث تاريخ النهاية
        updateEndDate();
    }

    // تحديث عند تغيير نوع الإجازة
    leaveTypeSelect.addEventListener('change', handleLeaveTypeChange);

    // تحديث عند تغيير تاريخ البداية
    startDateInput.addEventListener('change', updateEndDate);

    // تحديث عند تغيير عدد الأيام
    daysCountInput.addEventListener('change', updateEndDate);
    daysCountInput.addEventListener('input', updateEndDate);

    // تشغيل عند تحميل الصفحة للتأكد من الحالة الأولية
    handleLeaveTypeChange();
});


