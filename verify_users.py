from app import app, db
from models import Employee

def verify_and_create_admin():
    with app.app_context():
        try:
            # إنشاء جميع الجداول
            db.create_all()
            print("تم إنشاء جداول قاعدة البيانات")

            # التحقق من وجود حساب المدير
            admin = Employee.query.filter_by(username='admin').first()
            if admin:
                print("حساب المدير موجود بالفعل")
            else:
                # إنشاء مستخدم جديد
                admin = Employee(
                    username='admin',
                    full_name='مدير النظام',
                    is_admin=True,
                    permissions='all',
                    work_location='المكتب الرئيسي',
                    job_title='مدير النظام',
                    leave_balance=30
                )
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                print("تم إنشاء حساب المدير بنجاح")

            # عرض تفاصيل الحساب
            print("\nتفاصيل حساب المدير:")
            print(f"اسم المستخدم: admin")
            print(f"كلمة المرور: admin123")
            
        except Exception as e:
            print(f"حدث خطأ: {str(e)}")
            db.session.rollback()

if __name__ == '__main__':
    verify_and_create_admin()

