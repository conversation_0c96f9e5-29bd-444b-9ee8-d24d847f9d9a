{% extends 'base.html' %}

{% block title %}مسح مستند{% endblock %}

{% block styles %}
<style>
    .card {
        border: 1px solid #0d6efd;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .card-header {
        background-color: #0d6efd;
        color: white;
        font-weight: bold;
    }
    .scanner-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid #dee2e6;
    }
    .btn-scanner {
        background-color: #0d6efd;
        color: white;
        font-weight: bold;
        padding: 10px 20px;
    }
    .btn-save {
        background-color: #198754;
        color: white;
        font-weight: bold;
        padding: 10px 20px;
    }
    .alert-pdf {
        background-color: #f8d7da;
        border-color: #f5c2c7;
        color: #842029;
    }
    .table-documents {
        background-color: white;
    }
    .table-documents th {
        background-color: #0d6efd;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">مسح مستند لإجازة {{ employee.full_name }}</h5>
                        <a href="{{ url_for('employee_leaves', employee_id=leave.employee_id) }}" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-right"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- حاوية التنبيهات الداخلية -->
                    <div id="alerts-container-inner"></div>

                    <!-- معلومات الموظف والإجازة -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title mb-3">معلومات الموظف</h5>
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>الاسم:</span>
                                            <strong>{{ employee.full_name }}</strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>الرقم الوظيفي:</span>
                                            <strong>{{ employee.employee_number or '-' }}</strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>المسمى الوظيفي:</span>
                                            <strong>{{ employee.job_title }}</strong>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title mb-3">معلومات الإجازة</h5>
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>نوع الإجازة:</span>
                                            <strong>{{ leave.leave_type.name }}</strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>تاريخ البداية:</span>
                                            <strong>{{ leave.start_date.strftime('%Y-%m-%d') }}</strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>تاريخ النهاية:</span>
                                            <strong>{{ leave.end_date.strftime('%Y-%m-%d') }}</strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>المدة:</span>
                                            <strong>{{ leave.days_count }} يوم</strong>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قسم المسح الضوئي -->
                    <div class="scanner-section">
                        <!-- قسم اختيار الماسح الضوئي -->
                        <div class="mb-4">
                            <h5 class="text-primary">اختيار جهاز المسح الضوئي</h5>
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="input-group">
                                        <select id="scanner-select" class="form-select form-select-lg">
                                            <option value="" disabled selected>-- يرجى الاختيار --</option>
                                        </select>
                                        <button id="refresh-scanners" class="btn btn-outline-primary" type="button">
                                            <i class="fas fa-sync-alt"></i> تحديث
                                        </button>
                                    </div>
                                    <div id="scanners-loading" class="mt-2">
                                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <span class="ms-2">جاري البحث عن أجهزة المسح الضوئي...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- قسم إعدادات المسح الضوئي -->
                        <div class="mb-4">
                            <h5 class="text-primary">إعدادات المسح الضوئي</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="dpi-select" class="form-label">الدقة (DPI)</label>
                                        <select id="dpi-select" class="form-select">
                                            <option value="100">100 DPI</option>
                                            <option value="200">200 DPI</option>
                                            <option value="300" selected>300 DPI</option>
                                            <option value="600">600 DPI</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3 form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="use-adf" checked>
                                        <label class="form-check-label" for="use-adf">استخدام التغذية التلقائية (ADF)</label>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات PDF فقط -->
                            <div class="alert alert-pdf">
                                <i class="fas fa-file-pdf"></i> <strong>ملاحظة:</strong> سيتم مسح المستند وحفظه بصيغة PDF فقط. يمكنك إضافة صفحات متعددة للمستند.
                            </div>
                        </div>

                        <!-- زر بدء المسح الضوئي -->
                        <div class="mb-4">
                            <button id="scan-button" class="btn btn-scanner btn-lg" disabled>
                                <i class="fas fa-scanner me-2"></i> بدء المسح الضوئي
                            </button>
                            <div id="scanning-progress" class="mt-2 alert alert-info" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                        <span class="visually-hidden">جاري المسح الضوئي...</span>
                                    </div>
                                    <span>جاري المسح الضوئي، يرجى الانتظار حتى اكتمال العملية...</span>
                                </div>
                            </div>
                        </div>

                        <!-- قسم نتائج المسح الضوئي -->
                        <div id="scan-results" style="display: none;">
                            <div class="mb-3">
                                <p>تم مسح <span id="scanned-pages-count">0</span> صفحة بنجاح.</p>
                                <button id="add-more-pages-button" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-plus"></i> إضافة صفحات أخرى
                                </button>
                            </div>

                            <!-- قسم حفظ المستند -->
                            <div class="mb-4">
                                <h5 class="text-primary">حفظ المستند</h5>
                                <div class="mb-3">
                                    <label for="document-description" class="form-label">وصف المستند</label>
                                    <input type="text" class="form-control" id="document-description" placeholder="أدخل وصفاً للمستند">
                                </div>
                                <button id="save-document-button" class="btn btn-save btn-lg" disabled data-leave-id="{{ leave_id }}">
                                    <i class="fas fa-save me-2"></i> حفظ المستند
                                </button>
                                <div id="saving-progress" class="mt-2 alert alert-success" style="display: none;">
                                    <div class="d-flex align-items-center">
                                        <div class="spinner-border spinner-border-sm text-success me-2" role="status">
                                            <span class="visually-hidden">جاري الحفظ...</span>
                                        </div>
                                        <span>جاري حفظ المستند، يرجى الانتظار حتى اكتمال العملية...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قسم المستندات الحالية -->
                    <div class="mb-4">
                        <h5 class="text-primary">المستندات الحالية</h5>
                        {% if documents %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover table-documents">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الوصف</th>
                                        <th>نوع الملف</th>
                                        <th>تاريخ الرفع</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for document in documents %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>{{ document.description or 'بدون وصف' }}</td>
                                        <td>{{ document.file_type }}</td>
                                        <td>{{ document.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ url_for('view_document', document_id=document.id) }}" class="btn btn-sm btn-primary" target="_blank">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                                <a href="{{ url_for('download_document', document_id=document.id) }}" class="btn btn-sm btn-secondary">
                                                    <i class="fas fa-download"></i> تنزيل
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="if(confirm('هل أنت متأكد من حذف هذا المستند؟')) window.location.href='{{ url_for('delete_document', document_id=document.id) }}'">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            لا توجد مستندات مرفقة بهذه الإجازة.
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- إضافة حاوية للتنبيهات -->
<div id="alerts-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

<script>
    // متغيرات عامة
    let scannersList = [];
    let selectedScannerId = null;
    let selectedScannerSource = 'pdf_only';
    let debugMode = true;

    // تحديث قائمة أجهزة المسح الضوئي
    function updateScannersList() {
        // إظهار مؤشر التحميل
        $('#scanners-loading').show();
        $('#scanners-list').hide();

        if (debugMode) {
            console.log("جاري تحديث قائمة أجهزة المسح الضوئي...");
        }

        // البحث عن أجهزة المسح الضوئي بدون إظهار رسائل غير ضرورية

        // الحصول على قائمة أجهزة المسح الضوئي
        $.ajax({
            url: '/get_available_scanners_api',
            type: 'GET',
            success: function(response) {
                // إخفاء مؤشر التحميل
                $('#scanners-loading').hide();
                $('#scanners-list').show();

                if (debugMode) {
                    console.log("استجابة الخادم:", response);
                }

                if (response.success) {
                    scannersList = response.scanners;

                    if (debugMode) {
                        console.log("تم استلام قائمة الماسحات الضوئية:", scannersList);
                        console.log("عدد الماسحات الضوئية:", scannersList.length);
                    }

                    // تفريغ القائمة
                    $('#scanner-select').empty();
                    $('#scanner-select').append(
                        $('<option></option>')
                            .val('')
                            .text('-- يرجى الاختيار --')
                            .attr('disabled', true)
                            .attr('selected', true)
                    );

                    // إضافة الخيارات
                    if (scannersList && scannersList.length > 0) {
                        // تم العثور على أجهزة مسح ضوئي

                        // إضافة كل ماسح ضوئي إلى القائمة
                        scannersList.forEach(function(scanner, index) {
                            let source = scanner.source || 'unknown';
                            let sourceLabel = '';

                            if (source === 'vuescan') {
                                sourceLabel = ' (VueScan)';
                            } else if (source === 'pdf_only') {
                                sourceLabel = ' (PDF)';
                            } else if (source === 'direct_wia') {
                                sourceLabel = ' (WIA)';
                            } else if (source === 'wia') {
                                sourceLabel = ' (WIA)';
                            } else if (source === 'detect_scanners') {
                                sourceLabel = ' (Auto)';
                            } else if (source === 'wmi') {
                                sourceLabel = ' (WMI)';
                            } else if (source === 'device_manager') {
                                sourceLabel = ' (Device Manager)';
                            } else if (source === 'twain') {
                                sourceLabel = ' (TWAIN)';
                            } else if (source === 'manual') {
                                sourceLabel = ' (Manual)';
                            }

                            if (debugMode) {
                                console.log(`إضافة ماسح ضوئي: ${scanner.name}, المصدر: ${source}, المعرف: ${scanner.id}`);
                            }

                            // إضافة الماسح الضوئي إلى القائمة
                            try {
                                $('#scanner-select').append(
                                    $('<option></option>')
                                        .val(index)
                                        .text(scanner.name + sourceLabel)
                                        .attr('data-source', source)
                                        .attr('data-id', scanner.id)
                                );

                                if (debugMode) {
                                    console.log(`تمت إضافة الماسح الضوئي ${scanner.name} إلى القائمة`);
                                }
                            } catch (error) {
                                console.error(`خطأ في إضافة الماسح الضوئي ${scanner.name} إلى القائمة:`, error);
                            }
                        });

                        // تفعيل زر المسح الضوئي
                        $('#scan-button').prop('disabled', false);

                        // تم تحديث قائمة الماسحات الضوئية
                    } else {
                        if (debugMode) {
                            console.log("لا توجد ماسحات ضوئية متوفرة");
                        }

                        // لم يتم العثور على أي جهاز مسح ضوئي
                        showAlert('warning', 'لم يتم العثور على أي جهاز مسح ضوئي');

                        $('#scanner-select').append(
                            $('<option></option>')
                                .val('')
                                .text('لا توجد أجهزة مسح ضوئي متوفرة')
                        );

                        // تعطيل زر المسح الضوئي
                        $('#scan-button').prop('disabled', true);
                    }
                } else {
                    console.error("خطأ في استجابة الخادم:", response.error);
                    showAlert('danger', response.error || 'حدث خطأ أثناء الحصول على قائمة أجهزة المسح الضوئي');
                }
            },
            error: function(xhr, status, error) {
                // إخفاء مؤشر التحميل
                $('#scanners-loading').hide();

                console.error("خطأ في الاتصال بالخادم:", error);
                console.error("الحالة:", status);
                console.error("استجابة الخادم:", xhr.responseText);

                showAlert('danger', 'حدث خطأ أثناء الاتصال بالخادم: ' + error);
            }
        });
    }

    // تحديث معلومات الماسح الضوئي المحدد
    function updateSelectedScanner() {
        const selectedIndex = $('#scanner-select').val();

        if (debugMode) {
            console.log("تحديث معلومات الماسح الضوئي المحدد، الفهرس:", selectedIndex);
        }

        if (selectedIndex !== null && selectedIndex !== '' && scannersList && scannersList.length > 0) {
            const scanner = scannersList[selectedIndex];

            if (debugMode) {
                console.log("الماسح الضوئي المحدد:", scanner);
            }

            // تعيين معرف الماسح الضوئي
            if (scanner.id) {
                selectedScannerId = scanner.id;
            } else if (scanner.device_id) {
                // استخدام device_id إذا كان id غير موجود
                selectedScannerId = scanner.device_id;
            } else {
                // استخدام القيمة الافتراضية إذا لم يكن هناك معرف
                selectedScannerId = "0";
            }

            // تعيين المصدر
            if (scanner.source) {
                selectedScannerSource = scanner.source;
            } else {
                // استخدام القيمة الافتراضية إذا لم يكن هناك مصدر
                selectedScannerSource = 'pdf_only';
            }

            if (debugMode) {
                console.log('تم تحديد المصدر:', selectedScannerSource);
                console.log('تم تحديد الماسح الضوئي:', scanner.name, 'المعرف:', selectedScannerId, 'المصدر:', selectedScannerSource);
            }

            // تم تحديد الماسح الضوئي

            // تفعيل زر المسح الضوئي
            $('#scan-button').prop('disabled', false);

            // إظهار نتائج المسح الضوئي
            $('#scan-results').show();
        } else {
            // إذا لم يتم تحديد أي ماسح ضوئي، استخدم القيمة الافتراضية
            selectedScannerId = "0";
            selectedScannerSource = 'pdf_only';

            if (debugMode) {
                console.log('لم يتم تحديد أي ماسح ضوئي، استخدام القيمة الافتراضية:', selectedScannerId);
            }

            // لم يتم تحديد أي ماسح ضوئي

            // تعطيل زر المسح الضوئي
            $('#scan-button').prop('disabled', true);

            // إخفاء نتائج المسح الضوئي
            $('#scan-results').hide();
        }
    }

    // بدء المسح الضوئي
    function startScanning() {
        // التأكد من وجود معرف للماسح الضوئي
        if (!selectedScannerId) {
            // استخدام القيمة الافتراضية إذا لم يكن هناك معرف
            selectedScannerId = "0";
            if (debugMode) {
                console.log("لم يتم تحديد جهاز المسح الضوئي، استخدام القيمة الافتراضية:", selectedScannerId);
            }
        }

        if (debugMode) {
            console.log("بدء المسح الضوئي باستخدام الماسح:", selectedScannerId, "المصدر:", selectedScannerSource);
        }

        // بدء المسح الضوئي
        showAlert('info', 'جاري بدء المسح الضوئي...');

        // إظهار مؤشر التحميل
        $('#scanning-progress').show();
        $('#scan-button').prop('disabled', true);
        $('#scanner-select').prop('disabled', true);

        // الحصول على إعدادات المسح الضوئي
        const dpi = $('#dpi-select').val() || "300";
        const useAdf = $('#use-adf').prop('checked');

        if (debugMode) {
            console.log("إعدادات المسح الضوئي:", {
                dpi: dpi,
                useAdf: useAdf
            });
        }

        // إعداد بيانات الطلب
        const requestData = {
            device_id: selectedScannerId,
            scanner_source: selectedScannerSource,
            dpi: parseInt(dpi),
            use_adf: useAdf
        };

        if (debugMode) {
            console.log("بيانات الطلب:", requestData);
        }

        // إرسال طلب المسح الضوئي

        // إرسال طلب المسح الضوئي
        $.ajax({
            url: '/scan_with_device_api',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(requestData),
            success: function(response) {
                // إخفاء مؤشر التحميل
                $('#scanning-progress').hide();
                $('#scan-button').prop('disabled', false);
                $('#scanner-select').prop('disabled', false);

                if (debugMode) {
                    console.log("استجابة المسح الضوئي:", response);
                }

                if (response.success) {
                    // تحديث عدد الصفحات الممسوحة
                    updateScannedPagesCount(response.page_count);
                    showAlert('success', response.message || 'تم مسح المستند بنجاح');

                    // إظهار نتائج المسح الضوئي
                    $('#scan-results').show();
                } else {
                    console.error("خطأ في المسح الضوئي:", response.error);
                    showAlert('danger', response.error || 'حدث خطأ أثناء المسح الضوئي');

                    // إضافة رسالة تشخيصية إضافية في واجهة المستخدم
                    if (response.details) {
                        showAlert('warning', `تفاصيل الخطأ: ${response.details}`);
                    }
                }
            },
            error: function(xhr, status, error) {
                // إخفاء مؤشر التحميل
                $('#scanning-progress').hide();
                $('#scan-button').prop('disabled', false);
                $('#scanner-select').prop('disabled', false);

                console.error("خطأ في الاتصال بالخادم:", error);
                console.error("الحالة:", status);
                console.error("استجابة الخادم:", xhr.responseText);

                showAlert('danger', 'حدث خطأ أثناء الاتصال بالخادم: ' + error);
            }
        });
    }

    // تحديث عدد الصفحات الممسوحة
    function updateScannedPagesCount(count) {
        $('#scanned-pages-count').text(count);

        // تفعيل زر الحفظ إذا كان هناك صفحات ممسوحة
        if (count > 0) {
            $('#save-document-button').prop('disabled', false);
        } else {
            $('#save-document-button').prop('disabled', true);
        }
    }

    // عرض رسالة تنبيه
    function showAlert(type, message) {
        // إزالة التنبيهات السابقة من نفس النوع لتجنب التكرار
        $('.alert-' + type).alert('close');

        // إنشاء تنبيه جديد
        const alertDiv = $('<div>')
            .addClass('alert alert-' + type + ' alert-dismissible fade show')
            .attr('role', 'alert')
            .html(message + '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>');

        // إضافة التنبيه إلى الحاوية الرئيسية فقط (وليس الداخلية)
        $('#alerts-container').append(alertDiv);

        // إخفاء التنبيه تلقائياً بعد 3 ثوانٍ
        setTimeout(function() {
            alertDiv.alert('close');
        }, 3000);
    }

    // حفظ المستند الممسوح ضوئياً
    function saveScannedDocument() {
        // التحقق من وجود مستند ممسوح ضوئياً
        const pagesCount = parseInt($('#scanned-pages-count').text());
        if (pagesCount <= 0) {
            showAlert('warning', 'لا توجد صفحات ممسوحة للحفظ');
            return;
        }

        // الحصول على وصف المستند
        const description = $('#document-description').val();

        // الحصول على معرف الإجازة من زر الحفظ
        const leaveId = $('#save-document-button').data('leave-id');
        if (!leaveId) {
            showAlert('danger', 'لم يتم تحديد معرف الإجازة');
            return;
        }

        // إظهار مؤشر التحميل
        $('#saving-progress').show();
        $('#save-document-button').prop('disabled', true);

        // إعداد بيانات الطلب - استخدام PDF فقط
        let requestData = {
            description: description,
            scan_method: 'pdf_only'
        };

        if (debugMode) {
            console.log("إرسال طلب حفظ المستند:", requestData);
        }

        // حفظ المستند
        showAlert('info', 'جاري حفظ المستند...');

        // إرسال طلب حفظ المستند
        $.ajax({
            url: '/save_scanned_document/' + leaveId,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(requestData),
            success: function(response) {
                // إخفاء مؤشر التحميل
                $('#saving-progress').hide();

                if (response.success) {
                    showAlert('success', 'تم حفظ المستند بنجاح');
                    if (debugMode) {
                        console.log("تم حفظ المستند بنجاح:", response);
                    }

                    // إعادة تعيين عداد الصفحات
                    $('#scanned-pages-count').text('0');
                    $('#save-document-button').prop('disabled', true);

                    // إعادة تحميل الصفحة بعد ثانيتين
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    $('#save-document-button').prop('disabled', false);
                    console.error("خطأ في حفظ المستند:", response.error);
                    showAlert('danger', response.error || 'حدث خطأ أثناء حفظ المستند');
                }
            },
            error: function(xhr, status, error) {
                // إخفاء مؤشر التحميل
                $('#saving-progress').hide();
                $('#save-document-button').prop('disabled', false);

                console.error("خطأ في الاتصال بالخادم:", error);
                console.error("استجابة الخادم:", xhr.responseText);

                showAlert('danger', 'حدث خطأ أثناء الاتصال بالخادم: ' + error);
            }
        });
    }

    // تهيئة الصفحة
    $(document).ready(function() {
        if (debugMode) {
            console.log("تهيئة صفحة المسح الضوئي...");
        }

        // تهيئة صفحة المسح الضوئي بدون إظهار رسائل غير ضرورية

        try {
            // تحديث قائمة أجهزة المسح الضوئي عند تحميل الصفحة
            updateScannersList();

            // تحديث معلومات الماسح الضوئي المحدد عند تغيير الاختيار
            $('#scanner-select').on('change', function() {
                if (debugMode) {
                    console.log("تم تغيير اختيار الماسح الضوئي");
                }
                updateSelectedScanner();
            });

            // بدء المسح الضوئي عند النقر على زر المسح
            $('#scan-button').on('click', function() {
                if (debugMode) {
                    console.log("تم النقر على زر المسح الضوئي");
                }
                startScanning();
            });

            // تحديث قائمة أجهزة المسح الضوئي عند النقر على زر التحديث
            $('#refresh-scanners').on('click', function() {
                if (debugMode) {
                    console.log("تم النقر على زر تحديث قائمة الماسحات الضوئية");
                }
                updateScannersList();
            });

            // إضافة صفحات أخرى عند النقر على زر الإضافة
            $('#add-more-pages-button').on('click', function() {
                if (debugMode) {
                    console.log("تم النقر على زر إضافة صفحات أخرى");
                }
                startScanning();
            });

            // حفظ المستند الممسوح ضوئياً عند النقر على زر الحفظ
            $('#save-document-button').on('click', function() {
                if (debugMode) {
                    console.log("تم النقر على زر حفظ المستند");
                }
                saveScannedDocument();
            });

            if (debugMode) {
                console.log("تم تهيئة صفحة المسح الضوئي بنجاح");
            }

            // تم تهيئة صفحة المسح الضوئي بنجاح
        } catch (error) {
            console.error("حدث خطأ أثناء تهيئة صفحة المسح الضوئي:", error);
            showAlert('danger', 'حدث خطأ أثناء تهيئة صفحة المسح الضوئي: ' + error.message);
        }

        // تحديث قائمة الماسحات الضوئية مرة واحدة فقط عند تحميل الصفحة
    });
</script>
{% endblock %}
