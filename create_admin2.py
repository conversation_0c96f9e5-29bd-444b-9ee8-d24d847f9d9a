from app import app, db
from models import Employee
from werkzeug.security import generate_password_hash

# إنشاء سياق التطبيق
with app.app_context():
    # إنشاء مستخدم مدير جديد
    admin = Employee(
        username='admin2',
        full_name='مدير النظام 2',
        job_title='مدير النظام',
        work_location='الإدارة',
        is_admin=True
    )
    
    # تعيين كلمة المرور
    admin.set_password('admin2')
    
    # إضافة المستخدم إلى قاعدة البيانات
    db.session.add(admin)
    db.session.commit()
    
    print("تم إنشاء مستخدم مدير جديد")
    print("اسم المستخدم: admin2")
    print("كلمة المرور: admin2")
