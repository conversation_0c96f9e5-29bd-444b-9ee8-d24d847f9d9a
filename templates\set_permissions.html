{% extends 'base.html' %}

{% block title %}تعيين صلاحيات المستخدم{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">تعيين صلاحيات المستخدم: {{ user.full_name }}</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="form-group mb-3">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="username" value="{{ user.username }}" readonly>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="full_name" class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="full_name" value="{{ user.full_name }}" readonly>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label class="form-label">الصلاحيات</label>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="permissions" value="all" id="all_permissions" 
                                    {% if 'all' in current_permissions or user.is_admin %}checked{% endif %}>
                                <label class="form-check-label" for="all_permissions">
                                    <strong>جميع الصلاحيات</strong>
                                </label>
                            </div>
                            
                            <hr>
                            
                            <div class="row">
                                {% for permission in available_permissions %}
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input permission-checkbox" type="checkbox" 
                                            name="permissions" value="{{ permission.name }}" id="{{ permission.name }}"
                                            {% if permission.name in current_permissions %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ permission.name }}">
                                            {{ permission.description }}
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        
                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">حفظ الصلاحيات</button>
                            <a href="{{ url_for('user_management') }}" class="btn btn-secondary">إلغاء</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // عند تحديد "جميع الصلاحيات"، تحديد جميع الصلاحيات الأخرى
    document.getElementById('all_permissions').addEventListener('change', function() {
        var checkboxes = document.querySelectorAll('.permission-checkbox');
        for (var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].checked = this.checked;
            checkboxes[i].disabled = this.checked;
        }
    });
    
    // تحديد حالة "جميع الصلاحيات" عند تحميل الصفحة
    window.addEventListener('load', function() {
        var allPermissions = document.getElementById('all_permissions');
        if (allPermissions.checked) {
            var checkboxes = document.querySelectorAll('.permission-checkbox');
            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].checked = true;
                checkboxes[i].disabled = true;
            }
        }
    });
</script>
{% endblock %}
