/**
 * وظائف مساعدة للتقارير والطباعة
 */

// طباعة التقرير
function printReport(reportTitle) {
    // إضافة عنوان للتقرير المطبوع
    const reportHeader = document.createElement('div');
    reportHeader.className = 'report-print-header';
    reportHeader.innerHTML = `
        <h1>${reportTitle || 'تقرير نظام إدارة الإجازات'}</h1>
        <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
    `;

    // إضافة العنوان مؤقتًا للطباعة
    const reportTable = document.getElementById('reportTable');

    // حفظ المحتوى الأصلي
    const originalContent = reportTable.innerHTML;

    // إضافة العنوان للطباعة
    reportTable.prepend(reportHeader);

    // طباعة التقرير
    window.print();

    // إعادة المحتوى الأصلي (بدلاً من إزالة العنوان فقط)
    setTimeout(() => {
        reportTable.innerHTML = originalContent;
    }, 500);
}

// تصدير التقرير إلى Excel
function exportToExcel(sheetName, fileName) {
    const table = document.querySelector('#reportTable table');
    const wb = XLSX.utils.table_to_book(table, {sheet: sheetName || "تقرير"});
    XLSX.writeFile(wb, fileName || "تقرير.xlsx");
}

// تصدير التقرير إلى PDF
function exportToPDF(fileName) {
    const element = document.getElementById('reportTable');
    const opt = {
        margin: 10,
        filename: fileName || 'تقرير.pdf',
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'mm', format: 'a4', orientation: 'landscape' }
    };

    // استخدام مكتبة html2pdf
    html2pdf().set(opt).from(element).save();
}

// تعيين تواريخ افتراضية للتقارير
function setDefaultDates() {
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');

    if (!startDateInput.value) {
        const today = new Date();
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        startDateInput.value = firstDayOfMonth.toISOString().split('T')[0];
    }

    if (!endDateInput.value) {
        const today = new Date();
        endDateInput.value = today.toISOString().split('T')[0];
    }
}

// تهيئة عناصر التقرير
document.addEventListener('DOMContentLoaded', function() {
    // تعيين تواريخ افتراضية
    setDefaultDates();

    // إضافة سمة lang للحقول لضمان عرض التاريخ بشكل صحيح
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        input.setAttribute('lang', 'en');
    });
});
