{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-gradient-primary-to-secondary text-white">
            <h2 class="mb-0"><i class="fas fa-chart-bar me-2"></i> التقارير</h2>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <!-- تقارير الموظفين -->
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="card h-100 border-0 shadow-sm report-card">
                        <div class="card-header bg-primary text-white py-2">
                            <h3 class="h6 mb-0"><i class="fas fa-users me-1"></i> تقارير الموظفين</h3>
                        </div>
                        <div class="card-body p-3">
                            <p class="card-text small mb-3">عرض تقارير تفصيلية عن إجازات الموظفين.</p>
                            <a href="{{ url_for('employee_reports') }}" class="btn btn-sm btn-primary w-100">
                                <i class="fas fa-file-alt me-1"></i> عرض التقارير
                            </a>
                        </div>
                    </div>
                </div>

                <!-- تقارير أنواع الإجازات -->
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="card h-100 border-0 shadow-sm report-card">
                        <div class="card-header bg-success text-white py-2">
                            <h3 class="h6 mb-0"><i class="fas fa-calendar-alt me-1"></i> تقارير أنواع الإجازات</h3>
                        </div>
                        <div class="card-body p-3">
                            <p class="card-text small mb-3">عرض تقارير إحصائية حسب أنواع الإجازات.</p>
                            <a href="{{ url_for('leave_type_reports') }}" class="btn btn-sm btn-success w-100">
                                <i class="fas fa-chart-pie me-1"></i> عرض التقارير
                            </a>
                        </div>
                    </div>
                </div>

                <!-- تقارير الفترات الزمنية -->
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="card h-100 border-0 shadow-sm report-card">
                        <div class="card-header bg-info text-white py-2">
                            <h3 class="h6 mb-0"><i class="fas fa-calendar-week me-1"></i> تقارير الفترات الزمنية</h3>
                        </div>
                        <div class="card-body p-3">
                            <p class="card-text small mb-3">عرض تقارير الإجازات حسب الفترات الزمنية.</p>
                            <a href="{{ url_for('date_range_reports') }}" class="btn btn-sm btn-info w-100">
                                <i class="fas fa-calendar-day me-1"></i> عرض التقارير
                            </a>
                        </div>
                    </div>
                </div>

                <!-- تقارير الأقسام -->
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="card h-100 border-0 shadow-sm report-card">
                        <div class="card-header bg-warning text-dark py-2">
                            <h3 class="h6 mb-0"><i class="fas fa-building me-1"></i> تقارير الأقسام</h3>
                        </div>
                        <div class="card-body p-3">
                            <p class="card-text small mb-3">عرض تقارير الإجازات حسب الأقسام والإدارات.</p>
                            <a href="{{ url_for('department_reports') }}" class="btn btn-sm btn-warning w-100">
                                <i class="fas fa-sitemap me-1"></i> عرض التقارير
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .card-header {
        border-radius: 10px 10px 0 0 !important;
    }

    .bg-gradient-primary-to-secondary {
        background: linear-gradient(to right, #3a5ccc, #1a3a9c);
    }

    .card {
        transition: transform 0.3s;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0,0,0,0.15) !important;
    }

    .report-card {
        border: 1px solid rgba(0,0,0,0.1) !important;
    }

    .report-card .card-header {
        padding: 0.5rem 1rem;
    }

    .report-card .btn {
        font-weight: 500;
        border-radius: 5px;
    }

    /* تحسين التباين للألوان */
    .bg-primary {
        background-color: #0d47a1 !important;
    }

    .bg-success {
        background-color: #1b5e20 !important;
    }

    .bg-info {
        background-color: #006064 !important;
    }

    .bg-warning {
        background-color: #e65100 !important;
        color: white !important;
    }

    .btn-warning {
        color: white !important;
    }
</style>
{% endblock %}
