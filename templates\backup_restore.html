{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h1 class="mb-4">النسخ الاحتياطية واستعادة البيانات</h1>

    <div class="row">
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0"><i class="fas fa-download me-2"></i> إنشاء نسخة احتياطية</h3>
                </div>
                <div class="card-body">
                    <p class="mb-3">قم بإنشاء نسخة احتياطية من قاعدة البيانات والمستندات المرفقة.</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> النسخة الاحتياطية تشمل:
                        <ul class="mb-0 mt-2">
                            <li>بيانات الموظفين</li>
                            <li>بيانات الإجازات</li>
                            <li>المستندات المرفقة</li>
                        </ul>
                    </div>
                    <form action="{{ url_for('backup_database') }}" method="post" class="mt-3">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-download me-2"></i> إنشاء نسخة احتياطية جديدة
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h3 class="mb-0"><i class="fas fa-upload me-2"></i> استعادة نسخة احتياطية</h3>
                </div>
                <div class="card-body">
                    <p class="mb-3">قم باستعادة نسخة احتياطية سابقة من قاعدة البيانات والمستندات المرفقة.</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> استعادة نسخة احتياطية سيؤدي إلى استبدال جميع البيانات الحالية!
                    </div>
                    <form action="{{ url_for('restore_database') }}" method="post" enctype="multipart/form-data" class="mt-3">
                        <div class="mb-3">
                            <label for="backup_file" class="form-label">اختر ملف النسخة الاحتياطية</label>
                            <input type="file" class="form-control" id="backup_file" name="backup_file" accept=".zip" required>
                            <div class="form-text">يجب أن يكون الملف بتنسيق ZIP وتم إنشاؤه بواسطة هذا النظام.</div>
                        </div>
                        <button type="submit" class="btn btn-warning btn-lg" onclick="return confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية!')">
                            <i class="fas fa-upload me-2"></i> استعادة النسخة الاحتياطية
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {% if backups %}
    <div class="card mt-4">
        <div class="card-header bg-dark text-white">
            <h3 class="mb-0"><i class="fas fa-history me-2"></i> النسخ الاحتياطية السابقة</h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>#</th>
                            <th>اسم الملف</th>
                            <th>تاريخ الإنشاء</th>
                            <th>حجم الملف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for backup in backups %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ backup.filename }}</td>
                            <td>{{ backup.date.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                            <td>{{ (backup.size / 1024 / 1024)|round(2) }} ميجابايت</td>
                            <td>
                                <a href="{{ url_for('backup_database') }}?file={{ backup.filename }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-download me-1"></i> تنزيل
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
