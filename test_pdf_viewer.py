#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار عارض PDF في البيئة المجمعة
"""

import os
import sys
import webbrowser
import tempfile
from datetime import datetime

def test_pdf_creation():
    """اختبار إنشاء ملف PDF بسيط"""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        
        # إنشاء ملف PDF مؤقت
        temp_dir = tempfile.gettempdir()
        pdf_path = os.path.join(temp_dir, f"test_pdf_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")
        
        # إنشاء PDF بسيط
        c = canvas.Canvas(pdf_path, pagesize=A4)
        c.drawString(100, 750, "اختبار PDF - تم إنشاؤه بنجاح")
        c.drawString(100, 700, f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        c.drawString(100, 650, f"المسار: {pdf_path}")
        c.save()
        
        print(f"✅ تم إنشاء ملف PDF بنجاح: {pdf_path}")
        return pdf_path
    except Exception as e:
        print(f"❌ فشل في إنشاء ملف PDF: {str(e)}")
        return None

def test_pdf_opening(pdf_path):
    """اختبار فتح ملف PDF"""
    if not pdf_path or not os.path.exists(pdf_path):
        print("❌ ملف PDF غير موجود")
        return False
    
    try:
        # محاولة فتح PDF بالمتصفح الافتراضي
        webbrowser.open(f"file:///{pdf_path}")
        print(f"✅ تم فتح ملف PDF بالمتصفح: {pdf_path}")
        return True
    except Exception as e:
        print(f"❌ فشل في فتح ملف PDF: {str(e)}")
        return False

def check_environment():
    """فحص البيئة والمكتبات"""
    print("=== فحص البيئة ===")
    print(f"Python Version: {sys.version}")
    print(f"Platform: {sys.platform}")
    print(f"Executable: {sys.executable}")
    print(f"Frozen: {getattr(sys, 'frozen', False)}")
    
    if getattr(sys, 'frozen', False):
        base_dir = os.path.dirname(sys.executable)
        print(f"Base Directory (EXE): {base_dir}")
    else:
        base_dir = os.path.dirname(os.path.abspath(__file__))
        print(f"Base Directory (Script): {base_dir}")
    
    print(f"Current Working Directory: {os.getcwd()}")
    
    # فحص المكتبات المطلوبة
    libraries = ['flask', 'reportlab', 'PIL', 'win32com.client']
    print("\n=== فحص المكتبات ===")
    for lib in libraries:
        try:
            __import__(lib)
            print(f"✅ {lib}: متوفرة")
        except ImportError as e:
            print(f"❌ {lib}: غير متوفرة - {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار عارض PDF في البيئة المجمعة")
    print("=" * 50)
    
    # فحص البيئة
    check_environment()
    
    print("\n" + "=" * 50)
    print("🧪 اختبار إنشاء وفتح PDF")
    
    # اختبار إنشاء PDF
    pdf_path = test_pdf_creation()
    
    if pdf_path:
        # اختبار فتح PDF
        success = test_pdf_opening(pdf_path)
        
        if success:
            print("\n✅ جميع الاختبارات نجحت!")
            print("💡 يبدو أن عارض PDF يعمل بشكل صحيح")
        else:
            print("\n❌ فشل في فتح PDF")
            print("💡 قد تحتاج إلى تثبيت عارض PDF أو تحديث المتصفح")
    else:
        print("\n❌ فشل في إنشاء PDF")
        print("💡 تحقق من تثبيت مكتبة reportlab")
    
    print("\n" + "=" * 50)
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
