import os
import sqlite3
from app import app
from models import db, Employee, LeaveType, LeaveRequest
from werkzeug.security import generate_password_hash

# حذف قاعدة البيانات الحالية إذا كانت موجودة
db_path = 'employees.db'
if os.path.exists(db_path):
    # إغلاق جميع الاتصالات بقاعدة البيانات
    try:
        conn = sqlite3.connect(db_path)
        conn.close()
    except:
        pass

    # حذف الملف
    try:
        os.remove(db_path)
        print(f"تم حذف قاعدة البيانات القديمة: {db_path}")
    except Exception as e:
        print(f"خطأ في حذف قاعدة البيانات: {str(e)}")
        print("سنحاول إعادة إنشاء الجداول بدلاً من ذلك")

with app.app_context():
    # إنشاء قاعدة البيانات الجديدة
    db.drop_all()  # حذف جميع الجداول
    db.create_all()  # إعادة إنشاء الجداول
    print("تم إنشاء قاعدة البيانات الجديدة")

    # إضافة حساب المدير
    admin = Employee(
        username='admin',
        full_name='مدير النظام',
        employee_number='1000',
        job_title='مدير النظام',
        work_location='المقر الرئيسي',
        is_admin=True,
        leave_balance=36,
        children_count=0  # إضافة الحقل الجديد
    )
    admin.password_hash = generate_password_hash('admin123')
    db.session.add(admin)

    # إضافة أنواع الإجازات
    leave_types = [
        {'name': 'إجازة اعتيادية', 'days_allowed': 30},
        {'name': 'إجازة مرضية', 'days_allowed': 30},
        {'name': 'إجازة المعيل', 'days_allowed': 15},
        {'name': 'إجازة خمس سنوات', 'days_allowed': 60},
        {'name': 'إجازة بدون راتب', 'days_allowed': None},
        {'name': 'إجازة ما قبل الوضع', 'days_allowed': 21},
        {'name': 'إجازة ما بعد الوضع', 'days_allowed': 51},
        {'name': 'إجازة أمومة', 'days_allowed': 365},
        {'name': 'إجازة دراسية', 'days_allowed': 180},
        {'name': 'إجازة طويلة', 'days_allowed': 365}
    ]

    for lt_data in leave_types:
        leave_type = LeaveType(**lt_data)
        db.session.add(leave_type)

    # حفظ التغييرات
    try:
        db.session.commit()
        print("تم إضافة حساب المدير وأنواع الإجازات بنجاح")
    except Exception as e:
        db.session.rollback()
        print(f"خطأ في إضافة البيانات: {str(e)}")
