from app import app, db
from models import LeaveType

def update_leave_types():
    with app.app_context():
        try:
            # إنشاء الجداول إذا لم تكن موجودة
            db.create_all()
            
            # حذف جميع أنواع الإجازات الحالية
            LeaveType.query.delete()
            
            # إضافة أنواع الإجازات الجديدة
            leave_types = [
                {'id': 1, 'name': 'إجازة اعتيادية', 'days_allowed': 30},
                {'id': 2, 'name': 'إجازة سنوية', 'days_allowed': 30},
                {'id': 3, 'name': 'إجازة مرضية', 'days_allowed': 30},
                {'id': 4, 'name': 'إجازة المعيل', 'days_allowed': 15},
                {'id': 5, 'name': 'إجازة خمس سنوات', 'days_allowed': 60},
                {'id': 6, 'name': 'إجازة بدون راتب', 'days_allowed': None},
                {'id': 7, 'name': 'إجازة ما قبل الوضع', 'days_allowed': 21, 'description': '21 يوم'},
                {'id': 8, 'name': 'إجازة ما بعد الوضع', 'days_allowed': 51, 'description': '51 يوم'},
                {'id': 9, 'name': 'إجازة أمومة', 'days_allowed': 365, 'description': 'سنة كاملة'}
            ]

            for leave_type in leave_types:
                new_leave_type = LeaveType(
                    id=leave_type['id'],
                    name=leave_type['name'],
                    days_allowed=leave_type['days_allowed'],
                    description=leave_type.get('description', '')
                )
                db.session.add(new_leave_type)

            db.session.commit()
            print("تم تحديث أنواع الإجازات بنجاح")
            
            # التحقق من البيانات
            all_types = LeaveType.query.all()
            print("\nأنواع الإجازات الموجودة:")
            for lt in all_types:
                print(f"- {lt.name} ({lt.days_allowed} يوم)")
                
        except Exception as e:
            db.session.rollback()
            print(f"حدث خطأ: {str(e)}")

if __name__ == "__main__":
    # التأكد من وجود ملف قاعدة البيانات
    import os
    if not os.path.exists('leaves.db'):
        print("إنشاء ملف قاعدة البيانات...")
        with app.app_context():
            db.create_all()
    
    update_leave_types()

