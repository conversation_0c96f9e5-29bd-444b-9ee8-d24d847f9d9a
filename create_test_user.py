from app import app, db
from models import Employee
from datetime import datetime

def create_test_employee():
    with app.app_context():
        # إضافة موظف تجريبي
        employee = Employee(
            full_name='موظف تجريبي',
            work_location='المكتب الرئيسي',
            job_title='موظف',
            leave_balance=30
        )
        db.session.add(employee)
        db.session.commit()
        print("تم إنشاء المستخدم التجريبي بنجاح")

if __name__ == '__main__':
    create_test_employee()

