"""
سكريبت لإنشاء مستخدم مدير افتراضي في قاعدة البيانات
"""
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash
import os
import sys

# إنشاء تطبيق Flask
app = Flask(__name__)

# تكوين قاعدة البيانات
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///ajazat.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة قاعدة البيانات
db = SQLAlchemy(app)

# تعريف نموذج الموظف
class Employee(db.Model):
    __tablename__ = 'employee'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True)
    password_hash = db.Column(db.String(120))
    full_name = db.Column(db.String(120))
    employee_number = db.Column(db.String(50), nullable=True, unique=True)
    department = db.Column(db.String(120), nullable=True)
    job_title = db.Column(db.String(120))
    work_location = db.Column(db.String(120))
    leave_balance = db.Column(db.Integer, default=36)
    is_admin = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    permissions = db.Column(db.String(255))
    children_count = db.Column(db.Integer, default=0)
    
    def set_password(self, password):
        if password:
            self.password_hash = generate_password_hash(password)

def create_admin_user():
    """إنشاء مستخدم مدير افتراضي"""
    print("جاري إنشاء مستخدم مدير افتراضي...")
    
    # التحقق من وجود المستخدم
    admin = Employee.query.filter_by(username='admin').first()
    if admin:
        print("المستخدم 'admin' موجود بالفعل")
        print(f"اسم المستخدم: admin")
        print(f"كلمة المرور: admin123")
        return
    
    # إنشاء مستخدم مدير جديد
    admin = Employee(
        username='admin',
        full_name='مدير النظام',
        job_title='مدير النظام',
        work_location='المقر الرئيسي',
        is_admin=True,
        leave_balance=36,
        is_active=True,
        permissions='all',
        children_count=0
    )
    
    # تعيين كلمة المرور
    admin.set_password('admin123')
    
    # حفظ المستخدم في قاعدة البيانات
    db.session.add(admin)
    db.session.commit()
    
    print("تم إنشاء مستخدم مدير افتراضي بنجاح")
    print("معلومات تسجيل الدخول:")
    print("اسم المستخدم: admin")
    print("كلمة المرور: admin123")

def create_admin2_user():
    """إنشاء مستخدم مدير افتراضي ثاني"""
    print("جاري إنشاء مستخدم مدير افتراضي ثاني...")
    
    # التحقق من وجود المستخدم
    admin2 = Employee.query.filter_by(username='admin2').first()
    if admin2:
        print("المستخدم 'admin2' موجود بالفعل")
        print(f"اسم المستخدم: admin2")
        print(f"كلمة المرور: admin2")
        return
    
    # إنشاء مستخدم مدير جديد
    admin2 = Employee(
        username='admin2',
        full_name='مدير النظام 2',
        job_title='مدير النظام',
        work_location='المقر الرئيسي',
        is_admin=True,
        leave_balance=36,
        is_active=True,
        permissions='all',
        children_count=0
    )
    
    # تعيين كلمة المرور
    admin2.set_password('admin2')
    
    # حفظ المستخدم في قاعدة البيانات
    db.session.add(admin2)
    db.session.commit()
    
    print("تم إنشاء مستخدم مدير افتراضي ثاني بنجاح")
    print("معلومات تسجيل الدخول:")
    print("اسم المستخدم: admin2")
    print("كلمة المرور: admin2")

def main():
    """الدالة الرئيسية"""
    with app.app_context():
        # إنشاء جميع الجداول إذا لم تكن موجودة
        db.create_all()
        
        # إنشاء مستخدمين مديرين افتراضيين
        create_admin_user()
        create_admin2_user()
        
        print("\nتم إنشاء المستخدمين بنجاح")
        print("يمكنك الآن تسجيل الدخول باستخدام أي من الحسابين:")
        print("1. اسم المستخدم: admin / كلمة المرور: admin123")
        print("2. اسم المستخدم: admin2 / كلمة المرور: admin2")

if __name__ == '__main__':
    main()
