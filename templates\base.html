<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الإجازات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/rtl-date-inputs.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/fix-table-display.css') }}" rel="stylesheet">
    {% block styles %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-calendar-check me-2"></i>نظام الإجازات
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            {% if session.get('employee_id') %}
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i> لوحة التحكم
                        </a>
                    </li>

                    <!-- عرض قائمة الموظفين للمدراء أو المستخدمين الذين لديهم صلاحيات عرض الموظفين -->
                    {% if session.get('is_admin') or 'view_employees' in session.get('permissions', '').split(',') %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="employeesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-users me-1"></i> الموظفين
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="employeesDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('employees') }}">قائمة الموظفين</a></li>
                            {% if session.get('is_admin') or 'add_employees' in session.get('permissions', '').split(',') %}
                            <li><a class="dropdown-item" href="{{ url_for('add_employee') }}">إضافة موظف</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('import_employees') }}">استيراد من Excel</a></li>
                            {% endif %}
                        </ul>
                    </li>
                    {% endif %}

                    <!-- عرض قائمة الإجازات للمدراء أو المستخدمين الذين لديهم صلاحيات عرض الإجازات -->
                    {% if session.get('is_admin') or 'view_leaves' in session.get('permissions', '').split(',') %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="leavesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-calendar-alt me-1"></i> الإجازات
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="leavesDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('search_leaves') }}">قائمة الإجازات</a></li>
                            {% if session.get('is_admin') or 'manage_leave_types' in session.get('permissions', '').split(',') %}
                            <li><a class="dropdown-item" href="{{ url_for('leave_types') }}">أنواع الإجازات</a></li>
                            {% endif %}
                        </ul>
                    </li>
                    {% endif %}

                    <!-- عرض إدارة الحسابات للمدراء فقط -->
                    {% if session.get('is_admin') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('user_management') }}">
                            <i class="fas fa-users-cog me-1"></i> إدارة الحسابات
                        </a>
                    </li>
                    {% endif %}

                    <!-- عرض التقارير للمدراء فقط -->
                    {% if session.get('is_admin') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports') }}">
                            <i class="fas fa-chart-bar me-1"></i> التقارير
                        </a>
                    </li>
                    {% endif %}

                    <!-- عرض النسخ الاحتياطية للمدراء فقط -->
                    {% if session.get('is_admin') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('backup') }}">
                            <i class="fas fa-database me-1"></i> النسخ الاحتياطية
                        </a>
                    </li>
                    {% endif %}
                </ul>
                <div class="d-flex">
                    <a class="btn btn-outline-light" href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt me-1"></i> تسجيل خروج
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}
        {% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://npmcdn.com/flatpickr/dist/l10n/ar.js"></script>
    <script src="{{ url_for('static', filename='js/rtl-date-inputs.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>

