{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h2 class="mb-0"><i class="fas fa-file-upload me-2"></i> رفع مستند</h2>
            <a href="{{ url_for('employee_leaves', employee_id=employee.id) }}" class="btn btn-light btn-sm">
                <i class="fas fa-arrow-right me-1"></i> العودة لإجازات الموظف
            </a>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title mb-3">معلومات الموظف</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>الاسم:</span>
                                    <strong>{{ employee.full_name }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>الرقم الوظيفي:</span>
                                    <strong>{{ employee.employee_number or '-' }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>المسمى الوظيفي:</span>
                                    <strong>{{ employee.job_title }}</strong>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title mb-3">معلومات الإجازة</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>نوع الإجازة:</span>
                                    <strong>{{ leave.leave_type.name }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>تاريخ البداية:</span>
                                    <strong>{{ leave.start_date.strftime('%Y-%m-%d') }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>تاريخ النهاية:</span>
                                    <strong>{{ leave.end_date.strftime('%Y-%m-%d') }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>المدة:</span>
                                    <strong>{{ leave.days_count }} يوم</strong>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <form method="POST" enctype="multipart/form-data" class="mt-4" id="upload-form">
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">رفع مستند جديد</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <div class="text-center mb-3">
                                <label for="document" class="btn btn-primary btn-lg fw-bold px-5 py-3" style="font-size: 1.2rem; cursor: pointer;">
                                    <i class="fas fa-file-upload me-2"></i> اختر ملف للرفع
                                </label>
                                <input type="file" class="d-none" id="document" name="document" required onchange="handleFileSelect(this)">
                            </div>

                            <div id="file-info" class="text-center mb-3 d-none">
                                <div class="alert alert-success">
                                    <div class="spinner-border spinner-border-sm text-success me-2" role="status" id="upload-spinner">
                                        <span class="visually-hidden">جاري الرفع...</span>
                                    </div>
                                    <span id="upload-status">جاري رفع الملف: </span>
                                    <strong id="selected-file-name"></strong>
                                </div>
                            </div>

                            <div class="form-text text-muted text-center">
                                <i class="fas fa-info-circle me-1"></i>
                                الملفات المسموح بها: PDF, DOC, DOCX, JPG, JPEG, PNG. الحد الأقصى للحجم: 16 ميجابايت.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">وصف المستند (اختياري)</label>
                            <input type="text" class="form-control" id="description" name="description" placeholder="مثال: أمر إداري، تقرير طبي، إلخ">
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ url_for('employee_leaves', employee_id=employee.id) }}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times me-1"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-success btn-lg fw-bold d-none" style="min-width: 200px;" id="upload-btn">
                                <i class="fas fa-upload me-2"></i> رفع المستند
                            </button>
                        </div>

                        <script>
                            function handleFileSelect(input) {
                                if (input.files && input.files[0]) {
                                    const fileName = input.files[0].name;
                                    const fileInfo = document.getElementById('file-info');
                                    const fileNameElement = document.getElementById('selected-file-name');

                                    // عرض اسم الملف وحالة الرفع
                                    fileNameElement.textContent = fileName;
                                    fileInfo.classList.remove('d-none');

                                    // إضافة وصف المستند إلى النموذج (إذا كان موجودًا)
                                    const description = document.getElementById('description').value;

                                    // إرسال النموذج تلقائيًا
                                    document.getElementById('upload-form').submit();
                                }
                            }
                        </script>
                    </div>
                </div>
            </form>

            <!-- عرض المستندات الحالية -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">المستندات الحالية</h5>
                </div>
                <div class="card-body">
                    {% if documents %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>اسم المستند</th>
                                        <th>النوع</th>
                                        <th>تاريخ الرفع</th>
                                        <th>الوصف</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for doc in documents %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ doc.file_name }}</td>
                                            <td>
                                                {% if doc.document_type == 'uploaded' %}
                                                    <span class="badge bg-primary">مرفوع</span>
                                                {% elif doc.document_type == 'scanned' %}
                                                    <span class="badge bg-info">ممسوح ضوئياً</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ doc.document_type }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ doc.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                            <td>{{ doc.description or '-' }}</td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{{ url_for('view_document', document_id=doc.id) }}" class="btn btn-sm btn-info" target="_blank">
                                                        <i class="fas fa-eye"></i> عرض
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteDocModal{{ doc.id }}">
                                                        <i class="fas fa-trash-alt"></i> حذف
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- نوافذ منبثقة لتأكيد حذف المستندات -->
                        {% for doc in documents %}
                            <div class="modal fade" id="deleteDocModal{{ doc.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger text-white">
                                            <h5 class="modal-title">تأكيد حذف المستند</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>هل أنت متأكد من رغبتك في حذف هذا المستند؟</p>
                                            <p><strong>اسم المستند:</strong> {{ doc.file_name }}</p>
                                            <p class="text-danger"><strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <a href="{{ url_for('delete_document', document_id=doc.id) }}" class="btn btn-danger">تأكيد الحذف</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد مستندات مرفقة بهذه الإجازة.
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="mt-4">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> إذا كنت ترغب في مسح المستند ضوئيًا بدلاً من رفعه، يمكنك استخدام
                    <a href="{{ url_for('scan_document', leave_id=leave.id) }}" class="alert-link">أداة المسح الضوئي</a>.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
