from app import app, db
from models import Document
import os
from sqlalchemy import inspect

def fix_document_table():
    """إصلاح جدول المستندات في قاعدة البيانات"""
    with app.app_context():
        # التحقق من وجود جدول document
        inspector = inspect(db.engine)
        if 'document' not in inspector.get_table_names():
            print("جدول 'document' غير موجود. سيتم إنشاؤه الآن...")
            Document.__table__.create(db.engine)
            print("تم إنشاء جدول 'document' بنجاح.")
        else:
            print("جدول 'document' موجود بالفعل.")

if __name__ == '__main__':
    # التأكد من وجود مجلد instance
    if not os.path.exists('instance'):
        os.makedirs('instance')
        print("تم إنشاء مجلد instance")

    # إصلاح جدول المستندات
    fix_document_table()
