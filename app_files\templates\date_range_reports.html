{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
            <h2 class="mb-0"><i class="fas fa-calendar-week me-2"></i> تقارير الفترات الزمنية</h2>
            <a href="{{ url_for('reports') }}" class="btn btn-light btn-sm">
                <i class="fas fa-arrow-right me-1"></i> العودة للتقارير
            </a>
        </div>
        <div class="card-body">
            <form method="GET" id="reportForm" class="mb-4">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="report_period" class="form-label">الفترة الزمنية:</label>
                        <select name="report_period" id="report_period" class="form-select">
                            <option value="custom" {% if report_period == 'custom' %}selected{% endif %}>فترة مخصصة</option>
                            <option value="current_month" {% if report_period == 'current_month' %}selected{% endif %}>الشهر الحالي</option>
                            <option value="last_month" {% if report_period == 'last_month' %}selected{% endif %}>الشهر الماضي</option>
                            <option value="current_year" {% if report_period == 'current_year' %}selected{% endif %}>السنة الحالية</option>
                            <option value="last_year" {% if report_period == 'last_year' %}selected{% endif %}>السنة الماضية</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">من تاريخ:</label>
                        <input type="date" id="start_date" name="start_date" class="form-control" value="{{ start_date }}">
                    </div>
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">إلى تاريخ:</label>
                        <input type="date" id="end_date" name="end_date" class="form-control" value="{{ end_date }}">
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-info w-100">
                            <i class="fas fa-search me-1"></i> عرض التقرير
                        </button>
                    </div>
                </div>
            </form>

            {% if report_data %}
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 class="h5 mb-0">نتائج التقرير للفترة من {{ start_date }} إلى {{ end_date }}</h3>
                <div class="btn-group">
                    <button type="button" class="btn btn-success" onclick="printReport()">
                        <i class="fas fa-print me-1"></i> طباعة
                    </button>
                    <button type="button" class="btn btn-info" onclick="exportToExcel()">
                        <i class="fas fa-file-excel me-1"></i> تصدير Excel
                    </button>
                </div>
            </div>

            <!-- ملخص إحصائي -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm text-center mb-3">
                        <div class="card-body">
                            <h3 class="h6 text-muted mb-2">إجمالي الإجازات</h3>
                            <h2 class="display-5 fw-bold text-primary">{{ total_leaves }}</h2>
                            <p class="text-muted">إجازة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm text-center mb-3">
                        <div class="card-body">
                            <h3 class="h6 text-muted mb-2">إجمالي أيام الإجازات</h3>
                            <h2 class="display-5 fw-bold text-success">{{ total_days }}</h2>
                            <p class="text-muted">يوم</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm text-center mb-3">
                        <div class="card-body">
                            <h3 class="h6 text-muted mb-2">متوسط مدة الإجازة</h3>
                            <h2 class="display-5 fw-bold text-info">{{ average_days }}</h2>
                            <p class="text-muted">يوم/إجازة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- توزيع الإجازات حسب الشهر -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h3 class="h6 mb-0"><i class="fas fa-chart-bar me-2"></i> توزيع الإجازات حسب الشهر</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>الشهر</th>
                                    {% for month_name in month_names %}
                                    <th>{{ month_name }}</th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>عدد الإجازات</strong></td>
                                    {% for count in monthly_distribution.counts %}
                                    <td>{{ count }}</td>
                                    {% endfor %}
                                </tr>
                                <tr>
                                    <td><strong>إجمالي الأيام</strong></td>
                                    {% for days in monthly_distribution.days %}
                                    <td>{{ days }}</td>
                                    {% endfor %}
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="table-responsive" id="reportTable">
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>ت</th>
                            <th>اسم الموظف</th>
                            <th>القسم</th>
                            <th>نوع الإجازة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>المدة (أيام)</th>
                            <th>تاريخ المباشرة</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in report_data %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ item.employee.full_name }}</td>
                            <td>{{ item.employee.department or '-' }}</td>
                            <td>{{ item.leave_type.name }}</td>
                            <td class="date-cell">{{ item.leave.start_date.strftime('%Y-%m-%d') }}</td>
                            <td class="date-cell">{{ item.leave.end_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ item.leave.days_count }}</td>
                            <td class="date-cell">{{ item.leave.return_date.strftime('%Y-%m-%d') if item.leave.return_date else '-' }}</td>
                            <td>
                                {% if 'تمت المباشرة' in item.leave.comment|default('') %}
                                <span class="badge bg-success">تمت المباشرة</span>
                                {% elif 'لم يباشر' in item.leave.comment|default('') %}
                                <span class="badge bg-danger">لم يباشر</span>
                                {% elif item.leave.end_date < current_date %}
                                <span class="badge bg-warning">انتهت</span>
                                {% else %}
                                <span class="badge bg-primary">جارية</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <td colspan="6" class="text-start"><strong>إجمالي أيام الإجازات:</strong></td>
                            <td><strong>{{ total_days }}</strong></td>
                            <td colspan="2"></td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> يرجى تحديد معايير البحث وعرض التقرير.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    /* تنسيقات حقول التاريخ */
    input[type="date"] {
        direction: ltr;
    }

    .table th {
        white-space: nowrap;
    }

    /* تنسيقات جدول التقارير */
    .report-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 1rem;
        border: 1px solid #000;
    }

    /* تنسيق رأس الجدول */
    .report-table thead {
        background-color: #e6e6e6;
    }

    .report-table th {
        background-color: #e6e6e6;
        color: #000;
        font-weight: bold;
        padding: 8px;
        text-align: right;
        border: 1px solid #000;
        white-space: nowrap;
    }

    /* تنسيق خلايا الجدول */
    .report-table td {
        background-color: #fff;
        color: #000;
        padding: 8px;
        text-align: right;
        border: 1px solid #000;
    }

    /* تنسيق الحدود */
    .report-table, .report-table th, .report-table td {
        border: 1px solid #000;
    }

    /* تنسيق تذييل الجدول */
    .report-table tfoot {
        background-color: #fff;
    }

    /* تنسيقات الطباعة */
    @media print {
        body * {
            visibility: hidden;
        }

        #reportTable, #reportTable * {
            visibility: visible;
        }

        #reportTable {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }

        /* إخفاء العناصر غير المطلوبة في الطباعة */
        .no-print, button, .btn, tfoot, .report-table tfoot {
            display: none !important;
        }

        /* تنسيق الجدول للطباعة */
        .report-table {
            width: 100% !important;
            border-collapse: collapse !important;
            border: 1px solid #000 !important;
        }

        /* تنسيق رأس الجدول للطباعة */
        .report-table thead, .report-table th {
            background-color: #e6e6e6 !important;
            color: #000 !important;
            border: 1px solid #000 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            color-adjust: exact !important;
        }

        /* تنسيق خلايا الجدول للطباعة */
        .report-table td {
            background-color: #fff !important;
            color: #000 !important;
            border: 1px solid #000 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            color-adjust: exact !important;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
<script>
    function printReport() {
        window.print();
    }

    function exportToExcel() {
        const table = document.querySelector('#reportTable table');
        const wb = XLSX.utils.table_to_book(table, {sheet: "تقرير الفترات الزمنية"});
        XLSX.writeFile(wb, "تقرير_الفترات_الزمنية.xlsx");
    }

    document.addEventListener('DOMContentLoaded', function() {
        const reportPeriodSelect = document.getElementById('report_period');
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');

        // تعيين تاريخ اليوم كقيمة افتراضية إذا لم يتم تحديد تاريخ
        if (!startDateInput.value) {
            const today = new Date();
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            startDateInput.value = firstDayOfMonth.toISOString().split('T')[0];
        }

        if (!endDateInput.value) {
            const today = new Date();
            endDateInput.value = today.toISOString().split('T')[0];
        }

        // تحديث التواريخ عند تغيير الفترة الزمنية
        reportPeriodSelect.addEventListener('change', function() {
            const today = new Date();
            let startDate, endDate;

            switch(this.value) {
                case 'current_month':
                    startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                    endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                    break;
                case 'last_month':
                    startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                    endDate = new Date(today.getFullYear(), today.getMonth(), 0);
                    break;
                case 'current_year':
                    startDate = new Date(today.getFullYear(), 0, 1);
                    endDate = new Date(today.getFullYear(), 11, 31);
                    break;
                case 'last_year':
                    startDate = new Date(today.getFullYear() - 1, 0, 1);
                    endDate = new Date(today.getFullYear() - 1, 11, 31);
                    break;
                default:
                    // لا تغيير للتواريخ في حالة الفترة المخصصة
                    return;
            }

            startDateInput.value = startDate.toISOString().split('T')[0];
            endDateInput.value = endDate.toISOString().split('T')[0];
        });
    });
</script>
{% endblock %}
