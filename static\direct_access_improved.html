<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عارض المستندات</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }

        .container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .toolbar {
            background-color: #0d47a1;
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 100;
        }

        .toolbar h3 {
            margin: 0;
            font-size: 18px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 70%;
        }

        .toolbar-actions {
            display: flex;
            gap: 10px;
        }

        .toolbar-button {
            color: white;
            text-decoration: none;
            padding: 6px 12px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
        }

        .toolbar-button:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .content {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            overflow: auto;
            background-color: #f0f0f0;
        }

        .image-container {
            max-width: 100%;
            max-height: 80vh;
            background-color: white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
        }

        .image-frame {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .pdf-container {
            width: 100%;
            height: 80vh;
            border: none;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        /* تحسين عرض الصور */
        @media screen {
            .image-container {
                max-width: 100%;
                max-height: 80vh;
                margin: 0 auto;
                background-color: white;
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                padding: 10px;
            }

            .image-frame {
                max-width: 100%;
                max-height: 100%;
                object-fit: contain;
            }
        }

        .error-message {
            text-align: center;
            padding: 20px;
            background-color: #ffebee;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 500px;
        }

        .error-message h3 {
            color: #d32f2f;
            margin-top: 0;
        }

        .error-message p {
            color: #555;
            margin-bottom: 20px;
        }

        .loading-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #0d47a1;
            animation: spin 1s linear infinite;
            margin-bottom: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fallback-container {
            text-align: center;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            max-width: 500px;
        }

        .fallback-container h3 {
            color: #0d47a1;
            margin-top: 0;
        }

        .fallback-container p {
            color: #555;
            margin-bottom: 20px;
        }

        .fallback-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
        }

        .fallback-button {
            background-color: #0d47a1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
            text-decoration: none;
            display: inline-block;
            min-width: 200px;
        }

        .fallback-button:hover {
            background-color: #0a3880;
        }

        @media print {
            .toolbar {
                display: none;
            }

            .content {
                padding: 0;
                background-color: white;
            }

            .image-container {
                box-shadow: none;
                padding: 0;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
        integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
</head>
<body>
    <div class="container">
        <div class="toolbar">
            <h3 id="document-title">عارض المستندات</h3>
            <div class="toolbar-actions">
                <button class="toolbar-button" onclick="printDocument()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="toolbar-button" onclick="goBack()">
                    <i class="fas fa-arrow-right"></i> رجوع
                </button>
            </div>
        </div>
        <div class="content" id="content">
            <div class="loading-indicator">
                <div class="spinner"></div>
                <p>جاري تحميل المستند...</p>
            </div>
        </div>
    </div>

    <script>
        // استخراج معلمات URL
        function getUrlParams() {
            const params = {};
            const queryString = window.location.search.substring(1);
            const pairs = queryString.split('&');

            for (const pair of pairs) {
                const [key, value] = pair.split('=');
                params[decodeURIComponent(key)] = decodeURIComponent(value || '');
            }

            return params;
        }

        // تحميل المستند
        function loadDocument() {
            const params = getUrlParams();
            const documentPath = params.path;
            const documentUrl = params.url;
            const documentTitle = params.title || 'مستند';
            const documentType = params.type || '';

            // تعيين عنوان المستند
            document.getElementById('document-title').textContent = documentTitle;
            document.title = documentTitle;

            // التحقق من وجود مسار للمستند
            if (!documentPath && !documentUrl) {
                showError('لم يتم تحديد مسار المستند');
                return;
            }

            // تحديد نوع المستند
            const isPdf = documentType.toLowerCase() === 'pdf' ||
                         (documentPath && documentPath.toLowerCase().endsWith('.pdf')) ||
                         (documentUrl && documentUrl.toLowerCase().endsWith('.pdf'));

            // استخدام المسار أو URL حسب المتوفر
            const documentSource = documentPath || documentUrl;

            // محاولة تحميل المستند
            try {
                if (isPdf) {
                    loadPdf(documentSource);
                } else {
                    loadImage(documentSource);
                }

                // إضافة معالج أخطاء عام
                window.addEventListener('error', function(e) {
                    console.error('حدث خطأ في تحميل المستند:', e);
                    showFallbackOptions(documentSource);
                });
            } catch (e) {
                console.error('حدث خطأ في تحميل المستند:', e);
                showFallbackOptions(documentSource);
            }
        }

        // تحميل صورة
        function loadImage(path) {
            const content = document.getElementById('content');

            // إنشاء عنصر الصورة
            const container = document.createElement('div');
            container.className = 'image-container';

            const img = new Image();
            img.className = 'image-frame';

            // معالجة حدث تحميل الصورة
            img.onload = function() {
                content.innerHTML = '';
                container.appendChild(img);
                content.appendChild(container);
            };

            // معالجة حدث خطأ تحميل الصورة
            img.onerror = function(e) {
                console.error('خطأ في تحميل الصورة:', e);
                showFallbackOptions(path);
            };

            // تعيين مصدر الصورة لبدء التحميل
            img.src = path;

            // إضافة مهلة للتحميل
            setTimeout(function() {
                if (!img.complete || img.naturalWidth === 0) {
                    showFallbackOptions(path);
                }
            }, 5000);
        }

        // تحميل ملف PDF
        function loadPdf(path) {
            const content = document.getElementById('content');
            const params = getUrlParams();

            // التحقق مما إذا كان المسار يبدأ بـ data:application/pdf;base64
            if (path.startsWith('data:application/pdf;base64,')) {
                // إنشاء عنصر embed لعرض PDF مباشرة من البيانات
                const embed = document.createElement('embed');
                embed.className = 'pdf-container';
                embed.type = 'application/pdf';
                embed.src = path;

                // إضافة العنصر إلى المحتوى
                content.innerHTML = '';
                content.appendChild(embed);
                return;
            }

            // إذا كان هناك URL للتنزيل المباشر، استخدمه بدلاً من مسار الملف
            const downloadUrl = params.url;
            if (downloadUrl && !path.startsWith('data:')) {
                // إنشاء عنصر embed لعرض PDF
                const embed = document.createElement('embed');
                embed.className = 'pdf-container';
                embed.type = 'application/pdf';
                embed.src = downloadUrl;

                // معالجة حدث خطأ تحميل الملف
                embed.onerror = function(e) {
                    console.error('خطأ في تحميل ملف PDF:', e);
                    showFallbackOptions(downloadUrl);
                };

                // إضافة العنصر إلى المحتوى
                content.innerHTML = '';
                content.appendChild(embed);

                // إضافة مهلة للتحميل
                setTimeout(function() {
                    if (!embed.complete) {
                        showFallbackOptions(downloadUrl);
                    }
                }, 5000);

                return;
            }

            // الطريقة التقليدية باستخدام iframe
            const iframe = document.createElement('iframe');
            iframe.className = 'pdf-container';

            // معالجة حدث تحميل الملف
            iframe.onload = function() {
                // التحقق مما إذا كان الملف قد تم تحميله بنجاح
                try {
                    // محاولة الوصول إلى محتوى الإطار
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

                    // إذا كان المحتوى فارغًا أو يحتوي على رسالة خطأ
                    if (!iframeDoc || iframeDoc.body.innerHTML.includes('error') || iframeDoc.body.innerHTML === '') {
                        throw new Error('فشل تحميل ملف PDF');
                    }
                } catch (e) {
                    // في حالة حدوث خطأ، عرض خيارات بديلة
                    showFallbackOptions(path);
                    return;
                }
            };

            // معالجة حدث خطأ تحميل الملف
            iframe.onerror = function(e) {
                console.error('خطأ في تحميل ملف PDF:', e);
                showFallbackOptions(path);
            };

            // تعيين مصدر الملف لبدء التحميل
            iframe.src = path;

            // إضافة الإطار إلى المحتوى
            content.innerHTML = '';
            content.appendChild(iframe);

            // إضافة مهلة للتحميل
            setTimeout(function() {
                try {
                    // محاولة الوصول إلى محتوى الإطار
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

                    // إذا كان المحتوى فارغًا أو يحتوي على رسالة خطأ
                    if (!iframeDoc || iframeDoc.body.innerHTML.includes('error') || iframeDoc.body.innerHTML === '') {
                        showFallbackOptions(path);
                    }
                } catch (e) {
                    showFallbackOptions(path);
                }
            }, 5000);
        }

        // عرض رسالة خطأ
        function showError(message) {
            const content = document.getElementById('content');

            content.innerHTML = `
                <div class="error-message">
                    <h3>حدث خطأ</h3>
                    <p>${message}</p>
                    <button class="toolbar-button" onclick="window.location.reload()">
                        إعادة المحاولة
                    </button>
                </div>
            `;
        }

        // عرض خيارات بديلة
        function showFallbackOptions(path) {
            const content = document.getElementById('content');
            const params = getUrlParams();
            const documentTitle = params.title || 'مستند';

            // إنشاء URL للتنزيل المباشر
            let downloadUrl = path;

            // إذا كان المسار يبدأ بـ file://، قم بإنشاء رابط تنزيل بديل
            if (path.startsWith('file:///')) {
                // استخدام رابط التنزيل من المعلمات إذا كان متوفرًا
                downloadUrl = params.url || path;
            }

            content.innerHTML = `
                <div class="fallback-container">
                    <h3>تعذر عرض المستند</h3>
                    <p>يبدو أن هناك مشكلة في عرض المستند مباشرة. يمكنك تجربة الخيارات التالية:</p>
                    <div class="fallback-actions">
                        <a href="${downloadUrl}" class="fallback-button" download="${documentTitle}">
                            <i class="fas fa-download"></i> تنزيل المستند
                        </a>
                        <button class="fallback-button" onclick="window.location.reload()">
                            <i class="fas fa-sync"></i> إعادة المحاولة
                        </button>
                        <button class="fallback-button" onclick="openInNewTab('${path}')">
                            <i class="fas fa-external-link-alt"></i> فتح في علامة تبويب جديدة
                        </button>
                    </div>
                </div>
            `;
        }

        // فتح المستند في علامة تبويب جديدة
        function openInNewTab(url) {
            window.open(url, '_blank');
        }

        // طباعة المستند
        function printDocument() {
            window.print();
        }

        // الرجوع للصفحة السابقة
        function goBack() {
            window.history.back();
        }

        // تحميل المستند عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', loadDocument);
    </script>
</body>
</html>
