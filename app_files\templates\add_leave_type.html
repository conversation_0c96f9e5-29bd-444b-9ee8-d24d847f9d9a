{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h2 class="mb-0"><i class="fas fa-plus-circle me-2"></i> إضافة نوع إجازة جديد</h2>
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="mb-3">
                    <label for="name" class="form-label">اسم نوع الإجازة:</label>
                    <input type="text" id="name" name="name" class="form-control" required>
                </div>
                
                <div class="mb-3">
                    <label for="description" class="form-label">الوصف:</label>
                    <textarea id="description" name="description" class="form-control" rows="3"></textarea>
                </div>
                
                <div class="mb-3">
                    <label for="days_allowed" class="form-label">عدد الأيام المسموح بها:</label>
                    <input type="number" id="days_allowed" name="days_allowed" class="form-control" min="1">
                    <div class="form-text">اترك هذا الحقل فارغًا إذا كان عدد الأيام غير محدد.</div>
                </div>
                
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> حفظ
                    </button>
                    <a href="{{ url_for('leave_types') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
