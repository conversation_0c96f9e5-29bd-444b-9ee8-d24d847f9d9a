"""
سكريبت للتحقق من المنافذ المفتوحة
"""
import socket
import subprocess
import sys

def check_port(port):
    """التحقق مما إذا كان المنفذ مفتوحًا"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"خطأ في التحقق من المنفذ: {str(e)}")
        return False

def get_process_using_port(port):
    """الحصول على العملية التي تستخدم المنفذ"""
    try:
        # هذا الأمر يعمل فقط على Windows
        output = subprocess.check_output(f'netstat -ano | findstr :{port}', shell=True).decode()
        if output:
            lines = output.strip().split('\n')
            for line in lines:
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.strip().split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        try:
                            process_info = subprocess.check_output(f'tasklist /fi "PID eq {pid}"', shell=True).decode()
                            return f"المنفذ {port} مستخدم من قبل العملية:\n{process_info}"
                        except:
                            return f"المنفذ {port} مستخدم من قبل العملية ذات المعرف {pid}"
            return f"المنفذ {port} مستخدم ولكن لم يتم العثور على معلومات العملية"
        return f"المنفذ {port} غير مستخدم"
    except subprocess.CalledProcessError:
        return f"المنفذ {port} غير مستخدم"
    except Exception as e:
        return f"خطأ في الحصول على معلومات المنفذ: {str(e)}"

def main():
    """الدالة الرئيسية"""
    print("التحقق من المنافذ المستخدمة...")
    
    # التحقق من المنافذ الشائعة
    ports_to_check = [5000, 8080]
    
    for port in ports_to_check:
        is_open = check_port(port)
        print(f"المنفذ {port}: {'مستخدم' if is_open else 'غير مستخدم'}")
        
        if is_open:
            print(get_process_using_port(port))
    
    print("\nإذا كان المنفذ مستخدمًا، يمكنك إغلاق العملية التي تستخدمه أو استخدام منفذ آخر.")
    print("لإغلاق العملية، افتح مدير المهام (Task Manager) وابحث عن العملية ذات المعرف (PID) المذكور وقم بإنهائها.")

if __name__ == '__main__':
    main()
    input("اضغط أي مفتاح للخروج...")
