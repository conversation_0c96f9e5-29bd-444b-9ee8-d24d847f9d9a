"""
سكريبت لإصلاح أزرار الحذف في واجهة المستخدم
"""
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
import os
import shutil

# إنشاء تطبيق Flask
app = Flask(__name__)

# تكوين قاعدة البيانات
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///ajazat.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة قاعدة البيانات
db = SQLAlchemy(app)

def fix_user_management_template():
    """إصلاح قالب إدارة المستخدمين"""
    print("جاري إصلاح قالب إدارة المستخدمين...")
    
    # مسارات القوالب
    template_paths = [
        'templates/user_management.html',
        'app_files/templates/user_management.html'
    ]
    
    for template_path in template_paths:
        if os.path.exists(template_path):
            print(f"تم العثور على القالب: {template_path}")
            
            # قراءة محتوى القالب
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من وجود زر الحذف
            if 'delete_user' in content and 'حذف' in content:
                print(f"زر الحذف موجود بالفعل في القالب: {template_path}")
            else:
                # إضافة زر الحذف
                content = content.replace(
                    '<a href="{{ url_for(\'set_permissions\', user_id=user.id) }}" class="btn btn-sm btn-info">\n                                            <i class="fas fa-key"></i> الصلاحيات\n                                        </a>',
                    '<a href="{{ url_for(\'set_permissions\', user_id=user.id) }}" class="btn btn-sm btn-info">\n                                            <i class="fas fa-key"></i> الصلاحيات\n                                        </a>\n                                        {% if user.id != session.get(\'employee_id\') %}\n                                            <a href="{{ url_for(\'delete_user\', user_id=user.id) }}" class="btn btn-sm btn-danger" onclick="return confirm(\'هل أنت متأكد من حذف هذا المستخدم؟\')">\n                                                <i class="fas fa-trash"></i> حذف\n                                            </a>\n                                        {% endif %}'
                )
                
                # حفظ التغييرات
                with open(template_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"تم إضافة زر الحذف إلى القالب: {template_path}")
        else:
            print(f"لم يتم العثور على القالب: {template_path}")

def fix_employees_template():
    """إصلاح قالب الموظفين"""
    print("جاري إصلاح قالب الموظفين...")
    
    # مسارات القوالب
    template_paths = [
        'templates/employees.html',
        'app_files/templates/employees.html'
    ]
    
    for template_path in template_paths:
        if os.path.exists(template_path):
            print(f"تم العثور على القالب: {template_path}")
            
            # قراءة محتوى القالب
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من وجود زر الحذف
            if 'delete_employee' in content and 'حذف' in content:
                print(f"زر الحذف موجود بالفعل في القالب: {template_path}")
            else:
                # إضافة زر الحذف
                content = content.replace(
                    '<a href="{{ url_for(\'edit_employee\', employee_id=employee.id) }}" class="btn btn-sm btn-warning">\n                                            <i class="fas fa-edit"></i> تعديل\n                                        </a>',
                    '<a href="{{ url_for(\'edit_employee\', employee_id=employee.id) }}" class="btn btn-sm btn-warning">\n                                            <i class="fas fa-edit"></i> تعديل\n                                        </a>\n                                        {% if employee.id != session.get(\'employee_id\') %}\n                                            <a href="{{ url_for(\'delete_employee\', employee_id=employee.id) }}" class="btn btn-sm btn-danger" onclick="return confirm(\'هل أنت متأكد من حذف هذا الموظف؟\')">\n                                                <i class="fas fa-trash"></i> حذف\n                                            </a>\n                                        {% endif %}'
                )
                
                # حفظ التغييرات
                with open(template_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"تم إضافة زر الحذف إلى القالب: {template_path}")
        else:
            print(f"لم يتم العثور على القالب: {template_path}")

def fix_manage_users_template():
    """إصلاح قالب إدارة المستخدمين الآخر"""
    print("جاري إصلاح قالب إدارة المستخدمين الآخر...")
    
    # مسارات القوالب
    template_paths = [
        'templates/manage_users.html',
        'app_files/templates/manage_users.html'
    ]
    
    for template_path in template_paths:
        if os.path.exists(template_path):
            print(f"تم العثور على القالب: {template_path}")
            
            # قراءة محتوى القالب
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من وجود زر الحذف
            if 'delete_user' in content and 'حذف' in content:
                print(f"زر الحذف موجود بالفعل في القالب: {template_path}")
            else:
                # إضافة زر الحذف
                content = content.replace(
                    '{% endif %}',
                    '{% endif %}\n                                        {% if user.id != session.get(\'employee_id\') %}\n                                            <a href="{{ url_for(\'delete_user\', user_id=user.id) }}" class="btn btn-sm btn-danger" onclick="return confirm(\'هل أنت متأكد من حذف هذا المستخدم؟\')">\n                                                <i class="fas fa-trash"></i> حذف\n                                            </a>\n                                        {% endif %}',
                    1  # استبدال أول ظهور فقط
                )
                
                # حفظ التغييرات
                with open(template_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"تم إضافة زر الحذف إلى القالب: {template_path}")
        else:
            print(f"لم يتم العثور على القالب: {template_path}")

def backup_templates():
    """عمل نسخة احتياطية من القوالب قبل التعديل"""
    print("جاري عمل نسخة احتياطية من القوالب...")
    
    # إنشاء مجلد للنسخ الاحتياطية
    backup_dir = 'templates_backup'
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    # قائمة القوالب المراد نسخها
    templates = [
        'templates/user_management.html',
        'templates/employees.html',
        'templates/manage_users.html',
        'app_files/templates/user_management.html',
        'app_files/templates/employees.html',
        'app_files/templates/manage_users.html'
    ]
    
    # نسخ القوالب
    for template_path in templates:
        if os.path.exists(template_path):
            # إنشاء اسم الملف في مجلد النسخ الاحتياطية
            backup_path = os.path.join(backup_dir, os.path.basename(template_path))
            
            # نسخ الملف
            shutil.copy2(template_path, backup_path)
            print(f"تم نسخ {template_path} إلى {backup_path}")

def main():
    """الدالة الرئيسية"""
    print("=== بدء إصلاح أزرار الحذف في واجهة المستخدم ===")
    
    # عمل نسخة احتياطية من القوالب
    backup_templates()
    
    # إصلاح القوالب
    fix_user_management_template()
    fix_employees_template()
    fix_manage_users_template()
    
    print("=== تم الانتهاء من إصلاح أزرار الحذف في واجهة المستخدم ===")
    print("يرجى إعادة تشغيل التطبيق لتطبيق التغييرات")

if __name__ == '__main__':
    main()
