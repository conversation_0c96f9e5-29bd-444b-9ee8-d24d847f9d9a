"""
وحدة للتفاعل مباشرة مع WIA باستخدام pywin32.
"""
import sys
import traceback
import base64
import io
import json
from datetime import datetime
from PIL import Image
import pythoncom

# المتغيرات العالمية
WIA_AVAILABLE = False
WIA_ERROR_MESSAGE = ""

try:
    # تهيئة COM قبل استخدام win32com
    pythoncom.CoInitialize()
    import win32com.client
    WIA_AVAILABLE = True
except ImportError as e:
    WIA_ERROR_MESSAGE = f"خطأ في استيراد win32com.client: {str(e)}"
    print(WIA_ERROR_MESSAGE, file=sys.stderr)
except Exception as e:
    WIA_ERROR_MESSAGE = f"خطأ غير متوقع: {str(e)}"
    print(WIA_ERROR_MESSAGE, file=sys.stderr)
    traceback.print_exc()

def get_available_scanners():
    """
    الحصول على قائمة بأجهزة المسح الضوئي المتوفرة.

    Returns:
        list: قائمة بالماسحات الضوئية المتوفرة.
    """
    print("=== بدء تنفيذ دالة direct_wia_scanner.get_available_scanners ===")

    if not WIA_AVAILABLE:
        print("WIA غير متوفر")
        return []

    try:
        # تهيئة COM مرة أخرى للتأكد من أنها نشطة في هذا السياق
        pythoncom.CoInitialize()

        # إنشاء مدير أجهزة WIA
        print("محاولة إنشاء مدير أجهزة WIA...")
        device_manager = win32com.client.Dispatch("WIA.DeviceManager")
        print("تم إنشاء مدير أجهزة WIA بنجاح")

        # الحصول على مجموعة الأجهزة
        devices = device_manager.DeviceInfos

        # طباعة معلومات تشخيصية
        print(f"عدد الأجهزة المكتشفة: {devices.Count}")

        # إنشاء قائمة بالماسحات الضوئية
        scanners = []

        # محاولة اكتشاف الماسحات الضوئية تلقائيًا
        print("محاولة اكتشاف الماسحات الضوئية تلقائيًا...")
        for i in range(1, devices.Count + 1):
            try:
                device_info = devices.Item(i)
                # طباعة معلومات تشخيصية عن الجهاز
                device_type = device_info.Type
                device_name = device_info.Properties("Name").Value
                print(f"الجهاز {i}: النوع={device_type}, الاسم={device_name}")

                # طباعة جميع خصائص الجهاز للتشخيص
                print(f"خصائص الجهاز {i}:")
                for j in range(1, device_info.Properties.Count + 1):
                    try:
                        prop = device_info.Properties(j)
                        print(f"  - {prop.Name}: {prop.Value}")
                    except Exception as prop_error:
                        print(f"  - خطأ في قراءة الخاصية {j}: {str(prop_error)}")

                # التحقق مما إذا كان الجهاز ماسحًا ضوئيًا
                if device_type == 1:  # 1 = ماسح ضوئي
                    # محاولة الحصول على معلومات إضافية
                    device_id = ""
                    try:
                        device_id = device_info.DeviceID
                        print(f"معرف الجهاز: {device_id}")
                    except Exception as id_error:
                        print(f"خطأ في الحصول على معرف الجهاز: {str(id_error)}")
                        device_id = f"scanner_{i-1}"

                    scanner_info = {
                        'id': str(i-1),  # استخدام الفهرس كمعرف (0-based)
                        'name': device_name,
                        'device_id': device_id,
                        'source': 'direct_wia'
                    }
                    scanners.append(scanner_info)
                    print(f"تم إضافة ماسح ضوئي: {device_name}, المعرف: {i-1}")
                elif "scan" in device_name.lower() or "canon" in device_name.lower() or "hp" in device_name.lower() or "epson" in device_name.lower():
                    # إضافة أي جهاز قد يكون ماسحًا ضوئيًا
                    device_id = ""
                    try:
                        device_id = device_info.DeviceID
                    except Exception:
                        device_id = f"possible_scanner_{i-1}"

                    scanner_info = {
                        'id': str(i-1),
                        'name': f"{device_name} (قد يكون ماسحًا ضوئيًا)",
                        'device_id': device_id,
                        'source': 'direct_wia'
                    }
                    scanners.append(scanner_info)
                    print(f"تم إضافة جهاز محتمل: {device_name}, المعرف: {i-1}")
            except Exception as e:
                print(f"خطأ في معالجة الجهاز {i}: {str(e)}")
                traceback.print_exc()
                continue

        # إضافة ماسح ضوئي CANON DR-M260 يدويًا إذا لم يتم اكتشافه
        canon_found = False
        for scanner in scanners:
            if "CANON" in scanner['name'].upper() and "DR-M260" in scanner['name'].upper():
                canon_found = True
                break

        if not canon_found:
            # إضافة ماسح ضوئي CANON DR-M260 يدويًا
            canon_scanner = {
                'id': "canon_dr_m260",
                'name': "CANON DR-M260 (مضاف يدويًا)",
                'device_id': "{6BDD1FC6-810F-11D0-BEC7-08002BE2092F}\\0005",
                'source': 'direct_wia_manual'
            }
            scanners.append(canon_scanner)
            print(f"تم إضافة ماسح ضوئي CANON DR-M260 يدويًا")

        # محاولة استخدام طريقة بديلة إذا لم يتم العثور على أي ماسح ضوئي
        if not scanners:
            print("لم يتم العثور على أي ماسح ضوئي، محاولة استخدام طريقة بديلة...")
            try:
                # إنشاء حوار WIA
                wia_dialog = win32com.client.Dispatch("WIA.CommonDialog")

                # محاولة عرض حوار اختيار الماسح الضوئي
                print("محاولة عرض حوار اختيار الماسح الضوئي...")
                device = wia_dialog.ShowSelectDevice(0, 0, 0)

                if device:
                    device_name = device.Properties("Name").Value
                    device_id = device.DeviceID

                    scanner_info = {
                        'id': "dialog_selected",
                        'name': device_name,
                        'device_id': device_id,
                        'source': 'direct_wia_dialog'
                    }
                    scanners.append(scanner_info)
                    print(f"تم إضافة ماسح ضوئي من الحوار: {device_name}")
            except Exception as dialog_error:
                print(f"خطأ في استخدام حوار اختيار الماسح الضوئي: {str(dialog_error)}")
                traceback.print_exc()

        # إضافة ماسح ضوئي افتراضي إذا لم يتم العثور على أي ماسح ضوئي
        if not scanners:
            default_scanner = {
                'id': "default_scanner",
                'name': "ماسح ضوئي افتراضي (للاختبار)",
                'device_id': "default_scanner",
                'source': 'direct_wia_default'
            }
            scanners.append(default_scanner)
            print("تم إضافة ماسح ضوئي افتراضي لأنه لم يتم العثور على أي ماسح ضوئي")

        print(f"تم العثور على {len(scanners)} ماسح ضوئي")
        print("=== انتهاء تنفيذ دالة direct_wia_scanner.get_available_scanners ===")
        return scanners
    except Exception as e:
        print(f"خطأ في الحصول على الماسحات الضوئية: {str(e)}")
        traceback.print_exc()

        # إضافة ماسح ضوئي CANON DR-M260 يدويًا كبديل
        print("إضافة ماسح ضوئي CANON DR-M260 يدويًا كبديل")
        canon_scanner = {
            'id': "canon_dr_m260",
            'name': "CANON DR-M260 (مضاف يدويًا)",
            'device_id': "{6BDD1FC6-810F-11D0-BEC7-08002BE2092F}\\0005",
            'source': 'direct_wia_fallback'
        }

        print("=== انتهاء تنفيذ دالة direct_wia_scanner.get_available_scanners مع خطأ ===")
        return [canon_scanner]

def scan_document(scanner_id, dpi=200, color_mode="Color", use_adf=True):
    """
    مسح مستند باستخدام الماسح الضوئي المحدد.

    Args:
        scanner_id (str): معرف الماسح الضوئي.
        dpi (int): الدقة بالنقاط في البوصة.
        color_mode (str): وضع الألوان ("Color", "Grayscale", "BlackAndWhite").
        use_adf (bool): استخدام وحدة التغذية التلقائية إذا كانت متوفرة.

    Returns:
        str: الصورة الممسوحة ضوئيًا بتنسيق base64.
    """
    print("=== بدء تنفيذ دالة direct_wia_scanner.scan_document ===")
    print(f"المعلمات: scanner_id={scanner_id}, dpi={dpi}, color_mode={color_mode}, use_adf={use_adf}")

    if not WIA_AVAILABLE:
        print("WIA غير متوفر")
        return None

    try:
        # تهيئة COM مرة أخرى للتأكد من أنها نشطة في هذا السياق
        pythoncom.CoInitialize()

        print(f"بدء المسح الضوئي باستخدام الجهاز {scanner_id}")
        print(f"استخدام وحدة التغذية التلقائية: {use_adf}")

        # استخدام طريقة مباشرة للتعامل مع WIA
        # إنشاء كائن CommonDialog
        print("إنشاء كائن CommonDialog...")
        wia_common_dialog = win32com.client.Dispatch("WIA.CommonDialog")
        print("تم إنشاء كائن CommonDialog بنجاح")

        # إنشاء مدير أجهزة WIA
        print("إنشاء مدير أجهزة WIA...")
        device_manager = win32com.client.Dispatch("WIA.DeviceManager")
        print("تم إنشاء مدير أجهزة WIA بنجاح")

        # الحصول على مجموعة الأجهزة
        print("الحصول على مجموعة الأجهزة...")
        devices = device_manager.DeviceInfos
        print(f"تم الحصول على مجموعة الأجهزة: {devices.Count} جهاز")

        # طباعة معلومات تشخيصية عن الأجهزة المتوفرة
        print(f"عدد الأجهزة المتوفرة: {devices.Count}")
        for i in range(1, devices.Count + 1):
            try:
                device_info_temp = devices.Item(i)
                print(f"الجهاز {i-1}: النوع={device_info_temp.Type}, الاسم={device_info_temp.Properties('Name').Value}")
            except Exception as e:
                print(f"خطأ في الحصول على معلومات الجهاز {i}: {str(e)}")

        # التعامل مع معرف الماسح الضوئي
        device = None
        device_info = None

        # التحقق من نوع معرف الماسح الضوئي
        print(f"نوع معرف الماسح الضوئي: {type(scanner_id)}, القيمة: {scanner_id}")

        # محاولة العثور على الماسح الضوئي باستخدام المعرف
        if scanner_id == "canon_dr_m260":
            # البحث عن ماسح ضوئي CANON DR-M260
            print("البحث عن ماسح ضوئي CANON DR-M260...")
            for i in range(1, devices.Count + 1):
                try:
                    device_info_temp = devices.Item(i)
                    device_name = device_info_temp.Properties("Name").Value
                    if "CANON" in device_name.upper() and "DR-M260" in device_name.upper():
                        device_info = device_info_temp
                        print(f"تم العثور على ماسح ضوئي CANON DR-M260: {device_name}")
                        break
                except Exception as e:
                    print(f"خطأ في البحث عن ماسح ضوئي CANON DR-M260 في الجهاز {i}: {str(e)}")
        elif scanner_id == "default_scanner" or scanner_id == "dialog_selected":
            # استخدام حوار اختيار الماسح الضوئي
            print("استخدام حوار اختيار الماسح الضوئي...")
            try:
                device = wia_common_dialog.ShowSelectDevice(0, 0, 0)
                if device:
                    print(f"تم اختيار الماسح الضوئي: {device.Properties('Name').Value}")
                else:
                    print("لم يتم اختيار أي ماسح ضوئي من الحوار")
            except Exception as dialog_error:
                print(f"خطأ في استخدام حوار اختيار الماسح الضوئي: {str(dialog_error)}")
        else:
            # محاولة تحويل معرف الماسح الضوئي إلى فهرس
            try:
                scanner_index = int(scanner_id) + 1  # تحويل إلى 1-based
                print(f"محاولة استخدام الجهاز بالفهرس: {scanner_index}")

                if 1 <= scanner_index <= devices.Count:
                    device_info = devices.Item(scanner_index)
                    print(f"تم اختيار الجهاز: {device_info.Properties('Name').Value}")
                else:
                    print(f"معرف الماسح الضوئي خارج النطاق: {scanner_id}, الحد الأقصى: {devices.Count}")
            except ValueError:
                # إذا لم يكن المعرف رقمًا، البحث عن الماسح الضوئي باستخدام DeviceID
                print(f"محاولة البحث عن الماسح الضوئي باستخدام DeviceID: {scanner_id}")
                for i in range(1, devices.Count + 1):
                    try:
                        device_info_temp = devices.Item(i)
                        if device_info_temp.DeviceID == scanner_id:
                            device_info = device_info_temp
                            print(f"تم العثور على الماسح الضوئي باستخدام DeviceID: {scanner_id}")
                            break
                    except Exception as e:
                        print(f"خطأ في البحث عن الماسح الضوئي باستخدام DeviceID في الجهاز {i}: {str(e)}")

        # إذا لم يتم العثور على الماسح الضوئي، محاولة استخدام أول ماسح ضوئي متوفر
        if not device and not device_info:
            print("لم يتم العثور على الماسح الضوئي المحدد، محاولة استخدام أول ماسح ضوئي متوفر...")
            for i in range(1, devices.Count + 1):
                try:
                    device_info_temp = devices.Item(i)
                    if device_info_temp.Type == 1:  # 1 = ماسح ضوئي
                        device_info = device_info_temp
                        print(f"تم العثور على ماسح ضوئي بديل: {device_info.Properties('Name').Value}")
                        break
                except Exception as e:
                    print(f"خطأ في البحث عن ماسح ضوئي بديل في الجهاز {i}: {str(e)}")

        # إذا لم يتم العثور على أي ماسح ضوئي، استخدام حوار اختيار الماسح الضوئي
        if not device and not device_info:
            print("لم يتم العثور على أي ماسح ضوئي، استخدام حوار اختيار الماسح الضوئي...")
            try:
                device = wia_common_dialog.ShowSelectDevice(0, 0, 0)
                if device:
                    print(f"تم اختيار الماسح الضوئي من الحوار: {device.Properties('Name').Value}")
                else:
                    print("لم يتم اختيار أي ماسح ضوئي من الحوار")
                    raise ValueError("لم يتم العثور على أي ماسح ضوئي")
            except Exception as dialog_error:
                print(f"خطأ في استخدام حوار اختيار الماسح الضوئي: {str(dialog_error)}")
                raise ValueError("لم يتم العثور على أي ماسح ضوئي")

        # الاتصال بالجهاز إذا لم يكن متصلاً بالفعل
        if not device and device_info:
            print("محاولة الاتصال بالجهاز...")
            try:
                device = device_info.Connect()
                print("تم الاتصال بالجهاز بنجاح")
            except Exception as connect_error:
                print(f"خطأ في الاتصال بالجهاز: {str(connect_error)}")
                raise

        # التحقق من وجود الجهاز
        if not device:
            print("لم يتم العثور على أي جهاز مسح ضوئي")
            raise ValueError("لم يتم العثور على أي جهاز مسح ضوئي")

        # ثوابت WIA للتغذية التلقائية
        WIA_DOCUMENT_HANDLING_SELECT = 3088
        WIA_DOCUMENT_HANDLING_STATUS = 3089
        WIA_DOCUMENT_HANDLING_CAPABILITIES = 3090
        WIA_PAGES = 3096
        FEEDER = 1
        FLATBED = 2

        # محاولة تفعيل وحدة التغذية التلقائية
        adf_available = False

        # محاولة استخدام الطريقة التقليدية أولاً (أكثر موثوقية)
        try:
            print("استخدام الطريقة التقليدية للمسح الضوئي...")

            # الحصول على العنصر الأول (عادة ما يكون سطح المسح الضوئي)
            if device.Items.Count > 0:
                selected_item = device.Items(1)
                print(f"استخدام العنصر: {selected_item.Properties('Item Name').Value}")

                # تعيين الدقة
                try:
                    selected_item.Properties("Horizontal Resolution").Value = dpi
                    selected_item.Properties("Vertical Resolution").Value = dpi
                    print(f"تم تعيين الدقة: {dpi} نقطة في البوصة")
                except Exception as e:
                    print(f"خطأ في تعيين الدقة: {str(e)}")

                # تعيين وضع الألوان
                try:
                    wia_intent_image_type_color = 1
                    wia_intent_image_type_grayscale = 2
                    wia_intent_image_type_text = 4

                    if color_mode == "Color":
                        selected_item.Properties("Current Intent").Value = wia_intent_image_type_color
                        print("تم تعيين وضع الألوان: ملون")
                    elif color_mode == "Grayscale":
                        selected_item.Properties("Current Intent").Value = wia_intent_image_type_grayscale
                        print("تم تعيين وضع الألوان: تدرج رمادي")
                    elif color_mode == "BlackAndWhite":
                        selected_item.Properties("Current Intent").Value = wia_intent_image_type_text
                        print("تم تعيين وضع الألوان: أبيض وأسود")
                except Exception as e:
                    print(f"خطأ في تعيين وضع الألوان: {str(e)}")

                # إذا تم طلب التغذية التلقائية، محاولة تفعيلها
                if use_adf:
                    print("محاولة تفعيل التغذية التلقائية بعدة طرق...")

                    # طريقة 1: محاولة تفعيل التغذية التلقائية باستخدام خصائص الجهاز
                    adf_enabled = False
                    try:
                        # التحقق من وجود خاصية WIA_DOCUMENT_HANDLING_SELECT
                        has_select_property = False
                        for i in range(device.Properties.Count):
                            try:
                                prop = device.Properties(i + 1)
                                if prop.PropertyID == WIA_DOCUMENT_HANDLING_SELECT:
                                    has_select_property = True
                                    break
                            except:
                                continue

                        if has_select_property:
                            device.Properties(WIA_DOCUMENT_HANDLING_SELECT).Value = FEEDER
                            print("طريقة 1: تم تفعيل وحدة التغذية التلقائية")
                            adf_enabled = True

                            # محاولة تعيين عدد الصفحات
                            try:
                                device.Properties(WIA_PAGES).Value = 0
                                print("تم تعيين عدد الصفحات للمسح الضوئي: جميع الصفحات")
                            except:
                                pass
                    except Exception as e:
                        print(f"طريقة 1 فشلت: {str(e)}")

                    # طريقة 2: محاولة تفعيل التغذية التلقائية باستخدام خصائص العنصر
                    if not adf_enabled:
                        try:
                            # البحث عن خصائص التغذية التلقائية في العنصر
                            for i in range(selected_item.Properties.Count):
                                try:
                                    prop = selected_item.Properties(i + 1)
                                    prop_name = prop.Name.lower()
                                    if "feeder" in prop_name or "adf" in prop_name or "document" in prop_name:
                                        print(f"وجدت خاصية متعلقة بالتغذية التلقائية: {prop.Name}")
                                        # محاولة تفعيل هذه الخاصية
                                        if prop.IsReadOnly == False:
                                            if isinstance(prop.Value, bool):
                                                prop.Value = True
                                            elif isinstance(prop.Value, int):
                                                prop.Value = 1
                                            print(f"طريقة 2: تم تفعيل خاصية {prop.Name}")
                                            adf_enabled = True
                                except:
                                    continue
                        except Exception as e:
                            print(f"طريقة 2 فشلت: {str(e)}")

                    # طريقة 3: محاولة تفعيل التغذية التلقائية باستخدام خصائص محددة
                    if not adf_enabled:
                        try:
                            # قائمة بأسماء الخصائص المحتملة للتغذية التلقائية
                            adf_property_names = [
                                "Document Handling Select",
                                "Document Handling Status",
                                "Pages",
                                "Feeder Enabled",
                                "ADF",
                                "Use ADF",
                                "Document Source",
                                "Document Feeder"
                            ]

                            for prop_name in adf_property_names:
                                try:
                                    prop = selected_item.Properties(prop_name)
                                    if prop and prop.IsReadOnly == False:
                                        if isinstance(prop.Value, bool):
                                            prop.Value = True
                                        elif isinstance(prop.Value, int):
                                            prop.Value = 1
                                        print(f"طريقة 3: تم تفعيل خاصية {prop_name}")
                                        adf_enabled = True
                                except:
                                    continue
                        except Exception as e:
                            print(f"طريقة 3 فشلت: {str(e)}")

                    # طريقة 4: محاولة مسح صفحات متعددة بشكل متكرر
                    if not adf_enabled:
                        print("استخدام طريقة المسح المتكرر للتغذية التلقائية")
                        # سنستخدم الطريقة التقليدية ولكن سنقوم بمسح صفحات متعددة
                        # هذا الكود سيتم تنفيذه لاحقاً بعد المسح الأول

                # إجراء المسح الضوئي
                print("بدء عملية المسح الضوئي...")

                # تحديد تنسيق الصورة
                wia_format_jpeg = "{B96B3CAE-0728-11D3-9D7B-0000F81EF32E}"

                # قائمة لتخزين الصور الممسوحة
                scanned_images = []

                # إجراء المسح الضوئي للصفحة الأولى
                try:
                    image_file = selected_item.Transfer(wia_format_jpeg)
                    print("تم الانتهاء من مسح الصفحة الأولى بنجاح")

                    # الحصول على بيانات الصورة
                    temp_file = image_file.FileData.BinaryData
                    print(f"تم الحصول على بيانات الصورة، الحجم: {len(temp_file)} بايت")

                    # إضافة الصورة إلى القائمة
                    scanned_images.append(temp_file)

                    # إذا تم طلب التغذية التلقائية، محاولة مسح صفحات إضافية
                    if use_adf:
                        # محاولة مسح صفحات إضافية (حتى 20 صفحة كحد أقصى)
                        max_pages = 20
                        for page_num in range(2, max_pages + 1):
                            try:
                                # محاولة مسح الصفحة التالية
                                print(f"محاولة مسح الصفحة {page_num}...")
                                next_image = selected_item.Transfer(wia_format_jpeg)

                                # الحصول على بيانات الصورة
                                next_temp_file = next_image.FileData.BinaryData

                                # التحقق من أن الصورة ليست فارغة
                                if len(next_temp_file) > 1000:  # تجاهل الصور الصغيرة جداً (قد تكون فارغة)
                                    print(f"تم الانتهاء من مسح الصفحة {page_num} بنجاح")
                                    print(f"حجم الصورة: {len(next_temp_file)} بايت")

                                    # إضافة الصورة إلى القائمة
                                    scanned_images.append(next_temp_file)
                                else:
                                    print(f"الصفحة {page_num} فارغة أو غير موجودة، إيقاف المسح")
                                    break
                            except Exception as e:
                                print(f"لا توجد صفحات إضافية للمسح: {str(e)}")
                                break
                except Exception as e:
                    print(f"خطأ في المسح الضوئي: {str(e)}")
                    return None

                # التحقق من وجود صور ممسوحة
                if not scanned_images:
                    print("لم يتم مسح أي صور")
                    return None

                # تحويل جميع الصور إلى PDF فقط
                print(f"تم مسح {len(scanned_images)} صفحة، سيتم تحويلها إلى PDF")

                try:
                    # استخدام مكتبة PIL لمعالجة الصور
                    from PIL import Image
                    import io
                    import os
                    import tempfile

                    # قائمة لتخزين مسارات الملفات المؤقتة
                    temp_files = []

                    # معالجة كل صورة وتحويلها إلى ملف مؤقت
                    for i, img_data in enumerate(scanned_images):
                        try:
                            # حفظ البيانات الأصلية في ملف مؤقت
                            temp_raw_path = f"temp_raw_{i}.dat"
                            with open(temp_raw_path, "wb") as raw_file:
                                raw_file.write(img_data)

                            # محاولة فتح الصورة
                            img = Image.open(temp_raw_path)

                            # تحويل الصورة إلى RGB إذا لزم الأمر
                            if img.mode != 'RGB':
                                img = img.convert('RGB')

                            # حفظ الصورة في ملف مؤقت
                            temp_img_path = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg').name
                            img.save(temp_img_path, format='JPEG', quality=95)

                            # إضافة المسار إلى القائمة
                            temp_files.append(temp_img_path)

                            # حذف الملف المؤقت الأصلي
                            if os.path.exists(temp_raw_path):
                                os.remove(temp_raw_path)

                            print(f"تم معالجة الصفحة {i+1} بنجاح")
                        except Exception as e:
                            print(f"خطأ في معالجة الصفحة {i+1}: {str(e)}")
                            continue

                            # تحويل الصورة إلى RGB إذا لزم الأمر
                            if img.mode != 'RGB':
                                img = img.convert('RGB')

                            # حفظ الصورة في ملف مؤقت بصيغة JPEG
                            temp_img_path = f"temp_scan_single.jpg"
                            img.save(temp_img_path, format='JPEG', quality=95)

                            # تحويل الصورة إلى base64
                            with open(temp_img_path, "rb") as img_file:
                                img_base64 = base64.b64encode(img_file.read()).decode()

                            # حذف الملفات المؤقتة
                            if os.path.exists(temp_img_path):
                                os.remove(temp_img_path)
                            if os.path.exists(temp_raw_path):
                                os.remove(temp_raw_path)

                            print(f"تم تحويل الصورة إلى base64 بنجاح")
                            return f"data:image/jpeg;base64,{img_base64}"
                        except Exception as e:
                            print(f"خطأ في معالجة الصورة: {str(e)}")
                            traceback.print_exc()

                            # في حالة الفشل، محاولة استخدام طريقة بديلة (مثل VueScan)
                            try:
                                print("محاولة استخدام طريقة بديلة...")

                                # استخدام مكتبة NumPy للتعامل مع البيانات الخام
                                import numpy as np

                                # تحويل البيانات إلى مصفوفة NumPy
                                data_array = np.frombuffer(scanned_images[0], dtype=np.uint8)

                                # تحديد أبعاد الصورة (افتراضياً A4 بدقة 300 DPI)
                                width = 2480  # عرض A4 بدقة 300 DPI
                                height = 3508  # ارتفاع A4 بدقة 300 DPI

                                # التحقق من حجم البيانات
                                expected_size = width * height * 3  # RGB = 3 قنوات
                                if len(data_array) >= expected_size:
                                    # إعادة تشكيل البيانات إلى صورة
                                    img_array = data_array[:expected_size].reshape((height, width, 3))

                                    # إنشاء صورة من المصفوفة
                                    img = Image.fromarray(img_array)

                                    # حفظ الصورة في ملف مؤقت
                                    temp_img_path = f"temp_scan_single_alt.jpg"
                                    img.save(temp_img_path, format='JPEG', quality=95)

                                    # تحويل الصورة إلى base64
                                    with open(temp_img_path, "rb") as img_file:
                                        img_base64 = base64.b64encode(img_file.read()).decode()

                                    # حذف الملف المؤقت
                                    if os.path.exists(temp_img_path):
                                        os.remove(temp_img_path)

                                    print(f"تم تحويل الصورة إلى base64 بنجاح باستخدام الطريقة البديلة")
                                    return f"data:image/jpeg;base64,{img_base64}"
                                else:
                                    # إذا كان حجم البيانات غير كافٍ، محاولة تحويل الصورة مباشرة
                                    img_str = base64.b64encode(scanned_images[0]).decode()
                                    return f"data:image/jpeg;base64,{img_str}"
                            except Exception as alt_e:
                                print(f"فشل في استخدام الطريقة البديلة: {str(alt_e)}")
                                traceback.print_exc()

                                # محاولة أخيرة: حفظ البيانات الخام كملف BMP
                                try:
                                    print("محاولة حفظ البيانات كملف BMP...")

                                    # حفظ البيانات الخام في ملف مؤقت
                                    temp_raw_path = f"temp_raw_single_final.dat"
                                    with open(temp_raw_path, "wb") as raw_file:
                                        raw_file.write(scanned_images[0])

                                    # استخدام مكتبة PIL لمحاولة فتح الملف بصيغ مختلفة
                                    for format_try in ['BMP', 'TIFF', 'PNG', 'JPEG']:
                                        try:
                                            img = Image.open(temp_raw_path)
                                            img = img.convert('RGB')

                                            # حفظ الصورة في ملف مؤقت
                                            temp_img_path = f"temp_scan_single_final.jpg"
                                            img.save(temp_img_path, format='JPEG', quality=95)

                                            # تحويل الصورة إلى base64
                                            with open(temp_img_path, "rb") as img_file:
                                                img_base64 = base64.b64encode(img_file.read()).decode()

                                            # حذف الملفات المؤقتة
                                            if os.path.exists(temp_img_path):
                                                os.remove(temp_img_path)
                                            if os.path.exists(temp_raw_path):
                                                os.remove(temp_raw_path)

                                            print(f"تم تحويل الصورة إلى base64 بنجاح باستخدام صيغة {format_try}")
                                            return f"data:image/jpeg;base64,{img_base64}"
                                        except:
                                            continue

                                    # إذا فشلت جميع المحاولات، إرجاع البيانات الخام
                                    img_str = base64.b64encode(scanned_images[0]).decode()
                                    return f"data:image/jpeg;base64,{img_str}"
                                except:
                                    print("فشل في جميع محاولات معالجة الصورة")
                                    return None

                    # تحويل جميع الصور إلى PDF فقط - تم إزالة خيار JPG

                    # استخدام PDF فقط
                        # طريقة 1: استخدام img2pdf (أفضل جودة)
                        try:
                            import img2pdf

                            # تحويل الصور إلى ملفات مؤقتة
                            temp_files = []
                            for i, img_data in enumerate(scanned_images):
                                try:
                                    # فتح الصورة باستخدام PIL
                                    img = Image.open(io.BytesIO(img_data))

                                    # تحويل الصورة إلى RGB إذا لزم الأمر
                                    if img.mode != 'RGB':
                                        img = img.convert('RGB')

                                    # حفظ الصورة في ملف مؤقت
                                    temp_img_path = f"temp_scan_{i}.jpg"
                                    img.save(temp_img_path, format='JPEG', quality=95)
                                    temp_files.append(temp_img_path)

                                    print(f"تم تحويل الصفحة {i+1} إلى ملف مؤقت")
                                except Exception as e:
                                    print(f"خطأ في معالجة الصفحة {i+1}: {str(e)}")

                            # إنشاء ملف PDF باستخدام img2pdf
                            pdf_bytes = img2pdf.convert(temp_files)

                            # حذف الملفات المؤقتة
                            for temp_file in temp_files:
                                if os.path.exists(temp_file):
                                    os.remove(temp_file)

                            # تحويل PDF إلى base64
                            pdf_str = base64.b64encode(pdf_bytes).decode()
                            print(f"تم إنشاء ملف PDF بحجم {len(pdf_bytes)} بايت باستخدام img2pdf")

                            return f"data:application/pdf;base64,{pdf_str}"
                        except ImportError:
                            print("مكتبة img2pdf غير متوفرة، استخدام reportlab بدلاً من ذلك")

                    # طريقة 2: استخدام reportlab
                    from reportlab.lib.pagesizes import A4
                    from reportlab.pdfgen import canvas

                    # إنشاء ملف PDF مؤقت في الذاكرة
                    pdf_buffer = io.BytesIO()
                    c = canvas.Canvas(pdf_buffer, pagesize=A4)

                    # الحصول على أبعاد A4
                    a4_width, a4_height = A4

                    # معالجة كل صورة وإضافتها إلى ملف PDF
                    for i, img_data in enumerate(scanned_images):
                        try:
                            # فتح الصورة باستخدام PIL
                            img = Image.open(io.BytesIO(img_data))

                            # تحويل الصورة إلى RGB إذا لزم الأمر
                            if img.mode != 'RGB':
                                img = img.convert('RGB')

                            # حفظ الصورة في ملف مؤقت
                            temp_img_path = f"temp_scan_{i}.jpg"
                            img.save(temp_img_path, format='JPEG', quality=95)

                            # حساب النسبة للحفاظ على تناسب الصورة
                            img_ratio = img.width / img.height
                            a4_ratio = a4_width / a4_height

                            if img_ratio > a4_ratio:
                                # الصورة أعرض من A4
                                new_width = a4_width - 40  # هامش 20 نقطة من كل جانب
                                new_height = new_width / img_ratio
                            else:
                                # الصورة أطول من A4
                                new_height = a4_height - 40  # هامش 20 نقطة من كل جانب
                                new_width = new_height * img_ratio

                            # حساب موضع الصورة لتكون في وسط الصفحة
                            x_offset = (a4_width - new_width) / 2
                            y_offset = (a4_height - new_height) / 2

                            # رسم الصورة في ملف PDF
                            c.drawImage(temp_img_path, x_offset, y_offset, width=new_width, height=new_height)

                            # إذا لم تكن هذه هي الصفحة الأخيرة، إضافة صفحة جديدة
                            if i < len(scanned_images) - 1:
                                c.showPage()

                            # حذف الملف المؤقت
                            if os.path.exists(temp_img_path):
                                os.remove(temp_img_path)

                            print(f"تمت إضافة الصفحة {i+1} إلى ملف PDF")
                        except Exception as e:
                            print(f"خطأ في معالجة الصفحة {i+1}: {str(e)}")

                    # حفظ ملف PDF
                    c.save()

                    # الحصول على بيانات PDF
                    pdf_data = pdf_buffer.getvalue()

                    # تحويل PDF إلى base64
                    pdf_str = base64.b64encode(pdf_data).decode()
                    print(f"تم إنشاء ملف PDF بحجم {len(pdf_data)} بايت باستخدام reportlab")

                    return f"data:application/pdf;base64,{pdf_str}"
                except Exception as e:
                    print(f"خطأ في إنشاء ملف PDF: {str(e)}")
                    traceback.print_exc()

                    # طريقة 3: استخدام PIL مباشرة
                    try:
                        # إنشاء قائمة بالصور
                        images = []
                        for i, img_data in enumerate(scanned_images):
                            try:
                                # فتح الصورة باستخدام PIL
                                img = Image.open(io.BytesIO(img_data))

                                # تحويل الصورة إلى RGB إذا لزم الأمر
                                if img.mode != 'RGB':
                                    img = img.convert('RGB')

                                images.append(img)
                                print(f"تم تحويل الصفحة {i+1} إلى صورة")
                            except Exception as e:
                                print(f"خطأ في معالجة الصفحة {i+1}: {str(e)}")

                        # حفظ الصور في ملف PDF
                        pdf_buffer = io.BytesIO()
                        if images:
                            # استخدام الصورة الأولى كأساس وإضافة باقي الصور
                            images[0].save(
                                pdf_buffer,
                                save_all=True,
                                append_images=images[1:] if len(images) > 1 else [],
                                format='PDF'
                            )

                            # تحويل PDF إلى base64
                            pdf_data = pdf_buffer.getvalue()
                            pdf_str = base64.b64encode(pdf_data).decode()
                            print(f"تم إنشاء ملف PDF بحجم {len(pdf_data)} بايت باستخدام PIL")

                            return f"data:application/pdf;base64,{pdf_str}"
                    except Exception as e:
                        print(f"خطأ في إنشاء ملف PDF باستخدام PIL: {str(e)}")
                        traceback.print_exc()

                    # في حالة فشل جميع الطرق، إرجاع الصورة الأولى فقط
                    img_str = base64.b64encode(scanned_images[0]).decode()
                    print("تم تحويل الصورة الأولى فقط إلى base64 بنجاح")
                    return f"data:image/jpeg;base64,{img_str}"
            else:
                print("لا توجد عناصر متاحة للمسح الضوئي، محاولة استخدام CommonDialog")
        except Exception as e:
            print(f"خطأ في استخدام الطريقة التقليدية للمسح الضوئي: {str(e)}")
            print("محاولة استخدام CommonDialog...")

        # إذا فشلت الطريقة التقليدية، محاولة استخدام CommonDialog
        try:
            print("استخدام CommonDialog للمسح الضوئي...")

            # تعيين خيارات المسح الضوئي
            wia_device_type = 1  # ماسح ضوئي
            wia_intent = 0  # افتراضي

            if color_mode == "Color":
                wia_intent = 1  # ملون
            elif color_mode == "Grayscale":
                wia_intent = 2  # تدرج رمادي
            elif color_mode == "BlackAndWhite":
                wia_intent = 4  # أبيض وأسود

            # محاولة استخدام ShowTransfer بعدة طرق
            image_file = None

            # محاولة 1: استخدام ShowUI
            try:
                print("محاولة استخدام ShowUI...")
                # هذه الطريقة تفتح واجهة المستخدم الخاصة بالماسح الضوئي
                image_file = wia_common_dialog.ShowUI(scanner_index)
                print("تم استخدام ShowUI بنجاح")
            except Exception as e:
                print(f"محاولة ShowUI فشلت: {str(e)}")

                # محاولة 2: استخدام ShowAcquireImage
                try:
                    print("محاولة استخدام ShowAcquireImage...")
                    image_file = wia_common_dialog.ShowAcquireImage(
                        wia_device_type,  # نوع الجهاز (ماسح ضوئي)
                        wia_intent,       # نوع الصورة (ملون، تدرج رمادي، إلخ)
                        0                 # معلمات إضافية
                    )
                    print("تم استخدام ShowAcquireImage بنجاح")
                except Exception as e:
                    print(f"محاولة ShowAcquireImage فشلت: {str(e)}")

                    # محاولة 3: بدون معلمات إضافية
                    try:
                        print("محاولة استخدام ShowTransfer بدون معلمات...")
                        image_file = wia_common_dialog.ShowTransfer(device)
                        print("تم استخدام ShowTransfer بنجاح")
                    except Exception as e:
                        print(f"محاولة ShowTransfer فشلت: {str(e)}")

                        # محاولة 4: مع معلمة واحدة فقط
                        try:
                            print("محاولة استخدام ShowTransfer مع معلمة واحدة...")
                            image_file = wia_common_dialog.ShowTransfer(device, 0)
                            print("تم استخدام ShowTransfer مع معلمة واحدة بنجاح")
                        except Exception as e:
                            print(f"محاولة ShowTransfer مع معلمة واحدة فشلت: {str(e)}")

                            # محاولة 5: مع جميع المعلمات
                            try:
                                print("محاولة استخدام ShowTransfer مع جميع المعلمات...")
                                image_file = wia_common_dialog.ShowTransfer(device, wia_intent, wia_device_type)
                                print("تم استخدام ShowTransfer مع جميع المعلمات بنجاح")
                            except Exception as e:
                                print(f"محاولة ShowTransfer مع جميع المعلمات فشلت: {str(e)}")
                                image_file = None

            if image_file:
                print("تم الانتهاء من المسح الضوئي باستخدام CommonDialog بنجاح")

                # الحصول على بيانات الصورة
                temp_file = image_file.FileData.BinaryData
                print(f"تم الحصول على بيانات الصورة، الحجم: {len(temp_file)} بايت")

                # تحويل الصورة مباشرة إلى base64 بدون معالجة
                img_str = base64.b64encode(temp_file).decode()
                print("تم تحويل الصورة إلى base64 بنجاح")

                return f"data:image/jpeg;base64,{img_str}"
            else:
                print("لم يتم الحصول على صورة من CommonDialog")
                return None
        except Exception as e:
            print(f"خطأ عام في استخدام CommonDialog: {str(e)}")
            return None
    except Exception as e:
        print(f"خطأ عام في المسح الضوئي: {str(e)}")
        traceback.print_exc()
        return None

def is_wia_available():
    """
    التحقق مما إذا كانت WIA متوفرة.

    Returns:
        bool: True إذا كانت WIA متوفرة، False خلاف ذلك.
    """
    return WIA_AVAILABLE

def get_wia_error_message():
    """
    الحصول على رسالة الخطأ إذا كانت WIA غير متوفرة.

    Returns:
        str: رسالة الخطأ أو سلسلة فارغة إذا لم يكن هناك خطأ.
    """
    return WIA_ERROR_MESSAGE

def open_scanner_ui(device_id):
    """
    فتح واجهة المستخدم الخاصة بالماسح الضوئي
    """
    if not WIA_AVAILABLE:
        print("مكتبة WIA غير متوفرة")
        return False

    try:
        # إنشاء كائن DeviceManager
        device_manager = win32com.client.Dispatch("WIA.DeviceManager")

        # طباعة معلومات عن الأجهزة المتوفرة للتشخيص
        print(f"عدد الأجهزة المتوفرة: {device_manager.DeviceInfos.Count}")

        # الحصول على جهاز المسح الضوئي
        device = None
        device_found = False

        # محاولة 1: استخدام المعرف المقدم مباشرة
        try:
            # محاولة الحصول على الماسح الضوئي من قائمة الماسحات المتوفرة
            scanners = get_available_scanners()
            print(f"عدد الماسحات الضوئية المتوفرة: {len(scanners)}")

            for scanner in scanners:
                print(f"معرف الماسح الضوئي: {scanner['id']}, الاسم: {scanner['name']}")
                if str(scanner['id']) == str(device_id):
                    device_found = True
                    # استخدام DeviceManager للحصول على الجهاز
                    for i in range(device_manager.DeviceInfos.Count):
                        device_info = device_manager.DeviceInfos(i + 1)
                        if device_info.DeviceID == scanner['id']:
                            device = device_info.Connect()
                            print(f"تم العثور على الجهاز: {scanner['name']}")
                            break
                    break
        except Exception as e:
            print(f"خطأ في محاولة 1: {str(e)}")

        # محاولة 2: استخدام الفهرس بدلاً من المعرف
        if not device and device_id.isdigit():
            try:
                index = int(device_id)
                if 0 <= index < device_manager.DeviceInfos.Count:
                    device_info = device_manager.DeviceInfos(index + 1)
                    device = device_info.Connect()
                    device_found = True
                    print(f"تم العثور على الجهاز باستخدام الفهرس: {index}")
            except Exception as e:
                print(f"خطأ في محاولة 2: {str(e)}")

        # محاولة 3: استخدام أول ماسح ضوئي متوفر
        if not device:
            try:
                # البحث عن أول ماسح ضوئي متوفر
                for i in range(device_manager.DeviceInfos.Count):
                    device_info = device_manager.DeviceInfos(i + 1)
                    # التحقق مما إذا كان الجهاز ماسحًا ضوئيًا
                    if device_info.Type == 1:  # 1 = ماسح ضوئي
                        device = device_info.Connect()
                        device_found = True
                        print(f"تم استخدام أول ماسح ضوئي متوفر: {device_info.DeviceID}")
                        break
            except Exception as e:
                print(f"خطأ في محاولة 3: {str(e)}")

        if not device:
            if device_found:
                print(f"تم العثور على جهاز المسح الضوئي بالمعرف {device_id} ولكن فشل الاتصال به")
            else:
                print(f"لم يتم العثور على جهاز المسح الضوئي بالمعرف {device_id}")
            return False

        # إنشاء كائن CommonDialog
        wia_common_dialog = win32com.client.Dispatch("WIA.CommonDialog")

        # فتح واجهة المستخدم الخاصة بالماسح الضوئي
        try:
            # محاولة استخدام ShowAcquisitionWizard
            wia_common_dialog.ShowAcquisitionWizard(device)
            print("تم فتح واجهة المستخدم الخاصة بالماسح الضوئي باستخدام ShowAcquisitionWizard")
            return True
        except Exception as e:
            print(f"خطأ في استخدام ShowAcquisitionWizard: {str(e)}")

            try:
                # محاولة استخدام ShowDeviceProperties
                wia_common_dialog.ShowDeviceProperties(device)
                print("تم فتح واجهة المستخدم الخاصة بالماسح الضوئي باستخدام ShowDeviceProperties")
                return True
            except Exception as e:
                print(f"خطأ في استخدام ShowDeviceProperties: {str(e)}")

                # محاولة أخيرة: فتح معالج المسح الضوئي العام
                try:
                    # فتح معالج المسح الضوئي العام بدون تحديد جهاز
                    wia_common_dialog.ShowSelectDevice()
                    print("تم فتح معالج اختيار الماسح الضوئي")
                    return True
                except Exception as e:
                    print(f"خطأ في استخدام ShowSelectDevice: {str(e)}")
                    return False
    except Exception as e:
        print(f"خطأ في فتح واجهة المستخدم الخاصة بالماسح الضوئي: {str(e)}")
        traceback.print_exc()
        return False

# اختبار الوحدة
if __name__ == "__main__":
    print("اختبار وحدة WIA المباشرة...")

    if is_wia_available():
        print("WIA متوفرة.")

        # الحصول على قائمة بالماسحات الضوئية
        scanners = get_available_scanners()
        print(f"تم العثور على {len(scanners)} ماسح ضوئي.")

        for i, scanner in enumerate(scanners):
            print(f"{i+1}. {scanner['name']} (ID: {scanner['id']})")

        if scanners:
            # اختبار المسح الضوئي
            scanner_id = scanners[0]['id']
            print(f"\nاختبار المسح الضوئي باستخدام الماسح الضوئي: {scanners[0]['name']}...")

            result = scan_document(scanner_id)
            if result:
                print("تم المسح الضوئي بنجاح.")

                # حفظ الصورة للتحقق
                img_data = base64.b64decode(result.split(',')[1])
                with open("test_scan.jpg", "wb") as f:
                    f.write(img_data)
                print("تم حفظ الصورة في test_scan.jpg")
            else:
                print("فشل المسح الضوئي.")
    else:
        print(f"WIA غير متوفرة: {get_wia_error_message()}")
