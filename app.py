from flask import Flask, render_template, redirect, url_for, request, session, flash, Response, send_from_directory, abort, send_file, jsonify
from models import db, Employee, LeaveRequest, LeaveType, Document
from werkzeug.security import check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime, timed<PERSON>ta
from collections import defaultdict
from sqlalchemy import text
import os
import pandas as pd
import io
import calendar
import uuid
import base64
import shutil
from PIL import Image
import zipfile
import tempfile
from datetime import datetime
import json
import logging
import sys

# إعداد التسجيل
logging.basicConfig(level=logging.DEBUG,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[logging.StreamHandler(sys.stdout)])

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

print("=== بدء تشغيل التطبيق ===")
print("إصدار Python:", sys.version)
print("المسار الحالي:", os.getcwd())

# استيراد وحدات المسح الضوئي
try:
    import direct_wia_scanner
    import scanner_utils
    import pdf_only_scanner
    import vue_scan_approach
except ImportError as e:
    print(f"خطأ في استيراد وحدات المسح الضوئي: {str(e)}")

# تهيئة التطبيق
app = Flask(__name__)
app.secret_key = 'your_secret_key'

# تكوين قاعدة البيانات والمسارات - محسن للبيئة المجمعة
if getattr(sys, 'frozen', False):
    # التطبيق يعمل كملف exe مجمع
    BASE_DIR = os.path.dirname(sys.executable)
    print(f"التطبيق يعمل كملف exe، المسار الأساسي: {BASE_DIR}")
else:
    # التطبيق يعمل كسكريبت Python
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    print(f"التطبيق يعمل كسكريبت Python، المسار الأساسي: {BASE_DIR}")

# تكوين مجلدات التطبيق
instance_dir = os.path.join(BASE_DIR, 'instance')
uploads_dir = os.path.join(BASE_DIR, 'uploads')
documents_dir = os.path.join(uploads_dir, 'documents')
scans_dir = os.path.join(uploads_dir, 'scans')
backups_dir = os.path.join(BASE_DIR, 'backups')

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [instance_dir, uploads_dir, documents_dir, scans_dir, backups_dir]:
    os.makedirs(directory, exist_ok=True)
    print(f"تم التأكد من وجود المجلد: {directory}")

# تكوين قاعدة البيانات
db_path = os.path.join(instance_dir, 'ajazat.db')
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تكوين مجلدات التحميل
app.config['UPLOAD_FOLDER'] = uploads_dir
app.config['DOCUMENTS_FOLDER'] = documents_dir
app.config['SCANS_FOLDER'] = scans_dir
app.config['BACKUP_FOLDER'] = backups_dir

# تكوين Flask للعمل مع المسارات المطلقة
app.config['BASE_DIR'] = BASE_DIR

# طباعة معلومات المجلدات
print("مجلدات التحميل:")
print(f"BASE_DIR: {app.config['BASE_DIR']}")
print(f"UPLOAD_FOLDER: {app.config['UPLOAD_FOLDER']}")
print(f"DOCUMENTS_FOLDER: {app.config['DOCUMENTS_FOLDER']}")
print(f"SCANS_FOLDER: {app.config['SCANS_FOLDER']}")
print(f"BACKUP_FOLDER: {app.config['BACKUP_FOLDER']}")
print(f"DATABASE_PATH: {db_path}")

# تهيئة قاعدة البيانات
db.init_app(app)

# إنشاء جميع الجداول إذا لم تكن موجودة
with app.app_context():
    db.create_all()

# تهيئة وحدة المسح الضوئي PDF فقط
# لا يوجد دالة init في وحدة pdf_only_scanner

# دالة مساعدة للتحقق من صلاحيات المستخدم
def current_user_has_permission(permission):
    """
    التحقق مما إذا كان المستخدم الحالي لديه صلاحية محددة

    Args:
        permission: الصلاحية المطلوب التحقق منها

    Returns:
        bool: True إذا كان المستخدم لديه الصلاحية، False خلاف ذلك
    """
    # إذا لم يكن هناك مستخدم مسجل الدخول، فليس لديه أي صلاحيات
    if not session.get('employee_id'):
        return False

    # الحصول على المستخدم من قاعدة البيانات
    employee = Employee.query.get(session.get('employee_id'))
    if not employee:
        return False

    # التحقق من الصلاحيات المحددة فقط (تجاهل حالة is_admin)
    if not employee.permissions:
        return False

    # التحقق من وجود الصلاحية المطلوبة في قائمة صلاحيات المستخدم
    return permission in employee.permissions.split(',')

print("بدء تشغيل التطبيق على المنفذ 5000...")

# صفحة اختبار الماسح الضوئي
@app.route('/scanner_test')
def scanner_test():
    """
    صفحة اختبار الماسح الضوئي
    """
    return render_template('scanner_test.html')

# صفحة الماسح الضوئي الرئيسية (بدون التحقق من صلاحيات المستخدم)
@app.route('/scanner_main')
def scanner_main():
    """
    صفحة الماسح الضوئي الرئيسية (بدون التحقق من صلاحيات المستخدم)
    """
    return render_template('scanner_test.html')

# صفحة الماسح الضوئي المحسنة
@app.route('/scanner_improved/<int:leave_id>')
def scanner_improved(leave_id):
    """
    صفحة الماسح الضوئي المحسنة التي تستخدم نفس كود صفحة الاختبار
    """
    # الحصول على الإجازة
    leave = LeaveRequest.query.get_or_404(leave_id)

    # الحصول على الموظف
    employee = Employee.query.get(leave.employee_id)

    # الحصول على المستندات الحالية
    documents = Document.query.filter_by(leave_id=leave_id).order_by(Document.upload_date.desc()).all()

    return render_template('scan_document_improved.html',
                          leave=leave,
                          employee=employee,
                          documents=documents,
                          leave_id=leave_id)

# حفظ المستند الممسوح ضوئياً (للاختبار)
@app.route('/save_scanned_document_test', methods=['POST'])
def save_scanned_document_test():
    """
    حفظ المستند الممسوح ضوئياً (للاختبار)
    """
    try:
        # الحصول على بيانات الطلب
        data = request.json
        description = data.get('description', '')
        scan_method = data.get('scan_method', 'pdf_only')

        print(f"طلب حفظ المستند: الوصف={description}, طريقة المسح={scan_method}")

        # التحقق من وجود صفحات ممسوحة
        if scan_method == 'pdf_only':
            page_count = pdf_only_scanner.get_scanned_pages_count()
            if page_count <= 0:
                print("لا توجد صفحات ممسوحة للحفظ")
                return jsonify({'error': 'لا توجد صفحات ممسوحة للحفظ'}), 400

            print(f"عدد الصفحات الممسوحة: {page_count}")

            # إنشاء اسم ملف فريد
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"scan_test_{timestamp}.pdf"

            # حفظ المستند
            output_path = os.path.join('static', 'scanned_images', filename)

            # التأكد من وجود المجلد
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # حفظ المستند باستخدام pdf_only_scanner
            success = pdf_only_scanner.save_document(output_path)

            if not success:
                print("فشل في حفظ المستند")
                return jsonify({'error': 'فشل في حفظ المستند'}), 500

            # إنشاء URL للمستند
            document_url = url_for('static', filename=f'scanned_images/{filename}')

            print(f"تم حفظ المستند بنجاح: {output_path}")
            print(f"URL المستند: {document_url}")

            # إرجاع معلومات المستند
            return jsonify({
                'success': True,
                'message': f'تم حفظ المستند بنجاح',
                'document_url': document_url,
                'filename': filename
            })
        else:
            print(f"طريقة المسح غير مدعومة: {scan_method}")
            return jsonify({'error': f'طريقة المسح غير مدعومة: {scan_method}'}), 400
    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"خطأ في حفظ المستند: {str(e)}")
        return jsonify({'error': f'حدث خطأ أثناء حفظ المستند: {str(e)}'}), 500

# الصفحة الرئيسية
@app.route('/')
def index():
    if not session.get('employee_id'):
        return redirect(url_for('login'))
    return redirect(url_for('dashboard'))

# صفحة تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        print(f"محاولة تسجيل الدخول: اسم المستخدم={username}, كلمة المرور={password}")

        # البحث عن الموظف باستخدام اسم المستخدم
        employee = Employee.query.filter_by(username=username).first()

        if not employee:
            print(f"لم يتم العثور على مستخدم باسم {username}")
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')
            return render_template('login.html')

        print(f"تم العثور على المستخدم: {employee.full_name}, المعرف: {employee.id}, مدير: {employee.is_admin}")
        print(f"كلمة المرور المخزنة: {employee.password_hash}")

        # التحقق من كلمة المرور
        password_correct = employee.check_password(password)
        print(f"نتيجة التحقق من كلمة المرور: {password_correct}")

        if password_correct:
            # حفظ معلومات المستخدم في الجلسة
            session['employee_id'] = employee.id
            session['employee_name'] = employee.full_name
            session['is_admin'] = employee.is_admin  # حفظ حالة المدير الفعلية
            session['permissions'] = employee.permissions or ''  # حفظ الصلاحيات

            print(f"تم تسجيل الدخول بنجاح: {employee.full_name}")
            print(f"بيانات الجلسة: employee_id={session.get('employee_id')}, employee_name={session.get('employee_name')}, is_admin={session.get('is_admin')}")
            print(f"الصلاحيات: {session.get('permissions')}")

            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            print(f"كلمة المرور غير صحيحة لـ {username}")
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')

    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

# لوحة التحكم
@app.route('/dashboard')
def dashboard():
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # الحصول على التاريخ الحالي
    current_date = datetime.now().date()

    # الحصول على جميع الإجازات المعتمدة
    all_approved_leaves = LeaveRequest.query.filter(
        LeaveRequest.status == 'approved'
    ).all()

    # إنشاء قائمة فارغة للإجازات الحالية والمنتهية
    current_leaves = []

    # فحص كل إجازة وتحديد ما إذا كانت حالية أو منتهية
    for leave in all_approved_leaves:
        # تجاهل الإجازات التي تحتوي على "تمت المباشرة" في الملاحظات
        if leave.comment and "تمت المباشرة" in leave.comment:
            continue

        # إضافة الإجازات الحالية والمنتهية التي لم يباشر أصحابها
        if (leave.start_date <= current_date and leave.end_date >= current_date) or (leave.end_date < current_date and not (leave.comment and "تمت المباشرة" in leave.comment)):
            current_leaves.append(leave)

    # الحصول على معلومات الموظفين
    employees_on_leave_info = []
    leave_types_dict = {}  # قاموس لتصنيف الإجازات حسب النوع
    leave_types_count = {}  # قاموس لحساب عدد الإجازات من كل نوع

    for leave in current_leaves:
        employee = Employee.query.get(leave.employee_id)
        if employee:
            leave_type = LeaveType.query.get(leave.leave_type_id)
            leave_type_name = leave_type.name if leave_type else "غير محدد"

            # حساب الأيام المتبقية أو المنقضية
            days_left = (leave.end_date - current_date).days

            # إضافة معلومات الموظف
            employee_info = {
                'employee_id': employee.id,
                'employee_name': employee.full_name,
                'work_location': employee.work_location,
                'leave_id': leave.id,
                'leave_type': leave_type_name,
                'start_date': leave.start_date,
                'end_date': leave.end_date,
                'days_left': days_left,
                'expired': days_left < 0,  # علامة لتحديد ما إذا كانت الإجازة منتهية
                'comment': leave.comment or ''
            }

            employees_on_leave_info.append(employee_info)

            # تصنيف الإجازات حسب النوع
            if leave_type_name not in leave_types_dict:
                leave_types_dict[leave_type_name] = []
                leave_types_count[leave_type_name] = 0

            # إضافة معلومات الموظف والإجازة إلى قاموس الأنواع
            leave_types_dict[leave_type_name].append({
                'employee': employee,
                'leave': leave,
                'days_left': days_left,
                'expired': days_left < 0
            })

            # زيادة عداد هذا النوع من الإجازات
            leave_types_count[leave_type_name] += 1

    # ترتيب الموظفين: أولاً الإجازات المنتهية ثم الإجازات الحالية حسب عدد الأيام المتبقية
    employees_on_leave_info.sort(key=lambda x: (not x['expired'], x['days_left']))

    # ترتيب الإجازات داخل كل نوع
    for leave_type in leave_types_dict:
        leave_types_dict[leave_type].sort(key=lambda x: (not x['expired'], x['days_left']))

    return render_template('dashboard.html',
                          employees_on_leave=employees_on_leave_info,
                          leave_types_dict=leave_types_dict,
                          leave_types_count=leave_types_count)

# الحصول على قائمة بأجهزة المسح الضوئي المتوفرة
@app.route('/get_available_scanners', methods=['GET'])
def get_available_scanners():
    """
    الحصول على قائمة بأجهزة المسح الضوئي المتوفرة
    """
    logger.info("=== بدء تنفيذ دالة get_available_scanners ===")
    print("=== بدء تنفيذ دالة get_available_scanners ===")

    if not session.get('employee_id'):
        logger.warning("خطأ: المستخدم غير مصرح")
        return jsonify({'error': 'غير مصرح'}), 401

    # التحقق من صلاحيات المستخدم (فقط المدير يمكنه استخدام الماسح الضوئي)
    if not session.get('is_admin'):
        logger.warning("خطأ: المستخدم ليس مديرًا")
        return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه الوظيفة'}), 403

    logger.info("المستخدم مصرح ولديه صلاحيات المدير")

    try:
        # تهيئة قائمة الماسحات الضوئية
        scanners_list = []

        # إضافة ماسح ضوئي CANON DR-M260 يدويًا (لأنه معروف أنه موجود)
        canon_scanner = {
            'id': "0",
            'name': "CANON DR-M260 (مضاف يدويًا)",
            'device_id': "{6BDD1FC6-810F-11D0-BEC7-08002BE2092F}\\0005",
            'source': 'direct_wia'
        }
        scanners_list.append(canon_scanner)
        logger.info("تم إضافة ماسح ضوئي CANON DR-M260 يدويًا")

        # إضافة ماسح ضوئي PDF فقط (دائمًا) - هذا هو الماسح الذي يعمل بشكل جيد في صفحة الاختبار
        pdf_scanner = {
            'id': 'pdf_scanner',
            'name': 'ماسح ضوئي PDF (مستحسن)',
            'device_id': 'pdf_scanner',
            'source': 'pdf_only'
        }
        scanners_list.append(pdf_scanner)
        logger.info("تم إضافة ماسح ضوئي PDF")

        # محاولة الحصول على قائمة الماسحات الضوئية الحقيقية من pdf_only_scanner
        try:
            real_scanners = pdf_only_scanner.get_available_scanners()
            logger.info(f"تم العثور على {len(real_scanners)} ماسح ضوئي حقيقي")

            # إضافة الماسحات الضوئية الحقيقية إلى القائمة (باستثناء التي تمت إضافتها بالفعل)
            for scanner in real_scanners:
                # تجاهل الماسحات الضوئية التي تمت إضافتها بالفعل
                if scanner.get('name', '').upper() == "CANON DR-M260" or scanner.get('id') == 'pdf_scanner':
                    continue

                scanner_info = {
                    'id': scanner.get('id', '0'),
                    'name': scanner.get('name', 'ماسح ضوئي غير معروف'),
                    'device_id': scanner.get('device_id', scanner.get('id', '0')),
                    'source': scanner.get('source', 'wia'),
                }
                scanners_list.append(scanner_info)
                logger.info(f"تم إضافة ماسح ضوئي حقيقي: {scanner_info['name']}")
        except Exception as scanner_error:
            logger.error(f"خطأ في الحصول على قائمة الماسحات الضوئية الحقيقية: {str(scanner_error)}")
            import traceback
            traceback.print_exc()

        # إرجاع قائمة الماسحات الضوئية
        response = {
            'success': True,
            'scanners': scanners_list,
            'method': 'enhanced_detection',
            'errors': None,
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        logger.info(f"تم العثور على {len(scanners_list)} جهاز مسح ضوئي بالإجمال")
        logger.info("=== انتهاء تنفيذ دالة get_available_scanners ===")

        return jsonify(response)
    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"خطأ في الحصول على قائمة أجهزة المسح الضوئي: {str(e)}")

        # في حالة حدوث خطأ، إرجاع الماسحات الضوئية الافتراضية
        canon_scanner = {
            'id': "0",
            'name': "CANON DR-M260 (مضاف يدويًا)",
            'device_id': "{6BDD1FC6-810F-11D0-BEC7-08002BE2092F}\\0005",
            'source': 'direct_wia'
        }

        pdf_scanner = {
            'id': 'pdf_scanner',
            'name': 'ماسح ضوئي PDF (مستحسن)',
            'device_id': 'pdf_scanner',
            'source': 'pdf_only'
        }

        response = {
            'success': True,
            'scanners': [canon_scanner, pdf_scanner],
            'method': 'fallback',
            'errors': str(e),
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        logger.info("تم إرجاع الماسحات الضوئية الافتراضية بسبب حدوث خطأ")
        return jsonify(response)

# مسح مستند باستخدام جهاز مسح ضوئي محدد
@app.route('/scan_with_device', methods=['POST'])
def scan_with_device():
    """
    مسح مستند باستخدام جهاز مسح ضوئي محدد
    """
    if not session.get('employee_id'):
        return jsonify({'error': 'غير مصرح'}), 401

    # التحقق من صلاحيات المستخدم (فقط المدير يمكنه استخدام الماسح الضوئي)
    if not session.get('is_admin'):
        return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه الوظيفة'}), 403

    # الحصول على معرف الماسح الضوئي ومصدره من الطلب
    scanner_id = request.json.get('scanner_id', 'pdf_scanner')  # استخدام 'pdf_scanner' كقيمة افتراضية
    scanner_source = request.json.get('scanner_source', 'pdf_only')  # استخدام pdf_only كمصدر افتراضي

    # طباعة معلومات تشخيصية
    print(f"معرف الماسح الضوئي: {scanner_id}, المصدر: {scanner_source}")
    print(f"بيانات الطلب الكاملة: {request.json}")

    if not scanner_id:
        return jsonify({'error': 'لم يتم تحديد جهاز المسح الضوئي'}), 400

    # معالجة خاصة لماسح ضوئي PDF
    if scanner_id == 'pdf_scanner':
        scanner_source = 'pdf_only'
        print("تم تعيين المصدر إلى pdf_only لأن المعرف هو pdf_scanner")

    # الحصول على إعدادات المسح الضوئي (اختياري)
    dpi = request.json.get('dpi', 300)
    use_adf = request.json.get('use_adf', True)  # استخدام وحدة التغذية التلقائية افتراضيًا

    try:
        # طباعة معلومات الإعدادات
        print(f"إعدادات المسح الضوئي: scanner_id={scanner_id}, dpi={dpi}, use_adf={use_adf}")

        # استخدام ماسح PDF فقط إذا كان المصدر هو pdf_only أو إذا كان معرف الجهاز هو pdf_scanner
        if scanner_source == 'pdf_only' or scanner_id == 'pdf_scanner':
            print("استخدام ماسح PDF فقط")

            # مسح المستند باستخدام pdf_only_scanner
            print(f"جاري مسح المستند باستخدام pdf_only_scanner: device_id={scanner_id}, dpi={dpi}, use_adf={use_adf}")

            # استخدام الدالة scan_document مباشرة كما في صفحة الاختبار
            success = pdf_only_scanner.scan_document(
                scanner_id=scanner_id if scanner_id != 'pdf_scanner' else None,
                dpi=dpi,
                use_adf=use_adf
            )

            if not success:
                print("فشل في مسح المستند باستخدام pdf_only_scanner.scan_document")
                return jsonify({'error': 'فشل في مسح المستند'}), 500

            # الحصول على عدد الصفحات الممسوحة
            page_count = pdf_only_scanner.get_scanned_pages_count()
            print(f"تم مسح {page_count} صفحة بنجاح باستخدام pdf_only_scanner")

            # إرجاع معلومات عن الصفحات الممسوحة
            return jsonify({
                'success': True,
                'page_count': page_count,
                'message': f'تم مسح {page_count} صفحة بنجاح',
                'method': 'pdf_only'
            })

        # التعامل مع ماسح CANON DR-M260 أو أي ماسح آخر
        elif scanner_id == '0' or scanner_id == 0:
            print("تم تحديد معرف الجهاز '0'، استخدام ماسح CANON DR-M260")

            # استخدام pdf_only_scanner مع معرف الجهاز '0'
            success = pdf_only_scanner.scan_document(
                scanner_id='0',
                dpi=dpi,
                use_adf=use_adf
            )

            if not success:
                print("فشل في مسح المستند باستخدام ماسح CANON DR-M260")

                # محاولة استخدام ماسح PDF فقط كبديل
                print("محاولة استخدام ماسح PDF فقط كبديل")
                fallback_success = pdf_only_scanner.scan_document(
                    scanner_id=None,  # استخدام القيمة الافتراضية
                    dpi=dpi,
                    use_adf=use_adf
                )

                if not fallback_success:
                    print("فشل في مسح المستند باستخدام ماسح PDF البديل")
                    return jsonify({'error': 'فشل في مسح المستند باستخدام جميع الماسحات المتاحة'}), 500

                # الحصول على عدد الصفحات الممسوحة
                page_count = pdf_only_scanner.get_scanned_pages_count()
                print(f"تم مسح {page_count} صفحة بنجاح باستخدام ماسح PDF البديل")

                return jsonify({
                    'success': True,
                    'page_count': page_count,
                    'message': f'تم مسح {page_count} صفحة بنجاح باستخدام ماسح PDF البديل',
                    'method': 'pdf_only',
                    'used_fallback': True
                })

            # الحصول على عدد الصفحات الممسوحة
            page_count = pdf_only_scanner.get_scanned_pages_count()
            print(f"تم مسح {page_count} صفحة بنجاح باستخدام ماسح CANON DR-M260")

            return jsonify({
                'success': True,
                'page_count': page_count,
                'message': f'تم مسح {page_count} صفحة بنجاح',
                'method': 'canon_dr_m260'
            })

        # التعامل مع أي ماسح آخر
        else:
            print(f"استخدام ماسح ضوئي آخر: {scanner_id}")

            # استخدام pdf_only_scanner مع معرف الجهاز المحدد
            success = pdf_only_scanner.scan_document(
                scanner_id=scanner_id,
                dpi=dpi,
                use_adf=use_adf
            )

            if not success:
                print(f"فشل في مسح المستند باستخدام الماسح {scanner_id}")

                # محاولة استخدام ماسح PDF فقط كبديل
                print("محاولة استخدام ماسح PDF فقط كبديل")
                fallback_success = pdf_only_scanner.scan_document(
                    scanner_id=None,  # استخدام القيمة الافتراضية
                    dpi=dpi,
                    use_adf=use_adf
                )

                if not fallback_success:
                    print("فشل في مسح المستند باستخدام ماسح PDF البديل")
                    return jsonify({'error': 'فشل في مسح المستند باستخدام جميع الماسحات المتاحة'}), 500

                # الحصول على عدد الصفحات الممسوحة
                page_count = pdf_only_scanner.get_scanned_pages_count()
                print(f"تم مسح {page_count} صفحة بنجاح باستخدام ماسح PDF البديل")

                return jsonify({
                    'success': True,
                    'page_count': page_count,
                    'message': f'تم مسح {page_count} صفحة بنجاح باستخدام ماسح PDF البديل',
                    'method': 'pdf_only',
                    'used_fallback': True
                })

            # الحصول على عدد الصفحات الممسوحة
            page_count = pdf_only_scanner.get_scanned_pages_count()
            print(f"تم مسح {page_count} صفحة بنجاح باستخدام الماسح {scanner_id}")

            return jsonify({
                'success': True,
                'page_count': page_count,
                'message': f'تم مسح {page_count} صفحة بنجاح',
                'method': 'custom_scanner'
            })

    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"خطأ في المسح الضوئي: {str(e)}")

        # محاولة استخدام ماسح PDF فقط كبديل في حالة الخطأ
        try:
            print("محاولة استخدام ماسح PDF فقط كبديل بعد حدوث خطأ")
            fallback_success = pdf_only_scanner.scan_document(
                scanner_id=None,  # استخدام القيمة الافتراضية
                dpi=300,
                use_adf=True
            )

            if fallback_success:
                # الحصول على عدد الصفحات الممسوحة
                page_count = pdf_only_scanner.get_scanned_pages_count()
                print(f"تم مسح {page_count} صفحة بنجاح باستخدام ماسح PDF البديل")

                return jsonify({
                    'success': True,
                    'message': 'تم المسح الضوئي بنجاح باستخدام ماسح PDF البديل',
                    'page_count': page_count,
                    'used_fallback': True,
                    'error_handled': str(e)
                })
            else:
                print("فشل في مسح المستند باستخدام ماسح PDF البديل")
        except Exception as fallback_error:
            print(f"خطأ في استخدام ماسح PDF البديل: {str(fallback_error)}")

        return jsonify({'error': f'حدث خطأ أثناء المسح الضوئي: {str(e)}'}), 500

# فتح واجهة المستخدم الخاصة بالماسح الضوئي
@app.route('/open_scanner_ui', methods=['POST'])
def open_scanner_ui():
    """
    فتح واجهة المستخدم الخاصة بالماسح الضوئي
    """
    if not session.get('employee_id'):
        return jsonify({'error': 'غير مصرح'}), 401

    # التحقق من صلاحيات المستخدم (فقط المدير يمكنه استخدام الماسح الضوئي)
    if not session.get('is_admin'):
        return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه الوظيفة'}), 403

    # الحصول على معرف الماسح الضوئي من الطلب
    scanner_id = request.json.get('scanner_id', 'pdf_scanner')  # استخدام 'pdf_scanner' كقيمة افتراضية

    if not scanner_id:
        return jsonify({'error': 'لم يتم تحديد جهاز المسح الضوئي'}), 400

    try:
        # التعامل مع ماسح PDF فقط
        if scanner_id == 'pdf_scanner':
            print("استخدام ماسح PDF فقط")

            # فتح واجهة المستخدم الخاصة بماسح PDF
            result = pdf_only_scanner.open_scanner_ui(scanner_id)

            print(f"نتيجة فتح واجهة المستخدم: {result}")

            if not result.get('success', False):
                error_message = result.get('message', 'فشل في فتح واجهة المستخدم الخاصة بماسح PDF')
                print(f"خطأ في فتح واجهة المستخدم: {error_message}")
                return jsonify({'error': error_message}), 500

            print("تم فتح واجهة المستخدم بنجاح")
            return jsonify({
                'success': True,
                'message': 'تم فتح واجهة المستخدم الخاصة بماسح PDF بنجاح'
            })

        # التعامل مع ماسح CANON DR-M260
        elif scanner_id == '0' or scanner_id == 0:
            print("استخدام ماسح CANON DR-M260")

            # فتح واجهة المستخدم الخاصة بماسح CANON DR-M260
            result = pdf_only_scanner.open_scanner_ui('0')

            print(f"نتيجة فتح واجهة المستخدم: {result}")

            if not result.get('success', False):
                error_message = result.get('message', 'فشل في فتح واجهة المستخدم الخاصة بماسح CANON DR-M260')
                print(f"خطأ في فتح واجهة المستخدم: {error_message}")

                # محاولة استخدام ماسح PDF فقط كبديل
                print("محاولة استخدام ماسح PDF فقط كبديل")
                fallback_result = pdf_only_scanner.open_scanner_ui('pdf_scanner')

                if not fallback_result.get('success', False):
                    print(f"فشل في فتح واجهة المستخدم الخاصة بماسح PDF البديل: {fallback_result.get('message', '')}")
                    return jsonify({'error': error_message}), 500

                print("تم فتح واجهة المستخدم الخاصة بماسح PDF البديل بنجاح")
                return jsonify({
                    'success': True,
                    'message': 'تم فتح واجهة المستخدم الخاصة بماسح PDF البديل بنجاح',
                    'used_fallback': True
                })

            print("تم فتح واجهة المستخدم بنجاح")
            return jsonify({
                'success': True,
                'message': 'تم فتح واجهة المستخدم الخاصة بماسح CANON DR-M260 بنجاح'
            })

        # التعامل مع أي ماسح آخر
        else:
            print(f"استخدام ماسح ضوئي آخر: {scanner_id}")

            # فتح واجهة المستخدم الخاصة بالماسح المحدد
            result = pdf_only_scanner.open_scanner_ui(scanner_id)

            print(f"نتيجة فتح واجهة المستخدم: {result}")

            if not result.get('success', False):
                error_message = result.get('message', f'فشل في فتح واجهة المستخدم الخاصة بالماسح {scanner_id}')
                print(f"خطأ في فتح واجهة المستخدم: {error_message}")

                # محاولة استخدام ماسح PDF فقط كبديل
                print("محاولة استخدام ماسح PDF فقط كبديل")
                fallback_result = pdf_only_scanner.open_scanner_ui('pdf_scanner')

                if not fallback_result.get('success', False):
                    print(f"فشل في فتح واجهة المستخدم الخاصة بماسح PDF البديل: {fallback_result.get('message', '')}")
                    return jsonify({'error': error_message}), 500

                print("تم فتح واجهة المستخدم الخاصة بماسح PDF البديل بنجاح")
                return jsonify({
                    'success': True,
                    'message': 'تم فتح واجهة المستخدم الخاصة بماسح PDF البديل بنجاح',
                    'used_fallback': True
                })

            print("تم فتح واجهة المستخدم بنجاح")
            return jsonify({
                'success': True,
                'message': f'تم فتح واجهة المستخدم الخاصة بالماسح {scanner_id} بنجاح'
            })

    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"خطأ في فتح واجهة المستخدم الخاصة بالماسح الضوئي: {str(e)}")

        # محاولة استخدام ماسح PDF فقط كبديل في حالة الخطأ
        try:
            print("محاولة استخدام ماسح PDF فقط كبديل بعد حدوث خطأ")
            fallback_result = pdf_only_scanner.open_scanner_ui('pdf_scanner')

            if fallback_result.get('success', False):
                print("تم فتح واجهة المستخدم الخاصة بماسح PDF البديل بنجاح")
                return jsonify({
                    'success': True,
                    'message': 'تم فتح واجهة المستخدم الخاصة بماسح PDF البديل بنجاح',
                    'used_fallback': True,
                    'error_handled': str(e)
                })
        except Exception as fallback_error:
            print(f"خطأ في استخدام ماسح PDF البديل: {str(fallback_error)}")

        return jsonify({'error': f'حدث خطأ أثناء فتح واجهة المستخدم الخاصة بالماسح الضوئي: {str(e)}'}), 500

# مسح مستند ضوئيًا
@app.route('/scan_document/<int:leave_id>', methods=['GET', 'POST'])
def scan_document(leave_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم (فقط المدير يمكنه مسح المستندات ضوئيًا)
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # إعادة توجيه إلى صفحة الماسح الضوئي المحسنة
    return redirect(url_for('scanner_improved', leave_id=leave_id))

# إدارة الموظفين
@app.route('/employees')
def employees():
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin') and not current_user_has_permission('view_employees'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على معلمات البحث
    search_query = request.args.get('search', '')
    department_filter = request.args.get('department', '')
    location_filter = request.args.get('location', '')

    # استعلام قاعدة البيانات
    query = Employee.query.filter(Employee.is_admin == False)

    # تطبيق البحث إذا تم تحديده
    if search_query:
        query = query.filter(
            (Employee.full_name.ilike(f'%{search_query}%')) |
            (Employee.job_title.ilike(f'%{search_query}%')) |
            (Employee.work_location.ilike(f'%{search_query}%'))
        )

    # تطبيق تصفية القسم إذا تم تحديدها
    if department_filter:
        query = query.filter(Employee.department == department_filter)

    # تطبيق تصفية موقع العمل إذا تم تحديدها
    if location_filter:
        query = query.filter(Employee.work_location == location_filter)

    # الحصول على قائمة الموظفين
    employees = query.order_by(Employee.full_name).all()

    # الحصول على قائمة الأقسام وأماكن العمل للتصفية
    departments = db.session.query(Employee.department).filter(Employee.department != None).distinct().all()
    departments = [dept[0] for dept in departments if dept[0]]

    locations = db.session.query(Employee.work_location).filter(Employee.work_location != None).distinct().all()
    locations = [loc[0] for loc in locations if loc[0]]

    # التعامل مع طلبات AJAX
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return render_template('employees_table.html',
                              employees=employees,
                              search_query=search_query,
                              department_filter=department_filter,
                              location_filter=location_filter)

    return render_template('employees.html',
                          employees=employees,
                          departments=departments,
                          locations=locations,
                          search_query=search_query,
                          department_filter=department_filter,
                          location_filter=location_filter)

@app.route('/add_employee', methods=['GET', 'POST'])
def add_employee():
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin') and not current_user_has_permission('add_employees'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        # الحصول على بيانات النموذج
        full_name = request.form.get('full_name')
        employee_number = request.form.get('employee_number')
        job_title = request.form.get('job_title')
        work_location = request.form.get('work_location')
        department = request.form.get('department')
        leave_balance = request.form.get('leave_balance', 36)
        is_admin = 'is_admin' in request.form

        # إنشاء موظف جديد
        employee = Employee(
            full_name=full_name,
            employee_number=employee_number,
            job_title=job_title,
            work_location=work_location,
            department=department,
            leave_balance=leave_balance,
            is_admin=is_admin,
            is_active=True
        )

        # حفظ الموظف في قاعدة البيانات
        db.session.add(employee)
        db.session.commit()

        flash(f'تم إضافة الموظف {full_name} بنجاح', 'success')
        return redirect(url_for('employees'))

    return render_template('add_employee.html')

@app.route('/edit_employee/<int:employee_id>', methods=['GET', 'POST'])
def edit_employee(employee_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin') and not current_user_has_permission('edit_employees'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على الموظف
    employee = Employee.query.get_or_404(employee_id)

    if request.method == 'POST':
        # تحديث بيانات الموظف
        employee.full_name = request.form.get('full_name')
        employee.employee_number = request.form.get('employee_number')
        employee.job_title = request.form.get('job_title')
        employee.work_location = request.form.get('work_location')
        employee.department = request.form.get('department')
        employee.leave_balance = request.form.get('leave_balance')
        employee.is_admin = 'is_admin' in request.form

        # حفظ التغييرات
        db.session.commit()

        flash(f'تم تحديث بيانات الموظف {employee.full_name} بنجاح', 'success')
        return redirect(url_for('employees'))

    return render_template('edit_employee.html', employee=employee)

@app.route('/delete_employee/<int:employee_id>', methods=['GET', 'POST'])
def delete_employee(employee_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin') and not current_user_has_permission('delete_employees'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على الموظف
    employee = Employee.query.get_or_404(employee_id)

    # التحقق من أن المستخدم لا يحاول حذف نفسه
    if employee.id == session.get('employee_id'):
        flash('لا يمكنك حذف حسابك الخاص', 'danger')
        return redirect(url_for('employees'))

    # حذف الموظف
    db.session.delete(employee)
    db.session.commit()

    flash(f'تم حذف الموظف {employee.full_name} بنجاح', 'success')
    return redirect(url_for('employees'))

@app.route('/import_employees', methods=['GET', 'POST'])
def import_employees():
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        # التحقق من وجود ملف
        if 'file' not in request.files:
            flash('لم يتم تحديد ملف', 'danger')
            return redirect(request.url)

        file = request.files['file']

        # التحقق من أن الملف ليس فارغًا
        if file.filename == '':
            flash('لم يتم تحديد ملف', 'danger')
            return redirect(request.url)

        # التحقق من امتداد الملف
        if not file.filename.endswith(('.xlsx', '.xls', '.csv')):
            flash('يجب أن يكون الملف بتنسيق Excel أو CSV', 'danger')
            return redirect(request.url)

        try:
            # قراءة الملف
            df = pd.read_excel(file) if file.filename.endswith(('.xlsx', '.xls')) else pd.read_csv(file)

            # التحقق من وجود الأعمدة المطلوبة
            required_columns = ['full_name', 'job_title', 'work_location']
            for col in required_columns:
                if col not in df.columns:
                    flash(f'الملف يجب أن يحتوي على عمود {col}', 'danger')
                    return redirect(request.url)

            # استيراد الموظفين
            imported_count = 0
            for idx, row in df.iterrows(): # تم تغيير اسم المتغير من index إلى idx
                # التحقق من وجود الموظف
                existing_employee = Employee.query.filter_by(full_name=row['full_name']).first()
                if existing_employee:
                    continue

                # قراءة الرقم الوظيفي (مع التحقق من وجود العمود والقيم الفارغة)
                employee_number = str(row["employee_number"]) if "employee_number" in row and not pd.isna(row["employee_number"]) else None
                print(f"DEBUG: Row {idx + 2}, Employee Number Read: {employee_number}, Type: {type(employee_number)}") # DEBUG PRINT ADDED

                # إنشاء موظف جديد مع الرقم الوظيفي
                employee = Employee(
                    full_name=row['full_name'],
                    job_title=row['job_title'],
                    work_location=row['work_location'],
                    employee_number=employee_number, # تمت إضافة الرقم الوظيفي هنا
                    leave_balance=36,
                    is_admin=False,
                    is_active=True
                )

                # إضافة الموظف إلى قاعدة البيانات
                db.session.add(employee)
                imported_count += 1

            # حفظ التغييرات
            db.session.commit()

            flash(f'تم استيراد {imported_count} موظف بنجاح', 'success')
            return redirect(url_for('employees'))

        except Exception as e:
            flash(f'حدث خطأ أثناء استيراد الموظفين: {str(e)}', 'danger')
            return redirect(request.url)

    return render_template('import_employees.html')

# إدارة الإجازات
@app.route('/employee_leaves/<int:employee_id>')
def employee_leaves(employee_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على الموظف
    employee = Employee.query.get_or_404(employee_id)

    # الحصول على إجازات الموظف
    leaves = LeaveRequest.query.filter_by(employee_id=employee_id).order_by(LeaveRequest.start_date.desc()).all()

    # الحصول على أنواع الإجازات
    leave_types = LeaveType.query.all()
    leave_type_map = {lt.id: lt for lt in leave_types}

    # إضافة التاريخ الحالي للقالب
    today = datetime.now().date()

    return render_template('employee_leaves.html',
                          employee=employee,
                          leaves=leaves,
                          leave_type_map=leave_type_map,
                          today=today)

@app.route('/add_leave/<int:employee_id>', methods=['GET', 'POST'])
def add_leave(employee_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin') and not current_user_has_permission('add_leaves'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على الموظف
    employee = Employee.query.get_or_404(employee_id)

    # الحصول على أنواع الإجازات
    leave_types = LeaveType.query.all()

    if request.method == 'POST':
        # الحصول على بيانات النموذج
        leave_type_id = request.form.get('leave_type_id')
        start_date = request.form.get('start_date')
        end_date = request.form.get('end_date')
        days_count = request.form.get('days_count')
        return_date = request.form.get('return_date')  # إضافة حقل تاريخ العودة إلى العمل
        admin_order_date = request.form.get('order_date')
        admin_order_number = request.form.get('order_number')
        admin_order_source = request.form.get('order_source')
        comment = request.form.get('comment')

        # تحويل التواريخ إلى كائنات datetime
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date() if start_date else None
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else None
        return_date = datetime.strptime(return_date, '%Y-%m-%d').date() if return_date else None  # تحويل تاريخ العودة
        admin_order_date = datetime.strptime(admin_order_date, '%Y-%m-%d').date() if admin_order_date else None

        # إنشاء طلب إجازة جديد
        leave = LeaveRequest(
            employee_id=employee_id,
            leave_type_id=leave_type_id,
            start_date=start_date,
            end_date=end_date,
            days_count=int(days_count) if days_count else 0,
            return_date=return_date,  # إضافة تاريخ العودة إلى العمل
            order_date=admin_order_date,
            order_number=admin_order_number,
            order_source=admin_order_source,
            comment=comment,
            status='approved'
        )

        # حفظ طلب الإجازة في قاعدة البيانات
        db.session.add(leave)

        # تحديث رصيد الإجازات للموظف
        if days_count and int(days_count) > 0:
            employee.leave_balance -= int(days_count)
            if employee.leave_balance < 0:
                employee.leave_balance = 0

        db.session.commit()

        flash(f'تم إضافة الإجازة للموظف {employee.full_name} بنجاح', 'success')
        return redirect(url_for('employee_leaves', employee_id=employee_id))

    return render_template('add_leave.html',
                          employee=employee,
                          leave_types=leave_types)

@app.route('/edit_leave/<int:leave_id>', methods=['GET', 'POST'])
def edit_leave(leave_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin') and not current_user_has_permission('edit_leaves'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على الإجازة
    leave = LeaveRequest.query.get_or_404(leave_id)

    # الحصول على الموظف
    employee = Employee.query.get(leave.employee_id)

    # الحصول على أنواع الإجازات
    leave_types = LeaveType.query.all()

    if request.method == 'POST':
        # حفظ المدة القديمة لاستخدامها في تحديث رصيد الإجازات
        old_days_count = leave.days_count

        # تحديث بيانات الإجازة
        leave.leave_type_id = request.form.get('leave_type_id')
        leave.start_date = datetime.strptime(request.form.get('start_date'), '%Y-%m-%d').date() if request.form.get('start_date') else None
        leave.end_date = datetime.strptime(request.form.get('end_date'), '%Y-%m-%d').date() if request.form.get('end_date') else None
        days_count = request.form.get('days_count')  # تغيير من duration إلى days_count
        leave.days_count = int(days_count) if days_count else 0
        leave.return_date = datetime.strptime(request.form.get('return_date'), '%Y-%m-%d').date() if request.form.get('return_date') else None  # إضافة تاريخ العودة
        leave.order_date = datetime.strptime(request.form.get('order_date'), '%Y-%m-%d').date() if request.form.get('order_date') else None  # تغيير من admin_order_date إلى order_date
        leave.order_number = request.form.get('order_number')  # تغيير من admin_order_number إلى order_number
        leave.order_source = request.form.get('order_source')  # تغيير من admin_order_source إلى order_source
        leave.comment = request.form.get('comment')

        # تحديث رصيد الإجازات للموظف
        if old_days_count and int(old_days_count) > 0:
            employee.leave_balance += int(old_days_count)

        if leave.days_count and int(leave.days_count) > 0:
            employee.leave_balance -= int(leave.days_count)
            if employee.leave_balance < 0:
                employee.leave_balance = 0

        db.session.commit()

        flash(f'تم تحديث الإجازة للموظف {employee.full_name} بنجاح', 'success')
        return redirect(url_for('employee_leaves', employee_id=employee.id))

    return render_template('edit_leave.html',
                          leave=leave,
                          employee=employee,
                          leave_types=leave_types)

@app.route('/delete_leave/<int:leave_id>', methods=['GET', 'POST'])
def delete_leave(leave_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin') and not current_user_has_permission('delete_leaves'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على الإجازة
    leave = LeaveRequest.query.get_or_404(leave_id)

    # الحصول على الموظف
    employee = Employee.query.get(leave.employee_id)

    # استعادة رصيد الإجازات للموظف
    if leave.days_count and int(leave.days_count) > 0:
        employee.leave_balance += int(leave.days_count)

    # حذف الإجازة
    db.session.delete(leave)
    db.session.commit()

    flash(f'تم حذف الإجازة للموظف {employee.full_name} بنجاح', 'success')
    return redirect(url_for('employee_leaves', employee_id=employee.id))

@app.route('/leave_types')
def leave_types():
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin') and not current_user_has_permission('manage_leave_types'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على أنواع الإجازات
    leave_types = LeaveType.query.all()

    return render_template('leave_types.html', leave_types=leave_types)

@app.route('/add_leave_type', methods=['GET', 'POST'])
def add_leave_type():
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        # الحصول على بيانات النموذج
        name = request.form.get('name')
        description = request.form.get('description')
        days_allowed = request.form.get('days_allowed')

        # إنشاء نوع إجازة جديد
        leave_type = LeaveType(
            name=name,
            description=description,
            days_allowed=days_allowed if days_allowed else None
        )

        # حفظ نوع الإجازة في قاعدة البيانات
        db.session.add(leave_type)
        db.session.commit()

        flash(f'تم إضافة نوع الإجازة {name} بنجاح', 'success')
        return redirect(url_for('leave_types'))

    return render_template('add_leave_type.html')

@app.route('/edit_leave_type/<int:leave_type_id>', methods=['GET', 'POST'])
def edit_leave_type(leave_type_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على نوع الإجازة
    leave_type = LeaveType.query.get_or_404(leave_type_id)

    if request.method == 'POST':
        # تحديث بيانات نوع الإجازة
        leave_type.name = request.form.get('name')
        leave_type.description = request.form.get('description')
        leave_type.days_allowed = request.form.get('days_allowed') if request.form.get('days_allowed') else None

        db.session.commit()

        flash(f'تم تحديث نوع الإجازة {leave_type.name} بنجاح', 'success')
        return redirect(url_for('leave_types'))

    return render_template('edit_leave_type.html', leave_type=leave_type)

@app.route('/delete_leave_type/<int:leave_type_id>', methods=['GET', 'POST'])
def delete_leave_type(leave_type_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على نوع الإجازة
    leave_type = LeaveType.query.get_or_404(leave_type_id)

    # التحقق من عدم وجود إجازات من هذا النوع
    leaves = LeaveRequest.query.filter_by(leave_type_id=leave_type_id).first()
    if leaves:
        flash(f'لا يمكن حذف نوع الإجازة {leave_type.name} لأنه مستخدم في إجازات موجودة', 'danger')
        return redirect(url_for('leave_types'))

    # حذف نوع الإجازة
    db.session.delete(leave_type)
    db.session.commit()

    flash(f'تم حذف نوع الإجازة {leave_type.name} بنجاح', 'success')
    return redirect(url_for('leave_types'))

# إدارة المستندات
@app.route('/upload_document/<int:leave_id>', methods=['GET', 'POST'])
def upload_document(leave_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin') and not current_user_has_permission('manage_documents'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على الإجازة
    leave = LeaveRequest.query.get_or_404(leave_id)

    # الحصول على الموظف
    employee = Employee.query.get(leave.employee_id)

    if request.method == 'POST':
        # التحقق من وجود ملف
        if 'document' not in request.files:
            flash('لم يتم تحديد ملف', 'danger')
            return redirect(request.url)

        file = request.files['document']

        # التحقق من أن الملف ليس فارغًا
        if file.filename == '':
            flash('لم يتم تحديد ملف', 'danger')
            return redirect(request.url)

        # التحقق من امتداد الملف
        allowed_extensions = {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'}
        if not '.' in file.filename or file.filename.rsplit('.', 1)[1].lower() not in allowed_extensions:
            flash('نوع الملف غير مسموح به', 'danger')
            return redirect(request.url)

        # إنشاء اسم فريد للملف
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4().hex}_{filename}"

        # حفظ الملف
        file_path = os.path.join(app.config['DOCUMENTS_FOLDER'], unique_filename)
        file.save(file_path)

        # إنشاء سجل للمستند
        document = Document(
            leave_id=leave_id,
            file_name=unique_filename,
            file_path=file_path,
            file_type=filename.rsplit('.', 1)[1].lower(),
            description=request.form.get('description', ''),
            document_type='uploaded',
            upload_date=datetime.now()
        )

        # حفظ السجل في قاعدة البيانات
        db.session.add(document)
        db.session.commit()

        flash('تم رفع المستند بنجاح', 'success')
        return redirect(url_for('employee_leaves', employee_id=employee.id))

    return render_template('upload_document.html',
                          leave=leave,
                          employee=employee)

@app.route('/delete_document/<int:document_id>', methods=['GET', 'POST'])
def delete_document(document_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على المستند
    document = Document.query.get_or_404(document_id)

    # الحصول على الإجازة والموظف
    leave = LeaveRequest.query.get(document.leave_id)
    employee = Employee.query.get(leave.employee_id) if leave else None

    # حذف الملف من القرص
    if document.file_path and os.path.exists(document.file_path):
        try:
            # تغيير صلاحيات الملف قبل الحذف
            import stat
            import time

            # محاولة تغيير صلاحيات الملف
            os.chmod(document.file_path, stat.S_IWRITE | stat.S_IREAD)

            # محاولة حذف الملف عدة مرات في حالة الفشل
            max_attempts = 5
            for attempt in range(max_attempts):
                try:
                    os.remove(document.file_path)
                    print(f"تم حذف الملف بنجاح: {document.file_path}")
                    break
                except Exception as e:
                    print(f"محاولة {attempt+1}/{max_attempts} لحذف الملف فشلت: {str(e)}")
                    if attempt < max_attempts - 1:
                        # انتظار قبل المحاولة مرة أخرى
                        time.sleep(0.5)
                    else:
                        # إذا فشلت جميع المحاولات، حاول استخدام طريقة بديلة
                        try:
                            import shutil
                            # محاولة نقل الملف إلى مجلد مؤقت ثم حذفه
                            temp_dir = os.path.join(app.config['DOCUMENTS_FOLDER'], 'temp_deleted')
                            if not os.path.exists(temp_dir):
                                os.makedirs(temp_dir)

                            # نقل الملف بدلاً من حذفه
                            shutil.move(document.file_path, os.path.join(temp_dir, os.path.basename(document.file_path)))
                            print(f"تم نقل الملف إلى المجلد المؤقت: {document.file_path}")
                        except Exception as move_error:
                            print(f"فشل في نقل الملف: {str(move_error)}")
                            flash(f'لم يتم حذف الملف من القرص، ولكن تم حذفه من قاعدة البيانات', 'warning')
        except Exception as e:
            print(f"خطأ في حذف الملف: {str(e)}")
            flash(f'حدث خطأ أثناء حذف الملف: {str(e)}', 'danger')
            # استمر في حذف السجل من قاعدة البيانات حتى لو فشل حذف الملف

    # حذف المستند من قاعدة البيانات
    db.session.delete(document)
    db.session.commit()

    flash('تم حذف المستند بنجاح', 'success')

    # إعادة التوجيه إلى صفحة إجازات الموظف أو لوحة التحكم
    if employee:
        return redirect(url_for('employee_leaves', employee_id=employee.id))
    else:
        return redirect(url_for('dashboard'))

@app.route('/view_documents/<int:leave_id>')
def view_documents(leave_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin') and not current_user_has_permission('view_documents'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على الإجازة
    leave = LeaveRequest.query.get_or_404(leave_id)

    # الحصول على الموظف
    employee = Employee.query.get(leave.employee_id)

    # الحصول على المستندات
    documents = Document.query.filter_by(leave_id=leave_id).order_by(Document.upload_date.desc()).all()

    return render_template('view_documents.html',
                          leave=leave,
                          employee=employee,
                          documents=documents)

@app.route('/direct_image_viewer/<int:document_id>')
def direct_image_viewer(document_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على المستند
    document = Document.query.get_or_404(document_id)

    # التحقق من وجود الملف
    if not document.file_path or not os.path.exists(document.file_path):
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('dashboard'))

    # التحقق من نوع الملف
    if document.file_type.lower() not in ['jpg', 'jpeg', 'png', 'gif', 'pdf']:
        # إذا كان الملف ليس صورة أو PDF، قم بتنزيله
        return send_file(document.file_path, as_attachment=True, download_name=document.file_name)

    # عرض الصورة أو ملف PDF
    return render_template('direct_image_viewer.html', document=document)

@app.route('/print_image_a4/<int:document_id>')
def print_image_a4(document_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على المستند
    document = Document.query.get_or_404(document_id)

    # التحقق من وجود الملف
    if not document.file_path or not os.path.exists(document.file_path):
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('dashboard'))

    # التحقق من نوع الملف - PDF فقط
    if document.file_type.lower() != 'pdf':
        flash('يمكن طباعة ملفات PDF فقط بهذه الطريقة', 'danger')
        return redirect(url_for('direct_image_viewer', document_id=document_id))

    # عرض صفحة الطباعة
    return render_template('print_image_a4.html', document=document)

@app.route('/save_scanned_document/<int:leave_id>', methods=['POST'])
def save_scanned_document(leave_id):
    """
    حفظ المستند الممسوح ضوئيًا بصيغة PDF فقط
    """
    print(f"=== بدء تنفيذ دالة save_scanned_document مع leave_id={leave_id} ===")

    # تجاوز التحقق من صلاحيات المستخدم مؤقتًا لأغراض التصحيح
    # if not session.get('employee_id'):
    #     print("خطأ: المستخدم غير مصرح")
    #     return jsonify({'error': 'غير مصرح'}), 401
    #
    # # التحقق من صلاحيات المستخدم (فقط المدير يمكنه مسح المستندات ضوئيًا)
    # if not session.get('is_admin'):
    #     print("خطأ: المستخدم ليس مديرًا")
    #     return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه الوظيفة'}), 403

    print("تم تجاوز التحقق من صلاحيات المستخدم مؤقتًا لأغراض التصحيح")

    # الحصول على بيانات المستند
    print("جاري الحصول على بيانات المستند...")

    if not request.json:
        print("خطأ: لم يتم استلام أي بيانات")
        return jsonify({'error': 'لم يتم استلام أي بيانات'}), 400

    description = request.json.get('description', '')
    scan_method = request.json.get('scan_method', 'pdf_only')

    print(f"بيانات المستند: description={description}, scan_method={scan_method}")

    try:
        # التحقق من وجود الإجازة
        leave = LeaveRequest.query.get(leave_id)
        if not leave:
            print("خطأ: الإجازة غير موجودة")
            return jsonify({'error': 'الإجازة غير موجودة'}), 404

        print("تم العثور على الإجازة، جاري معالجة المستند...")

        # استخدام PDF فقط
        print("جاري إنشاء ملف PDF من الصفحات الممسوحة...")

        # إنشاء اسم ملف مؤقت للحفظ
        temp_pdf_path = os.path.join(tempfile.gettempdir(), f"temp_scan_{uuid.uuid4().hex}.pdf")
        pdf_result = pdf_only_scanner.save_as_pdf(temp_pdf_path)

        if not pdf_result or not os.path.exists(temp_pdf_path):
            print("خطأ: فشل في إنشاء ملف PDF")
            return jsonify({'error': 'فشل في إنشاء ملف PDF من الصفحات الممسوحة'}), 500

        # قراءة محتوى ملف PDF
        with open(temp_pdf_path, 'rb') as f:
            pdf_data = f.read()

        # حذف الملف المؤقت بعد قراءته
        try:
            os.remove(temp_pdf_path)
        except Exception as e:
            print(f"تحذير: فشل في حذف الملف المؤقت: {str(e)}")

        # الحصول على معلومات الموظف والإجازة
        leave = LeaveRequest.query.get(leave_id)
        employee = Employee.query.get(leave.employee_id)

        # إنشاء اسم ملف مختصر وبسيط
        timestamp = datetime.now().strftime("%Y%m%d")
        employee_id = str(employee.id) if employee else "0"
        file_type = 'pdf'

        # التأكد من وجود مجلد المستندات
        if not os.path.exists(app.config['DOCUMENTS_FOLDER']):
            os.makedirs(app.config['DOCUMENTS_FOLDER'])

        # إنشاء اسم فريد للملف بصيغة مختصرة: emp_ID_DATE_SHORTCODE.pdf
        unique_filename = f"emp_{employee_id}_{timestamp}_{uuid.uuid4().hex[:6]}.{file_type}"
        dest_path = os.path.join(app.config['DOCUMENTS_FOLDER'], unique_filename)

        # حفظ الملف
        with open(dest_path, 'wb') as f:
            f.write(pdf_data)

        print(f"تم حفظ الملف في: {dest_path}")

        # إنشاء سجل للمستند
        document = Document(
            leave_id=leave_id,
            file_name=unique_filename,
            file_path=dest_path,
            file_type=file_type,
            description=description or f"مستند {employee.full_name}",  # استخدام اسم الموظف إذا لم يتم تحديد وصف
            document_type='scanned',
            upload_date=datetime.now()
        )

        # حفظ السجل في قاعدة البيانات
        db.session.add(document)
        db.session.commit()

        print(f"تم حفظ المستند في قاعدة البيانات بنجاح، معرف المستند: {document.id}")

        # مسح الصفحات الممسوحة بعد الحفظ
        # تأكد من أن الملف تم حفظه بنجاح قبل مسح الصفحات
        if os.path.exists(dest_path) and os.path.getsize(dest_path) > 0:
            pdf_only_scanner.clear_scanned_pages()
            print("تم مسح الصفحات الممسوحة من الذاكرة")
        else:
            print("تحذير: لم يتم مسح الصفحات الممسوحة من الذاكرة لأن الملف لم يتم حفظه بنجاح")

        return jsonify({
            'success': True,
            'document_id': document.id,
            'message': 'تم حفظ المستند بنجاح'
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"خطأ في حفظ المستند الممسوح ضوئيًا: {str(e)}")
        return jsonify({'error': f'حدث خطأ أثناء حفظ المستند: {str(e)}'}), 500

# إدارة المستخدمين
@app.route('/user_management')
def user_management():
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    employee = Employee.query.get(session.get('employee_id'))
    if not employee or (not employee.is_admin and not current_user_has_permission('manage_users')):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على جميع المستخدمين الذين لديهم اسم مستخدم (مدراء وعاديين)
    users = Employee.query.filter(Employee.username.isnot(None)).all()

    return render_template('user_management.html', users=users)

@app.route('/add_user', methods=['GET', 'POST'])
def add_user():
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    employee = Employee.query.get(session.get('employee_id'))
    if not employee or (not employee.is_admin and not current_user_has_permission('manage_users')):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        # الحصول على بيانات النموذج
        username = request.form.get('username')
        password = request.form.get('password')
        full_name = request.form.get('full_name')

        # التحقق من وجود اسم المستخدم
        existing_user = Employee.query.filter_by(username=username).first()
        if existing_user:
            flash('اسم المستخدم موجود بالفعل', 'danger')
            return redirect(request.url)

        # تحديد ما إذا كان المستخدم مديرًا أم مستخدمًا عاديًا
        user_type = request.form.get('user_type', 'normal')
        is_admin = (user_type == 'admin')

        # إنشاء مستخدم جديد
        user = Employee(
            username=username,
            full_name=full_name,
            is_admin=is_admin,
            is_active=True
        )

        # تعيين كلمة المرور
        user.set_password(password)

        # تعيين الصلاحيات
        permissions = request.form.getlist('permissions')
        # تحويل قائمة الصلاحيات إلى نص مفصول بفواصل
        user.permissions = ','.join(permissions) if permissions else ''

        # حفظ المستخدم في قاعدة البيانات
        db.session.add(user)
        db.session.commit()

        flash(f'تم إضافة المستخدم {full_name} بنجاح', 'success')
        return redirect(url_for('user_management'))

    return render_template('add_user.html')

@app.route('/edit_user/<int:user_id>', methods=['GET', 'POST'])
def edit_user(user_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    employee = Employee.query.get(session.get('employee_id'))
    if not employee or (not employee.is_admin and not current_user_has_permission('manage_users')):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على المستخدم
    user = Employee.query.get_or_404(user_id)

    if request.method == 'POST':
        # تحديث بيانات المستخدم
        user.full_name = request.form.get('full_name')
        user.job_title = request.form.get('job_title')
        user.work_location = request.form.get('work_location')
        user.department = request.form.get('department')
        user.leave_balance = request.form.get('leave_balance')

        # تحديث حالة المدير
        user.is_admin = 'is_admin' in request.form

        # تحديث كلمة المرور إذا تم تقديمها
        new_password = request.form.get('password')
        if new_password:
            user.set_password(new_password)

        # تحديث الصلاحيات
        permissions = request.form.getlist('permissions')
        # تحويل قائمة الصلاحيات إلى نص مفصول بفواصل
        user.permissions = ','.join(permissions) if permissions else ''

        # طباعة معلومات التصحيح
        print(f"تحديث صلاحيات المستخدم {user.full_name}:")
        print(f"is_admin: {user.is_admin}")
        print(f"permissions: {user.permissions}")

        db.session.commit()

        flash(f'تم تحديث بيانات المستخدم {user.full_name} بنجاح', 'success')
        return redirect(url_for('user_management'))

    return render_template('edit_user.html', user=user)

@app.route('/delete_user/<int:user_id>', methods=['GET', 'POST'])
def delete_user(user_id):
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    employee = Employee.query.get(session.get('employee_id'))
    if not employee or (not employee.is_admin and not current_user_has_permission('manage_users')):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على المستخدم
    user = Employee.query.get_or_404(user_id)

    # تم إزالة التحقق من أن المستخدم هو مدير لتمكين حذف جميع المستخدمين

    # التحقق من أن المستخدم لا يحاول حذف نفسه
    if user.id == session.get('employee_id'):
        flash('لا يمكنك حذف حسابك الخاص', 'danger')
        return redirect(url_for('user_management'))

    # حذف المستخدم
    db.session.delete(user)
    db.session.commit()

    flash(f'تم حذف المستخدم {user.full_name} بنجاح', 'success')
    return redirect(url_for('user_management'))

# البحث
@app.route('/search_employees')
def search_employees():
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على معلمات البحث
    query = request.args.get('query', '')

    if not query:
        return redirect(url_for('employees'))

    # البحث في الموظفين
    employees = Employee.query.filter(
        (Employee.full_name.ilike(f'%{query}%')) |
        (Employee.job_title.ilike(f'%{query}%')) |
        (Employee.work_location.ilike(f'%{query}%')) |
        (Employee.department.ilike(f'%{query}%'))
    ).all()

    return render_template('search_results.html',
                          employees=employees,
                          query=query)

@app.route('/search_leaves', methods=['GET', 'POST'])
def search_leaves():
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin') and not current_user_has_permission('search_leaves'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على أنواع الإجازات للقائمة المنسدلة
    leave_types = LeaveType.query.all()

    # متغيرات افتراضية
    query = ''
    leave_type_id = ''
    leaves_info = []

    # التحقق مما إذا كان هذا طلب بحث (طريقة POST)
    if request.method == 'POST':
        # الحصول على معلمات البحث من النموذج
        query = request.form.get('query', '')
        leave_type_id = request.form.get('leave_type_id', '')

        # البحث في الإجازات
        leaves_query = LeaveRequest.query

        # تطبيق تصفية نوع الإجازة إذا تم تحديدها
        if leave_type_id:
            leaves_query = leaves_query.filter(LeaveRequest.leave_type_id == leave_type_id)

        # تطبيق تصفية اسم الموظف إذا تم تحديدها
        if query:
            # الحصول على معرفات الموظفين المطابقين للبحث
            employee_ids = db.session.query(Employee.id).filter(
                Employee.full_name.ilike(f'%{query}%')
            ).all()
            employee_ids = [emp_id[0] for emp_id in employee_ids]

            # البحث في الإجازات باستخدام معرفات الموظفين
            if employee_ids:
                leaves_query = leaves_query.filter(LeaveRequest.employee_id.in_(employee_ids))

        # الحصول على الإجازات
        leaves = leaves_query.order_by(LeaveRequest.start_date.desc()).all()

        # إضافة معلومات الموظف ونوع الإجازة
        for leave in leaves:
            employee = Employee.query.get(leave.employee_id)
            leave_type = LeaveType.query.get(leave.leave_type_id)

            if employee and leave_type:
                leaves_info.append({
                    'leave': leave,
                    'employee': employee,
                    'leave_type': leave_type
                })

    # عرض صفحة البحث مع النتائج (إن وجدت)
    return render_template('search_leaves_results.html',
                          leaves=leaves_info,
                          query=query,
                          leave_type_id=leave_type_id,
                          leave_types=leave_types)

# النسخ الاحتياطي
@app.route('/backup_database', methods=['GET', 'POST'])
def backup_database():
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin') and not current_user_has_permission('backup_database'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        try:
            # إنشاء اسم للنسخة الاحتياطية
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"backup_{timestamp}.zip"
            backup_path = os.path.join(app.config['BACKUP_FOLDER'], backup_filename)

            # إنشاء ملف ZIP
            with zipfile.ZipFile(backup_path, 'w') as backup_zip:
                # إضافة قاعدة البيانات
                db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
                if os.path.exists(db_path):
                    backup_zip.write(db_path, os.path.basename(db_path))

                # إضافة المستندات
                for root, _, files in os.walk(app.config['DOCUMENTS_FOLDER']):
                    for file in files:
                        file_path = os.path.join(root, file)
                        backup_zip.write(file_path, os.path.join('documents', file))

            # إرسال ملف النسخة الاحتياطية للتنزيل
            return send_file(backup_path, as_attachment=True, download_name=backup_filename)

        except Exception as e:
            flash(f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}', 'danger')
            return redirect(url_for('backup_restore'))

    return redirect(url_for('backup_restore'))

@app.route('/restore_database', methods=['POST'])
def restore_database():
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # التحقق من وجود ملف
    if 'backup_file' not in request.files:
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('backup_restore'))

    file = request.files['backup_file']

    # التحقق من أن الملف ليس فارغًا
    if file.filename == '':
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('backup_restore'))

    # التحقق من امتداد الملف
    if not file.filename.endswith('.zip'):
        flash('يجب أن يكون الملف بتنسيق ZIP', 'danger')
        return redirect(url_for('backup_restore'))

    try:
        # حفظ الملف مؤقتًا
        temp_dir = tempfile.mkdtemp()
        backup_path = os.path.join(temp_dir, 'backup.zip')
        file.save(backup_path)

        # استخراج الملفات
        with zipfile.ZipFile(backup_path, 'r') as backup_zip:
            # استخراج قاعدة البيانات
            db_files = [f for f in backup_zip.namelist() if f.endswith('.db')]
            if not db_files:
                flash('ملف النسخة الاحتياطية لا يحتوي على قاعدة بيانات', 'danger')
                return redirect(url_for('backup_restore'))

            # إغلاق اتصال قاعدة البيانات
            db.session.close()

            # استخراج قاعدة البيانات
            db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
            backup_zip.extract(db_files[0], temp_dir)
            shutil.copy2(os.path.join(temp_dir, db_files[0]), db_path)

            # استخراج المستندات
            document_files = [f for f in backup_zip.namelist() if f.startswith('documents/')]
            for doc_file in document_files:
                backup_zip.extract(doc_file, temp_dir)
                dest_path = os.path.join(app.config['DOCUMENTS_FOLDER'], os.path.basename(doc_file))
                shutil.copy2(os.path.join(temp_dir, doc_file), dest_path)

        # حذف الملفات المؤقتة
        shutil.rmtree(temp_dir)

        flash('تم استعادة النسخة الاحتياطية بنجاح', 'success')
        return redirect(url_for('backup_restore'))

    except Exception as e:
        flash(f'حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}', 'danger')
        return redirect(url_for('backup_restore'))

@app.route('/backup_restore')
def backup_restore():
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على قائمة النسخ الاحتياطية
    backups = []
    if os.path.exists(app.config['BACKUP_FOLDER']):
        for file in os.listdir(app.config['BACKUP_FOLDER']):
            if file.endswith('.zip') and file.startswith('backup_'):
                file_path = os.path.join(app.config['BACKUP_FOLDER'], file)
                file_size = os.path.getsize(file_path)
                file_date = datetime.fromtimestamp(os.path.getmtime(file_path))

                backups.append({
                    'filename': file,
                    'path': file_path,
                    'size': file_size,
                    'date': file_date
                })

    # ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
    backups.sort(key=lambda x: x['date'], reverse=True)

    return render_template('backup_restore.html', backups=backups)

@app.route('/backup')
def backup():
    """صفحة النسخ الاحتياطي الجديدة"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على قائمة النسخ الاحتياطية
    backups = []
    if os.path.exists(app.config['BACKUP_FOLDER']):
        for file in os.listdir(app.config['BACKUP_FOLDER']):
            if file.endswith('.zip') and file.startswith('backup_'):
                file_path = os.path.join(app.config['BACKUP_FOLDER'], file)
                file_size = os.path.getsize(file_path)
                file_date = datetime.fromtimestamp(os.path.getmtime(file_path))

                # تنسيق حجم الملف
                if file_size < 1024 * 1024:  # أقل من 1 ميجابايت
                    size_str = f"{file_size / 1024:.1f} كيلوبايت"
                else:
                    size_str = f"{file_size / (1024 * 1024):.1f} ميجابايت"

                backups.append({
                    'filename': file,
                    'path': file_path,
                    'size': size_str,
                    'date': file_date.strftime('%Y-%m-%d %H:%M:%S')
                })

    # ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
    backups.sort(key=lambda x: x['date'], reverse=True)

    return render_template('backup.html', backups=backups)

@app.route('/create_backup', methods=['POST'])
def create_backup():
    """إنشاء نسخة احتياطية جديدة"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # إنشاء اسم للنسخة الاحتياطية
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"backup_{timestamp}.zip"
        backup_path = os.path.join(app.config['BACKUP_FOLDER'], backup_filename)

        # إنشاء ملف ZIP
        with zipfile.ZipFile(backup_path, 'w') as backup_zip:
            # إضافة قاعدة البيانات
            db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')

            # التأكد من وجود مجلد instance
            instance_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance')
            if not os.path.exists(instance_dir):
                os.makedirs(instance_dir)
                print(f"تم إنشاء مجلد قاعدة البيانات: {instance_dir}")

            # محاولة البحث عن قاعدة البيانات في المجلد الرئيسي
            if os.path.exists(db_path):
                backup_zip.write(db_path, os.path.basename(db_path))
                print(f"تم إضافة قاعدة البيانات من المجلد الرئيسي إلى النسخة الاحتياطية: {db_path}")
                db_found = True
            else:
                print(f"تحذير: لم يتم العثور على ملف قاعدة البيانات في المجلد الرئيسي: {db_path}")
                db_found = False

            # محاولة البحث عن قاعدة البيانات في مجلد instance
            instance_db_path = os.path.join(instance_dir, 'ajazat.db')
            if os.path.exists(instance_db_path):
                backup_zip.write(instance_db_path, os.path.basename(instance_db_path))
                print(f"تم إضافة قاعدة البيانات من مجلد instance إلى النسخة الاحتياطية: {instance_db_path}")
                db_found = True
            else:
                print(f"تحذير: لم يتم العثور على ملف قاعدة البيانات في مجلد instance: {instance_db_path}")

            if not db_found:
                print(f"تحذير: لم يتم العثور على ملف قاعدة البيانات في أي مكان")

            # إضافة المستندات
            for root, _, files in os.walk(app.config['DOCUMENTS_FOLDER']):
                for file in files:
                    file_path = os.path.join(root, file)
                    backup_zip.write(file_path, os.path.join('documents', file))

        flash('تم إنشاء النسخة الاحتياطية بنجاح', 'success')
        return redirect(url_for('backup'))

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}', 'danger')
        return redirect(url_for('backup'))

@app.route('/restore_backup', methods=['POST'])
def restore_backup():
    """استعادة نسخة احتياطية"""
    # التحقق من وجود ملف
    if 'backup_file' not in request.files:
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('backup'))

    file = request.files['backup_file']

    # التحقق من أن الملف ليس فارغًا
    if file.filename == '':
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('backup'))

    # التحقق من امتداد الملف
    if not file.filename.endswith('.zip'):
        flash('يجب أن يكون الملف بتنسيق ZIP', 'danger')
        return redirect(url_for('backup'))

    # إنشاء مجلد مؤقت
    temp_dir = tempfile.mkdtemp()

    try:
        # حفظ الملف مؤقتًا
        backup_path = os.path.join(temp_dir, 'backup.zip')
        file.save(backup_path)
        print(f"تم حفظ ملف النسخة الاحتياطية مؤقتًا في: {backup_path}")

        # التحقق من صحة ملف ZIP
        if not zipfile.is_zipfile(backup_path):
            flash('الملف المحدد ليس ملف ZIP صالح', 'danger')
            shutil.rmtree(temp_dir)
            return redirect(url_for('backup'))

        # استخراج الملفات
        with zipfile.ZipFile(backup_path, 'r') as backup_zip:
            # استخراج قاعدة البيانات
            db_files = [f for f in backup_zip.namelist() if f.endswith('.db')]
            if not db_files:
                flash('ملف النسخة الاحتياطية لا يحتوي على قاعدة بيانات', 'danger')
                shutil.rmtree(temp_dir)
                return redirect(url_for('backup'))

            print(f"ملفات قاعدة البيانات الموجودة في النسخة الاحتياطية: {db_files}")

            # استخراج قاعدة البيانات
            extracted_db_path = os.path.join(temp_dir, db_files[0])
            backup_zip.extract(db_files[0], temp_dir)
            print(f"تم استخراج قاعدة البيانات إلى: {extracted_db_path}")

            # استخراج المستندات
            document_files = [f for f in backup_zip.namelist() if f.startswith('documents/')]
            print(f"عدد المستندات في النسخة الاحتياطية: {len(document_files)}")

            for doc_file in document_files:
                try:
                    backup_zip.extract(doc_file, temp_dir)
                    print(f"تم استخراج المستند: {doc_file}")
                except Exception as doc_error:
                    print(f"خطأ في استخراج المستند {doc_file}: {str(doc_error)}")

        # إغلاق اتصال قاعدة البيانات
        try:
            # إغلاق جميع الاتصالات بقاعدة البيانات
            db.session.remove()
            db.session.close()
            db.engine.dispose()
            print("تم إغلاق اتصال قاعدة البيانات بنجاح")
        except Exception as db_close_error:
            print(f"خطأ في إغلاق اتصال قاعدة البيانات: {str(db_close_error)}")

        # التأكد من وجود مجلد instance
        instance_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance')
        if not os.path.exists(instance_dir):
            os.makedirs(instance_dir)
            print(f"تم إنشاء مجلد قاعدة البيانات: {instance_dir}")

        # تحديد مسارات قاعدة البيانات
        db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
        instance_db_path = os.path.join(instance_dir, 'ajazat.db')

        # إعادة تشغيل التطبيق لإغلاق جميع الاتصالات بقاعدة البيانات
        import time

        # تسجيل الخروج من الجلسة الحالية
        session.clear()
        print("تم تسجيل الخروج من الجلسة الحالية")

        # محاولة حذف قاعدة البيانات الحالية إذا كانت موجودة
        success = False
        max_attempts = 5

        for attempt in range(max_attempts):
            try:
                # محاولة حذف قاعدة البيانات في المجلد الرئيسي
                if os.path.exists(db_path):
                    try:
                        os.remove(db_path)
                        print(f"تم حذف قاعدة البيانات في المجلد الرئيسي: {db_path}")
                    except PermissionError:
                        print(f"خطأ في الصلاحيات عند محاولة حذف قاعدة البيانات في المجلد الرئيسي: {db_path}")
                        # محاولة تغيير اسم الملف بدلاً من حذفه
                        backup_db_path = f"{db_path}.bak"
                        try:
                            os.rename(db_path, backup_db_path)
                            print(f"تم تغيير اسم قاعدة البيانات في المجلد الرئيسي إلى: {backup_db_path}")
                        except Exception as rename_error:
                            print(f"فشل في تغيير اسم قاعدة البيانات: {str(rename_error)}")

                # محاولة حذف قاعدة البيانات في مجلد instance
                if os.path.exists(instance_db_path):
                    try:
                        os.remove(instance_db_path)
                        print(f"تم حذف قاعدة البيانات في مجلد instance: {instance_db_path}")
                    except PermissionError:
                        print(f"خطأ في الصلاحيات عند محاولة حذف قاعدة البيانات في مجلد instance: {instance_db_path}")
                        # محاولة تغيير اسم الملف بدلاً من حذفه
                        backup_instance_db_path = f"{instance_db_path}.bak"
                        try:
                            os.rename(instance_db_path, backup_instance_db_path)
                            print(f"تم تغيير اسم قاعدة البيانات في مجلد instance إلى: {backup_instance_db_path}")
                        except Exception as rename_error:
                            print(f"فشل في تغيير اسم قاعدة البيانات: {str(rename_error)}")

                success = True
                break
            except Exception as remove_error:
                print(f"محاولة {attempt+1}/{max_attempts}: خطأ في حذف قاعدة البيانات: {str(remove_error)}")
                time.sleep(1)  # انتظار لحظة قبل المحاولة مرة أخرى

        # حتى لو لم ننجح في حذف قاعدة البيانات، سنحاول نسخ الملف الجديد
        try:
            # نسخ قاعدة البيانات المستخرجة إلى المجلد الرئيسي
            if not os.path.exists(db_path) or success:
                shutil.copy2(extracted_db_path, db_path)
                print(f"تم نسخ قاعدة البيانات من {extracted_db_path} إلى {db_path}")
            else:
                print(f"تعذر نسخ قاعدة البيانات إلى المجلد الرئيسي لأن الملف موجود ولا يمكن حذفه")

            # نسخ قاعدة البيانات المستخرجة إلى مجلد instance
            if not os.path.exists(instance_db_path) or success:
                shutil.copy2(extracted_db_path, instance_db_path)
                print(f"تم نسخ قاعدة البيانات من {extracted_db_path} إلى {instance_db_path}")
            else:
                print(f"تعذر نسخ قاعدة البيانات إلى مجلد instance لأن الملف موجود ولا يمكن حذفه")

            if not success:
                flash('لم يتمكن النظام من حذف قاعدة البيانات الحالية. يرجى إغلاق التطبيق وإعادة تشغيله ثم المحاولة مرة أخرى.', 'warning')
                shutil.rmtree(temp_dir)
                return redirect(url_for('backup'))
        except Exception as copy_error:
            print(f"خطأ في نسخ قاعدة البيانات: {str(copy_error)}")
            flash(f'خطأ في نسخ قاعدة البيانات: {str(copy_error)}', 'danger')
            shutil.rmtree(temp_dir)
            return redirect(url_for('backup'))

        # التحقق من وجود الملف بعد النسخ
        if os.path.exists(db_path):
            print(f"تم التأكد من وجود قاعدة البيانات بعد النسخ: {db_path}")
            print(f"حجم الملف: {os.path.getsize(db_path)} بايت")
        else:
            print(f"تحذير: لم يتم العثور على قاعدة البيانات بعد النسخ: {db_path}")

        if os.path.exists(instance_db_path):
            print(f"تم التأكد من وجود قاعدة البيانات في مجلد instance بعد النسخ: {instance_db_path}")
            print(f"حجم الملف: {os.path.getsize(instance_db_path)} بايت")
        else:
            print(f"تحذير: لم يتم العثور على قاعدة البيانات في مجلد instance بعد النسخ: {instance_db_path}")

        # التأكد من وجود مجلد المستندات
        if not os.path.exists(app.config['DOCUMENTS_FOLDER']):
            os.makedirs(app.config['DOCUMENTS_FOLDER'])
            print(f"تم إنشاء مجلد المستندات: {app.config['DOCUMENTS_FOLDER']}")

        # حذف جميع المستندات الحالية
        for root, dirs, files in os.walk(app.config['DOCUMENTS_FOLDER']):
            for file in files:
                try:
                    file_path = os.path.join(root, file)
                    os.remove(file_path)
                    print(f"تم حذف المستند: {file_path}")
                except Exception as remove_error:
                    print(f"خطأ في حذف المستند {file_path}: {str(remove_error)}")

        # نسخ المستندات المستخرجة
        extracted_docs_dir = os.path.join(temp_dir, 'documents')
        if os.path.exists(extracted_docs_dir):
            for root, dirs, files in os.walk(extracted_docs_dir):
                for file in files:
                    try:
                        src_path = os.path.join(root, file)
                        dest_path = os.path.join(app.config['DOCUMENTS_FOLDER'], file)
                        shutil.copy2(src_path, dest_path)
                        print(f"تم نسخ المستند من {src_path} إلى {dest_path}")
                    except Exception as doc_error:
                        print(f"خطأ في نسخ المستند {file}: {str(doc_error)}")

        # حذف الملفات المؤقتة
        try:
            shutil.rmtree(temp_dir)
            print("تم حذف الملفات المؤقتة")
        except Exception as temp_error:
            print(f"خطأ في حذف الملفات المؤقتة: {str(temp_error)}")

        # التحقق من وجود جدول document وإنشاؤه إذا لم يكن موجودًا
        try:
            with app.app_context():
                from sqlalchemy import inspect
                inspector = inspect(db.engine)
                if 'document' not in inspector.get_table_names():
                    print("جدول 'document' غير موجود. سيتم إنشاؤه الآن...")
                    from models import Document
                    Document.__table__.create(db.engine)
                    print("تم إنشاء جدول 'document' بنجاح.")
                else:
                    print("جدول 'document' موجود بالفعل.")
        except Exception as doc_table_error:
            print(f"خطأ في التحقق من جدول document: {str(doc_table_error)}")

        flash('تم استعادة النسخة الاحتياطية بنجاح. يرجى إعادة تشغيل التطبيق لتطبيق التغييرات بشكل كامل.', 'success')
        return redirect(url_for('login'))

    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}")

        # محاولة حذف الملفات المؤقتة في حالة حدوث خطأ
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                print("تم حذف الملفات المؤقتة بعد حدوث خطأ")
        except:
            pass

        flash(f'حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}', 'danger')
        return redirect(url_for('backup'))

@app.route('/download_backup/<filename>')
def download_backup(filename):
    """تنزيل نسخة احتياطية"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # التحقق من وجود الملف
    backup_path = os.path.join(app.config['BACKUP_FOLDER'], filename)
    if not os.path.exists(backup_path):
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('backup'))

    # تنزيل الملف
    return send_file(backup_path, as_attachment=True, download_name=filename)

@app.route('/delete_backup/<filename>')
def delete_backup(filename):
    """حذف نسخة احتياطية"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # التحقق من وجود الملف
    backup_path = os.path.join(app.config['BACKUP_FOLDER'], filename)
    if not os.path.exists(backup_path):
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('backup'))

    try:
        # حذف الملف
        os.remove(backup_path)
        flash('تم حذف النسخة الاحتياطية بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حذف النسخة الاحتياطية: {str(e)}', 'danger')

    return redirect(url_for('backup'))

@app.route('/reset_system', methods=['GET', 'POST'])
def reset_system():
    """تصفير النظام"""
    # السماح بالوصول حتى بدون تسجيل دخول
    # if not session.get('employee_id'):
    #     return redirect(url_for('login'))

    # # التحقق من صلاحيات المستخدم
    # if not session.get('is_admin'):
    #     flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
    #     return redirect(url_for('dashboard'))

    if request.method == 'POST':
        # التحقق من تأكيد المستخدم
        confirmation = request.form.get('confirmation')
        if confirmation != 'تصفير النظام':
            flash('تأكيد غير صحيح. يرجى كتابة "تصفير النظام" بالضبط للتأكيد.', 'danger')
            return redirect(url_for('reset_system'))

        try:
            # إنشاء نسخة احتياطية تلقائية قبل التصفير
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"backup_before_reset_{timestamp}.zip"
            backup_path = os.path.join(app.config['BACKUP_FOLDER'], backup_filename)
            print(f"سيتم إنشاء نسخة احتياطية في: {backup_path}")

            # التأكد من وجود مجلد النسخ الاحتياطية
            if not os.path.exists(app.config['BACKUP_FOLDER']):
                os.makedirs(app.config['BACKUP_FOLDER'])
                print(f"تم إنشاء مجلد النسخ الاحتياطية: {app.config['BACKUP_FOLDER']}")

            # إنشاء ملف ZIP
            with zipfile.ZipFile(backup_path, 'w') as backup_zip:
                # إضافة قاعدة البيانات
                db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')

                # التأكد من وجود مجلد instance
                instance_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance')
                if not os.path.exists(instance_dir):
                    os.makedirs(instance_dir)
                    print(f"تم إنشاء مجلد قاعدة البيانات: {instance_dir}")

                # محاولة البحث عن قاعدة البيانات في المجلد الرئيسي
                db_found = False
                if os.path.exists(db_path):
                    backup_zip.write(db_path, os.path.basename(db_path))
                    print(f"تم إضافة قاعدة البيانات من المجلد الرئيسي إلى النسخة الاحتياطية: {db_path}")
                    db_found = True
                else:
                    print(f"تحذير: لم يتم العثور على ملف قاعدة البيانات في المجلد الرئيسي: {db_path}")

                # محاولة البحث عن قاعدة البيانات في مجلد instance
                instance_db_path = os.path.join(instance_dir, 'ajazat.db')
                if os.path.exists(instance_db_path):
                    backup_zip.write(instance_db_path, os.path.basename(instance_db_path))
                    print(f"تم إضافة قاعدة البيانات من مجلد instance إلى النسخة الاحتياطية: {instance_db_path}")
                    db_found = True
                else:
                    print(f"تحذير: لم يتم العثور على ملف قاعدة البيانات في مجلد instance: {instance_db_path}")

                if not db_found:
                    print(f"تحذير: لم يتم العثور على ملف قاعدة البيانات في أي مكان")

                # إضافة المستندات
                if os.path.exists(app.config['DOCUMENTS_FOLDER']):
                    doc_count = 0
                    for root, _, files in os.walk(app.config['DOCUMENTS_FOLDER']):
                        for file in files:
                            file_path = os.path.join(root, file)
                            backup_zip.write(file_path, os.path.join('documents', file))
                            doc_count += 1
                    print(f"تم إضافة {doc_count} مستند إلى النسخة الاحتياطية")
                else:
                    print(f"تحذير: لم يتم العثور على مجلد المستندات: {app.config['DOCUMENTS_FOLDER']}")

            print(f"تم إنشاء النسخة الاحتياطية بنجاح: {backup_path}")

            # إغلاق اتصال قاعدة البيانات
            try:
                # إغلاق جميع الاتصالات بقاعدة البيانات
                db.session.remove()
                db.session.close()
                db.engine.dispose()
                print("تم إغلاق اتصال قاعدة البيانات بنجاح")
            except Exception as db_close_error:
                print(f"خطأ في إغلاق اتصال قاعدة البيانات: {str(db_close_error)}")

            # تسجيل الخروج من الجلسة الحالية
            session.clear()
            print("تم تسجيل الخروج من الجلسة الحالية")

            # تحديد مسارات قاعدة البيانات
            db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
            instance_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance')
            instance_db_path = os.path.join(instance_dir, 'ajazat.db')

            # محاولة حذف قاعدة البيانات الحالية إذا كانت موجودة
            success = False
            max_attempts = 5

            for attempt in range(max_attempts):
                try:
                    # محاولة حذف قاعدة البيانات في المجلد الرئيسي
                    if os.path.exists(db_path):
                        try:
                            os.remove(db_path)
                            print(f"تم حذف قاعدة البيانات في المجلد الرئيسي: {db_path}")
                        except PermissionError:
                            print(f"خطأ في الصلاحيات عند محاولة حذف قاعدة البيانات في المجلد الرئيسي: {db_path}")
                            # محاولة تغيير اسم الملف بدلاً من حذفه
                            backup_db_path = f"{db_path}.bak"
                            try:
                                os.rename(db_path, backup_db_path)
                                print(f"تم تغيير اسم قاعدة البيانات في المجلد الرئيسي إلى: {backup_db_path}")
                            except Exception as rename_error:
                                print(f"فشل في تغيير اسم قاعدة البيانات: {str(rename_error)}")

                    # محاولة حذف قاعدة البيانات في مجلد instance
                    if os.path.exists(instance_db_path):
                        try:
                            os.remove(instance_db_path)
                            print(f"تم حذف قاعدة البيانات في مجلد instance: {instance_db_path}")
                        except PermissionError:
                            print(f"خطأ في الصلاحيات عند محاولة حذف قاعدة البيانات في مجلد instance: {instance_db_path}")
                            # محاولة تغيير اسم الملف بدلاً من حذفه
                            backup_instance_db_path = f"{instance_db_path}.bak"
                            try:
                                os.rename(instance_db_path, backup_instance_db_path)
                                print(f"تم تغيير اسم قاعدة البيانات في مجلد instance إلى: {backup_instance_db_path}")
                            except Exception as rename_error:
                                print(f"فشل في تغيير اسم قاعدة البيانات: {str(rename_error)}")

                    success = True
                    break
                except Exception as remove_error:
                    print(f"محاولة {attempt+1}/{max_attempts}: خطأ في حذف قاعدة البيانات: {str(remove_error)}")
                    time.sleep(1)  # انتظار لحظة قبل المحاولة مرة أخرى

            if not success:
                flash('لم يتمكن النظام من حذف قاعدة البيانات الحالية. يرجى إغلاق التطبيق وإعادة تشغيله ثم المحاولة مرة أخرى.', 'warning')
                # سنستمر في محاولة إنشاء قاعدة بيانات جديدة حتى لو لم ننجح في حذف القديمة

            # إنشاء قاعدة بيانات جديدة
            try:
                # إنشاء جميع الجداول
                with app.app_context():
                    # حذف جميع البيانات من الجداول الموجودة
                    try:
                        # إغلاق جميع الاتصالات بقاعدة البيانات
                        db.session.remove()
                        db.session.close()
                        db.engine.dispose()
                        print("تم إغلاق جميع اتصالات قاعدة البيانات")

                        # إسقاط جميع الجداول وإعادة إنشائها
                        db.drop_all()
                        print("تم إسقاط جميع الجداول")
                        db.create_all()
                        print("تم إعادة إنشاء جداول قاعدة البيانات")
                    except Exception as reset_error:
                        print(f"خطأ في إعادة تهيئة قاعدة البيانات: {str(reset_error)}")
                        # محاولة حذف البيانات من كل جدول بشكل منفصل
                        try:
                            # حذف البيانات من جميع الجداول
                            db.session.execute(text("DELETE FROM leave_request"))
                            db.session.execute(text("DELETE FROM document"))
                            db.session.execute(text("DELETE FROM employee"))
                            db.session.execute(text("DELETE FROM leave_type"))
                            db.session.commit()
                            print("تم حذف البيانات من جميع الجداول")

                            # إعادة إنشاء الجداول
                            db.create_all()
                            print("تم إعادة إنشاء جداول قاعدة البيانات")
                        except Exception as delete_error:
                            print(f"خطأ في حذف البيانات من الجداول: {str(delete_error)}")
                            flash(f'حدث خطأ أثناء حذف البيانات من الجداول: {str(delete_error)}', 'danger')
                            return redirect(url_for('reset_system'))

                    # إنشاء مستخدم مدير افتراضي جديد
                    try:
                        # حذف أي مستخدمين موجودين بنفس اسم المستخدم
                        db.session.execute(text("DELETE FROM employee WHERE username = 'admin'"))
                        db.session.execute(text("DELETE FROM employee WHERE username = 'admin2'"))
                        db.session.commit()
                        print("تم حذف المستخدمين الموجودين بنفس اسم المستخدم")

                        # إنشاء مستخدم مدير افتراضي جديد
                        admin = Employee(
                            username='admin2',
                            full_name='مدير النظام',
                            job_title='مدير النظام',
                            work_location='المقر الرئيسي',
                            is_admin=True,
                            leave_balance=36,
                            is_active=True,
                            children_count=0
                        )
                        admin.set_password('admin')
                        db.session.add(admin)
                        db.session.commit()
                        print("تم إنشاء مستخدم مدير افتراضي جديد")
                    except Exception as admin_error:
                        print(f"خطأ في إنشاء المستخدم المدير: {str(admin_error)}")
                        # محاولة إنشاء مستخدم مدير بديل
                        try:
                            db.session.rollback()
                            admin2 = Employee(
                                username='admin3',
                                full_name='مدير النظام 3',
                                job_title='مدير النظام',
                                work_location='المقر الرئيسي',
                                is_admin=True,
                                leave_balance=36,
                                is_active=True,
                                children_count=0
                            )
                            admin2.set_password('admin')
                            db.session.add(admin2)
                            db.session.commit()
                            print("تم إنشاء مستخدم مدير بديل")
                        except Exception as admin2_error:
                            db.session.rollback()
                            print(f"خطأ في إنشاء المستخدم المدير البديل: {str(admin2_error)}")

                    # حذف جميع أنواع الإجازات الموجودة وإضافة أنواع جديدة
                    try:
                        # حذف جميع أنواع الإجازات الموجودة
                        db.session.execute(text("DELETE FROM leave_type"))
                        db.session.commit()
                        print("تم حذف جميع أنواع الإجازات الموجودة")

                        # إضافة أنواع الإجازات الافتراضية
                        leave_types = [
                            {'name': 'اعتيادية', 'days_allowed': 30, 'description': 'إجازة اعتيادية'},
                            {'name': 'مرضية', 'days_allowed': 30, 'description': 'إجازة مرضية'},
                            {'name': 'أمومة', 'days_allowed': 365, 'description': 'إجازة أمومة'},
                            {'name': 'ما قبل الوضع', 'days_allowed': 21, 'description': 'إجازة ما قبل الوضع'},
                            {'name': 'ما بعد الوضع', 'days_allowed': 51, 'description': 'إجازة ما بعد الوضع'},
                            {'name': 'دراسية', 'days_allowed': None, 'description': 'إجازة دراسية'},
                            {'name': 'طويلة', 'days_allowed': None, 'description': 'إجازة طويلة'}
                        ]

                        for lt_data in leave_types:
                            leave_type = LeaveType(**lt_data)
                            db.session.add(leave_type)

                        db.session.commit()
                        print(f"تم إضافة {len(leave_types)} من أنواع الإجازات الافتراضية")
                    except Exception as leave_type_error:
                        db.session.rollback()
                        print(f"خطأ في إضافة أنواع الإجازات: {str(leave_type_error)}")
                        # محاولة إضافة أنواع الإجازات بطريقة أخرى
                        try:
                            # إضافة أنواع الإجازات واحدة تلو الأخرى
                            leave_types = [
                                {'name': 'اعتيادية', 'days_allowed': 30, 'description': 'إجازة اعتيادية'},
                                {'name': 'مرضية', 'days_allowed': 30, 'description': 'إجازة مرضية'},
                                {'name': 'أمومة', 'days_allowed': 365, 'description': 'إجازة أمومة'},
                                {'name': 'دراسية', 'days_allowed': None, 'description': 'إجازة دراسية'},
                                {'name': 'طويلة', 'days_allowed': None, 'description': 'إجازة طويلة'}
                            ]

                            for lt_data in leave_types:
                                try:
                                    leave_type = LeaveType(**lt_data)
                                    db.session.add(leave_type)
                                    db.session.commit()
                                    print(f"تم إضافة نوع الإجازة: {lt_data['name']}")
                                except Exception as lt_error:
                                    db.session.rollback()
                                    print(f"خطأ في إضافة نوع الإجازة {lt_data['name']}: {str(lt_error)}")
                        except Exception as alt_leave_type_error:
                            print(f"خطأ في إضافة أنواع الإجازات بالطريقة البديلة: {str(alt_leave_type_error)}")

                    # حفظ التغييرات
                    db.session.commit()
                    print("تم حفظ التغييرات في قاعدة البيانات")

                # التحقق من وجود ملف قاعدة البيانات بعد الإنشاء
                if os.path.exists(db_path):
                    print(f"تم التأكد من وجود قاعدة البيانات بعد الإنشاء: {db_path}")
                    print(f"حجم الملف: {os.path.getsize(db_path)} بايت")
                else:
                    print(f"تحذير: لم يتم العثور على قاعدة البيانات بعد الإنشاء: {db_path}")

                # نسخ قاعدة البيانات إلى مجلد instance إذا لم تكن موجودة هناك
                if os.path.exists(db_path) and not os.path.exists(instance_db_path):
                    shutil.copy2(db_path, instance_db_path)
                    print(f"تم نسخ قاعدة البيانات من {db_path} إلى {instance_db_path}")
                elif os.path.exists(instance_db_path) and not os.path.exists(db_path):
                    shutil.copy2(instance_db_path, db_path)
                    print(f"تم نسخ قاعدة البيانات من {instance_db_path} إلى {db_path}")
            except Exception as db_create_error:
                import traceback
                traceback.print_exc()
                print(f"خطأ في إنشاء قاعدة البيانات الجديدة: {str(db_create_error)}")
                flash(f'حدث خطأ أثناء إنشاء قاعدة البيانات الجديدة: {str(db_create_error)}', 'danger')
                return redirect(url_for('reset_system'))

            # حذف جميع المستندات
            try:
                if os.path.exists(app.config['DOCUMENTS_FOLDER']):
                    deleted_count = 0
                    error_count = 0
                    for root, dirs, files in os.walk(app.config['DOCUMENTS_FOLDER']):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                os.remove(file_path)
                                deleted_count += 1
                            except Exception as file_error:
                                error_count += 1
                                print(f"خطأ في حذف المستند {file_path}: {str(file_error)}")
                    print(f"تم حذف {deleted_count} مستند، مع {error_count} خطأ")
                else:
                    print(f"تحذير: لم يتم العثور على مجلد المستندات: {app.config['DOCUMENTS_FOLDER']}")
                    # إنشاء مجلد المستندات إذا لم يكن موجودًا
                    os.makedirs(app.config['DOCUMENTS_FOLDER'])
                    print(f"تم إنشاء مجلد المستندات: {app.config['DOCUMENTS_FOLDER']}")
            except Exception as docs_error:
                print(f"خطأ في حذف المستندات: {str(docs_error)}")

            flash('تم تصفير النظام بنجاح. تم إنشاء مستخدم مدير افتراضي (اسم المستخدم: admin2، كلمة المرور: admin).', 'success')
            return redirect(url_for('login'))

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"خطأ في تصفير النظام: {str(e)}")
            flash(f'حدث خطأ أثناء تصفير النظام: {str(e)}', 'danger')
            return redirect(url_for('reset_system'))

    return render_template('reset_system.html')

# مسارات عرض المستندات
@app.route('/document_viewer')
def document_viewer():
    """عرض المستند في صفحة مخصصة - PDF فقط"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    file_path = request.args.get('file_path')

    if not file_path or not os.path.exists(file_path):
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('dashboard'))

    # تحديد نوع الملف
    file_ext = file_path.rsplit('.', 1)[1].lower() if '.' in file_path else ''

    # التعامل مع ملفات PDF
    if file_ext == 'pdf':
        return render_template('pdf_viewer.html', file_path=file_path)

    # التعامل مع أنواع الملفات الأخرى
    else:
        return send_file(file_path, as_attachment=True)

@app.route('/raw_document')
def raw_document():
    """عرض المستند الخام - PDF فقط"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    try:
        file_path = request.args.get('file_path')

        if not file_path or not os.path.exists(file_path):
            return redirect(url_for('document_viewer', file_path=file_path))

        # تحديد نوع المحتوى
        file_ext = file_path.rsplit('.', 1)[1].lower() if '.' in file_path else ''
        mimetype = None

        if file_ext == 'pdf':
            mimetype = 'application/pdf'
        else:
            # إذا لم يكن الملف PDF، إرساله كمرفق للتنزيل
            return send_file(file_path, as_attachment=True)

        # إرسال ملف PDF مباشرة
        return send_file(file_path, as_attachment=False, mimetype=mimetype)
    except Exception as e:
        print(f"خطأ في عرض المستند: {str(e)}")
        return redirect(url_for('document_viewer', file_path=file_path))

@app.route('/raw_document_by_id/<int:leave_id>')
def raw_document_by_id(leave_id):
    """عرض المستند الخام باستخدام معرف الإجازة - PDF فقط"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    try:
        leave = LeaveRequest.query.get_or_404(leave_id)

        if not leave.document_path or not os.path.exists(leave.document_path):
            return redirect(url_for('document_viewer', file_path=leave.document_path))

        # تحديد نوع المحتوى
        file_ext = leave.document_path.rsplit('.', 1)[1].lower() if '.' in leave.document_path else ''
        mimetype = None

        if file_ext == 'pdf':
            mimetype = 'application/pdf'
            # إرسال ملف PDF مباشرة
            return send_file(leave.document_path, as_attachment=False, mimetype=mimetype)
        else:
            # إذا لم يكن الملف PDF، إرساله كمرفق للتنزيل
            return send_file(leave.document_path, as_attachment=True)

    except Exception as e:
        print(f"خطأ في عرض المستند للإجازة {leave_id}: {str(e)}")
        return redirect(url_for('document_viewer', file_path=leave.document_path if leave and leave.document_path else ''))

@app.route('/raw_document_by_document_id/<int:document_id>')
def raw_document_by_document_id(document_id):
    """عرض المستند الخام باستخدام معرف المستند - PDF فقط"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    try:
        document = Document.query.get_or_404(document_id)

        if not document.file_path or not os.path.exists(document.file_path):
            flash('الملف غير موجود', 'danger')
            return redirect(url_for('dashboard'))

        # تحديد نوع المحتوى
        file_ext = document.file_path.rsplit('.', 1)[1].lower() if '.' in document.file_path else ''
        mimetype = None

        if file_ext == 'pdf':
            mimetype = 'application/pdf'
            # إرسال ملف PDF مباشرة
            return send_file(document.file_path, as_attachment=False, mimetype=mimetype)
        else:
            # إذا لم يكن الملف PDF، إرساله كمرفق للتنزيل
            return send_file(document.file_path, as_attachment=True, download_name=document.file_name)

    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"خطأ في عرض المستند {document_id}: {str(e)}")
        flash(f'حدث خطأ أثناء عرض المستند: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

@app.route('/download_document/<int:document_id>')
def download_document(document_id):
    """تنزيل المستند"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على المستند
    document = Document.query.get_or_404(document_id)

    # التحقق من وجود الملف
    if not document.file_path or not os.path.exists(document.file_path):
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('dashboard'))

    # تنزيل الملف
    return send_file(document.file_path, as_attachment=True, download_name=document.file_name)

@app.route('/get_scanned_pages')
def get_scanned_pages():
    """
    الحصول على الصفحات الممسوحة ضوئيًا
    """
    if not session.get('employee_id'):
        return jsonify({'error': 'غير مصرح'}), 401

    # التحقق من صلاحيات المستخدم (فقط المدير يمكنه استخدام الماسح الضوئي)
    if not session.get('is_admin'):
        return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه الوظيفة'}), 403

    try:
        # الحصول على الصفحات الممسوحة ضوئيًا
        pages = pdf_only_scanner.get_scanned_pages()

        # تحويل الصفحات إلى قائمة من البيانات
        pages_data = []
        for i, page in enumerate(pages):
            pages_data.append({
                'index': i,
                'image_data': page,
                'thumbnail': page  # يمكن إنشاء صورة مصغرة هنا إذا لزم الأمر
            })

        return jsonify({
            'success': True,
            'pages': pages_data,
            'page_count': len(pages)
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"خطأ في الحصول على الصفحات الممسوحة ضوئيًا: {str(e)}")
        return jsonify({'error': f'حدث خطأ أثناء الحصول على الصفحات الممسوحة ضوئيًا: {str(e)}'}), 500

@app.route('/create_pdf_from_scanned_pages', methods=['POST'])
def create_pdf_from_scanned_pages():
    """
    إنشاء ملف PDF من الصفحات الممسوحة ضوئيًا
    """
    if not session.get('employee_id'):
        return jsonify({'error': 'غير مصرح'}), 401

    # التحقق من صلاحيات المستخدم (فقط المدير يمكنه استخدام الماسح الضوئي)
    if not session.get('is_admin'):
        return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه الوظيفة'}), 403

    try:
        # إنشاء ملف PDF
        pdf_data = pdf_only_scanner.create_pdf_from_scanned_pages()

        if not pdf_data:
            return jsonify({'error': 'فشل في إنشاء ملف PDF'}), 500

        # إنشاء اسم ملف فريد
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"scan_{timestamp}.pdf"

        # التأكد من وجود مجلد الصور
        if not os.path.exists('static/scanned_images'):
            os.makedirs('static/scanned_images')

        # حفظ الملف
        file_path = os.path.join('static/scanned_images', filename)
        with open(file_path, "wb") as f:
            f.write(pdf_data)

        # إنشاء URL للملف
        pdf_url = url_for('static', filename=f'scanned_images/{filename}')

        return jsonify({
            'success': True,
            'pdf_url': pdf_url,
            'filename': filename,
            'file_path': file_path
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"خطأ في إنشاء ملف PDF: {str(e)}")
        return jsonify({'error': f'حدث خطأ أثناء إنشاء ملف PDF: {str(e)}'}), 500

@app.route('/clear_scanned_pages', methods=['POST'])
def clear_scanned_pages():
    """
    مسح الصفحات الممسوحة ضوئيًا
    """
    try:
        # مسح الصفحات الممسوحة ضوئيًا
        pdf_only_scanner.clear_scanned_pages()
        print("تم مسح الصفحات الممسوحة ضوئيًا بنجاح")

        return jsonify({
            'success': True,
            'message': 'تم مسح الصفحات الممسوحة ضوئيًا بنجاح'
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"خطأ في مسح الصفحات الممسوحة ضوئيًا: {str(e)}")
        return jsonify({'error': f'حدث خطأ أثناء مسح الصفحات الممسوحة ضوئيًا: {str(e)}'}), 500

# مسارات إضافية
@app.route('/update_return_status/<int:leave_id>', methods=['POST'])
def update_return_status(leave_id):
    """تحديث حالة العودة من الإجازة"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin') and not current_user_has_permission('update_return_status'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على الإجازة
    leave = LeaveRequest.query.get_or_404(leave_id)

    # الحصول على الموظف
    employee = Employee.query.get(leave.employee_id)

    if request.method == 'POST':
        # الحصول على بيانات النموذج
        return_date = request.form.get('return_date')
        additional_comment = request.form.get('additional_comment', '')
        return_status = request.form.get('return_status')

        # تحويل التاريخ إلى كائن datetime
        return_date = datetime.strptime(return_date, '%Y-%m-%d').date() if return_date else None

        # تحديث بيانات الإجازة
        leave.return_date = return_date

        # تحديث التعليق
        comment = leave.comment or ''
        if return_status == 'returned':
            comment = f"{comment}\nتمت المباشرة بتاريخ {return_date.strftime('%Y-%m-%d')}"
        else:
            comment = f"{comment}\nلم يباشر"

        if additional_comment:
            comment = f"{comment}\n{additional_comment}"

        leave.comment = comment.strip()

        db.session.commit()

        flash(f'تم تحديث حالة العودة للموظف {employee.full_name} بنجاح', 'success')
        return redirect(url_for('employee_leaves', employee_id=employee.id))

    return redirect(url_for('employee_leaves', employee_id=employee.id))

@app.route('/view_document/<int:document_id>')
def view_document(document_id):
    """عرض المستند - PDF فقط"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على المستند
    document = Document.query.get_or_404(document_id)

    # التحقق من وجود الملف
    if not document.file_path or not os.path.exists(document.file_path):
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('dashboard'))

    # تحديد نوع الملف
    file_ext = document.file_path.rsplit('.', 1)[1].lower() if '.' in document.file_path else ''

    # التعامل مع ملفات PDF
    if file_ext == 'pdf':
        return render_template('pdf_viewer.html',
                              document_name=document.file_name,
                              document_url=url_for('raw_document_by_document_id', document_id=document.id),
                              back_url=url_for('view_documents', leave_id=document.leave_id))

    # التعامل مع أنواع الملفات الأخرى
    else:
        return send_file(document.file_path, as_attachment=True, download_name=document.file_name)

@app.route('/scan_document_page/<int:leave_id>', methods=['GET', 'POST'])
def scan_document_page(leave_id):
    """عرض صفحة مسح المستندات ضوئيًا"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على الإجازة
    leave = LeaveRequest.query.get_or_404(leave_id)

    # الحصول على الموظف
    employee = Employee.query.get(leave.employee_id)

    # الحصول على المستندات الحالية
    documents = Document.query.filter_by(leave_id=leave_id).order_by(Document.upload_date.desc()).all()

    # تحديد ما إذا كانت مكتبات المسح الضوئي متوفرة
    wia_available = True
    direct_wia_available = True
    wia_scan_available = True
    wia_error_message = ""

    try:
        # يمكن إضافة منطق للتحقق من توفر مكتبات المسح الضوئي هنا
        pass
    except Exception as e:
        wia_error_message = str(e)
        wia_available = False
        direct_wia_available = False
        wia_scan_available = False

    return render_template('scan_document_pdf_only.html',
                          leave=leave,
                          employee=employee,
                          documents=documents,
                          leave_id=leave_id)

@app.route('/get_available_scanners_api')
def get_available_scanners_api():
    """الحصول على قائمة بأجهزة المسح الضوئي المتوفرة"""
    print("=== بدء تنفيذ دالة get_available_scanners_api ===")

    # تجاوز التحقق من صلاحيات المستخدم مؤقتًا لأغراض التصحيح
    # if not session.get('employee_id'):
    #     print("خطأ: المستخدم غير مصرح")
    #     return jsonify({'error': 'غير مصرح'}), 401
    #
    # # التحقق من صلاحيات المستخدم (فقط المدير يمكنه استخدام الماسح الضوئي)
    # if not session.get('is_admin'):
    #     print("خطأ: المستخدم ليس مديرًا")
    #     return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه الوظيفة'}), 403

    print("تم تجاوز التحقق من صلاحيات المستخدم مؤقتًا لأغراض التصحيح")

    try:
        print("محاولة الحصول على قائمة أجهزة المسح الضوئي المتوفرة...")

        # تهيئة قائمة الماسحات الضوئية
        scanners_list = []

        # إضافة ماسح ضوئي CANON DR-M260 يدويًا (لأنه معروف أنه موجود)
        canon_scanner = {
            'id': "0",
            'name': "CANON DR-M260 (مضاف يدويًا)",
            'device_id': "{6BDD1FC6-810F-11D0-BEC7-08002BE2092F}\\0005",
            'source': 'direct_wia'
        }
        scanners_list.append(canon_scanner)
        print("تم إضافة ماسح ضوئي CANON DR-M260 يدويًا")

        # إضافة ماسح ضوئي PDF فقط (دائمًا) - هذا هو الماسح الذي يعمل بشكل جيد في صفحة الاختبار
        pdf_scanner = {
            'id': 'pdf_scanner',
            'name': 'ماسح ضوئي PDF (مستحسن)',
            'device_id': 'pdf_scanner',
            'source': 'pdf_only'
        }
        scanners_list.append(pdf_scanner)
        print("تم إضافة ماسح ضوئي PDF")

        # محاولة الحصول على قائمة الماسحات الضوئية الحقيقية من pdf_only_scanner
        try:
            real_scanners = pdf_only_scanner.get_available_scanners()
            print(f"تم العثور على {len(real_scanners)} ماسح ضوئي حقيقي")

            # إضافة الماسحات الضوئية الحقيقية إلى القائمة (باستثناء التي تمت إضافتها بالفعل)
            for scanner in real_scanners:
                # تجاهل الماسحات الضوئية التي تمت إضافتها بالفعل
                if scanner.get('name', '').upper() == "CANON DR-M260" or scanner.get('id') == 'pdf_scanner':
                    continue

                scanner_info = {
                    'id': scanner.get('id', '0'),
                    'name': scanner.get('name', 'ماسح ضوئي غير معروف'),
                    'device_id': scanner.get('device_id', scanner.get('id', '0')),
                    'source': scanner.get('source', 'wia'),
                }
                scanners_list.append(scanner_info)
                print(f"تم إضافة ماسح ضوئي حقيقي: {scanner_info['name']}")
        except Exception as scanner_error:
            print(f"خطأ في الحصول على قائمة الماسحات الضوئية الحقيقية: {str(scanner_error)}")
            traceback.print_exc()

        # إرجاع قائمة الماسحات الضوئية
        response = {
            'success': True,
            'scanners': scanners_list,
            'method': 'enhanced_detection',
            'errors': None,
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        print(f"تم العثور على {len(scanners_list)} جهاز مسح ضوئي بالإجمال")
        print("=== انتهاء تنفيذ دالة get_available_scanners_api ===")

        return jsonify(response)
    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"خطأ في الحصول على قائمة أجهزة المسح الضوئي: {str(e)}")

        # في حالة حدوث خطأ، إرجاع الماسحات الضوئية الافتراضية
        canon_scanner = {
            'id': "0",
            'name': "CANON DR-M260 (مضاف يدويًا)",
            'device_id': "{6BDD1FC6-810F-11D0-BEC7-08002BE2092F}\\0005",
            'source': 'direct_wia'
        }

        pdf_scanner = {
            'id': 'pdf_scanner',
            'name': 'ماسح ضوئي PDF (مستحسن)',
            'device_id': 'pdf_scanner',
            'source': 'pdf_only'
        }

        response = {
            'success': True,
            'scanners': [canon_scanner, pdf_scanner],
            'method': 'fallback',
            'errors': str(e),
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        print("تم إرجاع الماسحات الضوئية الافتراضية بسبب حدوث خطأ")
        return jsonify(response)

@app.route('/scan_with_device_api', methods=['POST'])
def scan_with_device_api():
    """مسح مستند باستخدام جهاز مسح ضوئي محدد (واجهة برمجة التطبيقات)"""
    print("=== بدء تنفيذ دالة scan_with_device_api ===")

    # تجاوز التحقق من صلاحيات المستخدم مؤقتًا لأغراض التصحيح
    # if not session.get('employee_id'):
    #     print("خطأ: المستخدم غير مصرح")
    #     return jsonify({'error': 'غير مصرح'}), 401
    #
    # # التحقق من صلاحيات المستخدم (فقط المدير يمكنه استخدام الماسح الضوئي)
    # if not session.get('is_admin'):
    #     print("خطأ: المستخدم ليس مديرًا")
    #     return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه الوظيفة'}), 403

    print("تم تجاوز التحقق من صلاحيات المستخدم مؤقتًا لأغراض التصحيح")

    try:
        # الحصول على بيانات الطلب
        data = request.json
        print(f"بيانات الطلب المستلمة: {data}")

        if not data:
            print("خطأ: لم يتم استلام أي بيانات")
            # استخدام القيمة الافتراضية
            data = {
                'device_id': 'pdf_scanner',  # استخدام ماسح PDF كقيمة افتراضية
                'scanner_source': 'pdf_only',
                'dpi': 300,
                'use_adf': True
            }
            print(f"استخدام البيانات الافتراضية: {data}")

        device_id = data.get('device_id', 'pdf_scanner')  # استخدام 'pdf_scanner' كقيمة افتراضية
        scanner_source = data.get('scanner_source', 'pdf_only')
        dpi = data.get('dpi', 300)
        use_adf = data.get('use_adf', True)
        clear_previous = data.get('clear_previous', True)  # مسح الصفحات السابقة بشكل افتراضي

        print(f"بيانات الطلب المعالجة: device_id={device_id}, scanner_source={scanner_source}, dpi={dpi}, use_adf={use_adf}")

        # استخدام ماسح PDF فقط إذا كان المصدر هو pdf_only أو إذا كان معرف الجهاز هو pdf_scanner
        if scanner_source == 'pdf_only' or device_id == 'pdf_scanner':
            print("استخدام ماسح PDF فقط")

            # مسح المستند باستخدام pdf_only_scanner
            print(f"جاري مسح المستند باستخدام pdf_only_scanner: device_id={device_id}, dpi={dpi}, use_adf={use_adf}")

            try:
                # مسح الصفحات السابقة إذا كان مطلوباً
                if clear_previous:
                    pdf_only_scanner.clear_scanned_pages()
                    print("تم مسح الصفحات السابقة بناءً على طلب المستخدم")

                # استخدام الدالة scan_with_device من pdf_only_scanner
                result = pdf_only_scanner.scan_with_device(device_id=device_id if device_id != 'pdf_scanner' else None)

                if result.get('success', False):
                    # الحصول على عدد الصفحات الممسوحة
                    page_count = result.get('page_count', 0)
                    print(f"تم مسح {page_count} صفحة بنجاح باستخدام pdf_only_scanner.scan_with_device")

                    # إرجاع معلومات عن الصفحات الممسوحة
                    return jsonify({
                        'success': True,
                        'page_count': page_count,
                        'message': result.get('message', f'تم مسح {page_count} صفحة بنجاح'),
                        'method': 'pdf_only'
                    })
                else:
                    print(f"فشل في مسح المستند باستخدام pdf_only_scanner.scan_with_device: {result.get('message', 'خطأ غير معروف')}")

                    # محاولة استخدام الدالة scan_document مباشرة كبديل
                    print("محاولة استخدام pdf_only_scanner.scan_document كبديل")

                    # إذا كان مطلوباً مسح الصفحات السابقة
                    if clear_previous:
                        pdf_only_scanner.clear_scanned_pages()
                        print("تم مسح الصفحات السابقة قبل المحاولة البديلة")

                    success = pdf_only_scanner.scan_document(
                        scanner_id=device_id if device_id != 'pdf_scanner' else None,
                        dpi=dpi,
                        use_adf=use_adf,
                        clear_previous=False  # لا نحتاج لمسح الصفحات مرة أخرى
                    )

                    if not success:
                        print("فشل في مسح المستند باستخدام pdf_only_scanner.scan_document")
                        return jsonify({'error': 'فشل في مسح المستند باستخدام جميع الطرق المتاحة'}), 500

                    # الحصول على عدد الصفحات الممسوحة
                    page_count = pdf_only_scanner.get_scanned_pages_count()
                    print(f"تم مسح {page_count} صفحة بنجاح باستخدام pdf_only_scanner.scan_document")

                    # إرجاع معلومات عن الصفحات الممسوحة
                    return jsonify({
                        'success': True,
                        'page_count': page_count,
                        'message': f'تم مسح {page_count} صفحة بنجاح باستخدام الطريقة البديلة',
                        'method': 'pdf_only_fallback'
                    })
            except Exception as scan_error:
                print(f"خطأ في مسح المستند باستخدام pdf_only_scanner: {str(scan_error)}")
                traceback.print_exc()

                # محاولة استخدام الدالة scan_document مباشرة كبديل
                print("محاولة استخدام pdf_only_scanner.scan_document كبديل بعد حدوث خطأ")
                try:
                    success = pdf_only_scanner.scan_document(
                        scanner_id=device_id if device_id != 'pdf_scanner' else None,
                        dpi=dpi,
                        use_adf=use_adf
                    )

                    if not success:
                        print("فشل في مسح المستند باستخدام pdf_only_scanner.scan_document")
                        return jsonify({'error': 'فشل في مسح المستند باستخدام جميع الطرق المتاحة'}), 500

                    # الحصول على عدد الصفحات الممسوحة
                    page_count = pdf_only_scanner.get_scanned_pages_count()
                    print(f"تم مسح {page_count} صفحة بنجاح باستخدام pdf_only_scanner.scan_document")

                    # إرجاع معلومات عن الصفحات الممسوحة
                    return jsonify({
                        'success': True,
                        'page_count': page_count,
                        'message': f'تم مسح {page_count} صفحة بنجاح باستخدام الطريقة البديلة',
                        'method': 'pdf_only_fallback'
                    })
                except Exception as fallback_error:
                    print(f"خطأ في مسح المستند باستخدام الطريقة البديلة: {str(fallback_error)}")
                    traceback.print_exc()
                    return jsonify({'error': f'فشل في مسح المستند: {str(scan_error)}\nفشل في الطريقة البديلة: {str(fallback_error)}'}), 500

        # التعامل مع ماسح CANON DR-M260 أو أي ماسح آخر
        elif device_id == '0' or device_id == 0:
            print("تم تحديد معرف الجهاز '0'، استخدام ماسح CANON DR-M260")

            try:
                # استخدام الدالة scan_with_device من pdf_only_scanner
                result = pdf_only_scanner.scan_with_device(device_id='0')

                if result.get('success', False):
                    # الحصول على عدد الصفحات الممسوحة
                    page_count = result.get('page_count', 0)
                    print(f"تم مسح {page_count} صفحة بنجاح باستخدام pdf_only_scanner.scan_with_device مع ماسح CANON DR-M260")

                    # إرجاع معلومات عن الصفحات الممسوحة
                    return jsonify({
                        'success': True,
                        'page_count': page_count,
                        'message': result.get('message', f'تم مسح {page_count} صفحة بنجاح'),
                        'method': 'canon_scanner'
                    })
                else:
                    print(f"فشل في مسح المستند باستخدام pdf_only_scanner.scan_with_device مع ماسح CANON DR-M260: {result.get('message', 'خطأ غير معروف')}")

                    # محاولة استخدام الدالة scan_document مباشرة كبديل
                    print("محاولة استخدام pdf_only_scanner.scan_document كبديل مع ماسح CANON DR-M260")
                    success = pdf_only_scanner.scan_document(
                        scanner_id='0',
                        dpi=dpi,
                        use_adf=use_adf
                    )

                    if not success:
                        print("فشل في مسح المستند باستخدام pdf_only_scanner.scan_document مع ماسح CANON DR-M260")

                        # محاولة استخدام ماسح PDF فقط كبديل
                        print("محاولة استخدام ماسح PDF فقط كبديل")
                        fallback_success = pdf_only_scanner.scan_document(
                            scanner_id=None,  # استخدام القيمة الافتراضية
                            dpi=dpi,
                            use_adf=use_adf
                        )

                        if not fallback_success:
                            print("فشل في مسح المستند باستخدام ماسح PDF البديل")
                            return jsonify({'error': 'فشل في مسح المستند باستخدام جميع الماسحات المتاحة'}), 500

                        # الحصول على عدد الصفحات الممسوحة
                        page_count = pdf_only_scanner.get_scanned_pages_count()
                        print(f"تم مسح {page_count} صفحة بنجاح باستخدام ماسح PDF البديل")

                        return jsonify({
                            'success': True,
                            'page_count': page_count,
                            'message': f'تم مسح {page_count} صفحة بنجاح باستخدام ماسح PDF البديل',
                            'method': 'pdf_only',
                            'used_fallback': True
                        })

                    # الحصول على عدد الصفحات الممسوحة
                    page_count = pdf_only_scanner.get_scanned_pages_count()
                    print(f"تم مسح {page_count} صفحة بنجاح باستخدام pdf_only_scanner.scan_document مع ماسح CANON DR-M260")

                    # إرجاع معلومات عن الصفحات الممسوحة
                    return jsonify({
                        'success': True,
                        'page_count': page_count,
                        'message': f'تم مسح {page_count} صفحة بنجاح باستخدام الطريقة البديلة',
                        'method': 'canon_scanner_fallback'
                    })
            except Exception as scan_error:
                print(f"خطأ في مسح المستند باستخدام ماسح CANON DR-M260: {str(scan_error)}")
                traceback.print_exc()

                # محاولة استخدام الدالة scan_document مباشرة كبديل
                print("محاولة استخدام pdf_only_scanner.scan_document كبديل بعد حدوث خطأ مع ماسح CANON DR-M260")
                try:
                    success = pdf_only_scanner.scan_document(
                        scanner_id='0',
                        dpi=dpi,
                        use_adf=use_adf
                    )

                    if not success:
                        print("فشل في مسح المستند باستخدام pdf_only_scanner.scan_document مع ماسح CANON DR-M260")

                        # محاولة استخدام ماسح PDF فقط كبديل
                        print("محاولة استخدام ماسح PDF فقط كبديل بعد فشل ماسح CANON DR-M260")
                        fallback_success = pdf_only_scanner.scan_document(
                            scanner_id=None,  # استخدام القيمة الافتراضية
                            dpi=dpi,
                            use_adf=use_adf
                        )

                        if not fallback_success:
                            print("فشل في مسح المستند باستخدام ماسح PDF البديل")
                            return jsonify({'error': 'فشل في مسح المستند باستخدام جميع الماسحات المتاحة'}), 500

                        # الحصول على عدد الصفحات الممسوحة
                        page_count = pdf_only_scanner.get_scanned_pages_count()
                        print(f"تم مسح {page_count} صفحة بنجاح باستخدام ماسح PDF البديل")

                        return jsonify({
                            'success': True,
                            'page_count': page_count,
                            'message': f'تم مسح {page_count} صفحة بنجاح باستخدام ماسح PDF البديل',
                            'method': 'pdf_only',
                            'used_fallback': True
                        })

                    # الحصول على عدد الصفحات الممسوحة
                    page_count = pdf_only_scanner.get_scanned_pages_count()
                    print(f"تم مسح {page_count} صفحة بنجاح باستخدام pdf_only_scanner.scan_document مع ماسح CANON DR-M260")

                    # إرجاع معلومات عن الصفحات الممسوحة
                    return jsonify({
                        'success': True,
                        'page_count': page_count,
                        'message': f'تم مسح {page_count} صفحة بنجاح باستخدام الطريقة البديلة',
                        'method': 'canon_scanner_fallback'
                    })
                except Exception as fallback_error:
                    print(f"خطأ في مسح المستند باستخدام الطريقة البديلة مع ماسح CANON DR-M260: {str(fallback_error)}")
                    traceback.print_exc()

                    # محاولة استخدام ماسح PDF فقط كبديل أخير
                    print("محاولة استخدام ماسح PDF فقط كبديل أخير بعد فشل جميع المحاولات السابقة")
                    try:
                        fallback_success = pdf_only_scanner.scan_document(
                            scanner_id=None,  # استخدام القيمة الافتراضية
                            dpi=dpi,
                            use_adf=use_adf
                        )

                        if not fallback_success:
                            print("فشل في مسح المستند باستخدام ماسح PDF البديل")
                            return jsonify({'error': 'فشل في مسح المستند باستخدام جميع الماسحات المتاحة'}), 500

                        # الحصول على عدد الصفحات الممسوحة
                        page_count = pdf_only_scanner.get_scanned_pages_count()
                        print(f"تم مسح {page_count} صفحة بنجاح باستخدام ماسح PDF البديل")

                        return jsonify({
                            'success': True,
                            'page_count': page_count,
                            'message': f'تم مسح {page_count} صفحة بنجاح باستخدام ماسح PDF البديل',
                            'method': 'pdf_only',
                            'used_fallback': True
                        })
                    except Exception as final_error:
                        print(f"خطأ في مسح المستند باستخدام الطريقة البديلة الأخيرة: {str(final_error)}")
                        traceback.print_exc()
                        return jsonify({'error': f'فشل في مسح المستند: {str(scan_error)}\nفشل في الطريقة البديلة: {str(fallback_error)}\nفشل في الطريقة البديلة الأخيرة: {str(final_error)}'}), 500

        # التعامل مع أي ماسح آخر
        else:
            print(f"استخدام ماسح ضوئي آخر: {device_id}")

            # استخدام pdf_only_scanner مع معرف الجهاز المحدد
            success = pdf_only_scanner.scan_document(
                scanner_id=device_id,
                dpi=dpi,
                use_adf=use_adf
            )

            if not success:
                print(f"فشل في مسح المستند باستخدام الماسح {device_id}")

                # محاولة استخدام ماسح PDF فقط كبديل
                print("محاولة استخدام ماسح PDF فقط كبديل")
                fallback_success = pdf_only_scanner.scan_document(
                    scanner_id=None,  # استخدام القيمة الافتراضية
                    dpi=dpi,
                    use_adf=use_adf
                )

                if not fallback_success:
                    print("فشل في مسح المستند باستخدام ماسح PDF البديل")
                    return jsonify({'error': 'فشل في مسح المستند باستخدام جميع الماسحات المتاحة'}), 500

                # الحصول على عدد الصفحات الممسوحة
                page_count = pdf_only_scanner.get_scanned_pages_count()
                print(f"تم مسح {page_count} صفحة بنجاح باستخدام ماسح PDF البديل")

                return jsonify({
                    'success': True,
                    'page_count': page_count,
                    'message': f'تم مسح {page_count} صفحة بنجاح باستخدام ماسح PDF البديل',
                    'method': 'pdf_only',
                    'used_fallback': True
                })

            # الحصول على عدد الصفحات الممسوحة
            page_count = pdf_only_scanner.get_scanned_pages_count()
            print(f"تم مسح {page_count} صفحة بنجاح باستخدام الماسح {device_id}")

            return jsonify({
                'success': True,
                'page_count': page_count,
                'message': f'تم مسح {page_count} صفحة بنجاح',
                'method': 'custom_scanner'
            })

    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"خطأ في المسح الضوئي: {str(e)}")

        # محاولة استخدام ماسح PDF فقط كبديل في حالة الخطأ
        try:
            print("محاولة استخدام ماسح PDF فقط كبديل بعد حدوث خطأ")
            fallback_success = pdf_only_scanner.scan_document(
                scanner_id=None,  # استخدام القيمة الافتراضية
                dpi=300,
                use_adf=True
            )

            if fallback_success:
                # الحصول على عدد الصفحات الممسوحة
                page_count = pdf_only_scanner.get_scanned_pages_count()
                print(f"تم مسح {page_count} صفحة بنجاح باستخدام ماسح PDF البديل")

                return jsonify({
                    'success': True,
                    'message': 'تم المسح الضوئي بنجاح باستخدام ماسح PDF البديل',
                    'page_count': page_count,
                    'used_fallback': True,
                    'error_handled': str(e)
                })
            else:
                print("فشل في مسح المستند باستخدام ماسح PDF البديل")
        except Exception as fallback_error:
            print(f"خطأ في استخدام ماسح PDF البديل: {str(fallback_error)}")

        return jsonify({'error': f'حدث خطأ أثناء المسح الضوئي: {str(e)}'}), 500

@app.route('/open_scanner_ui_api', methods=['POST'])
def open_scanner_ui_api():
    """فتح واجهة المستخدم الخاصة بالماسح الضوئي (واجهة برمجة التطبيقات)"""
    print("=== بدء تنفيذ دالة open_scanner_ui_api ===")

    # تجاوز التحقق من صلاحيات المستخدم مؤقتًا لأغراض التصحيح
    # if not session.get('employee_id'):
    #     print("خطأ: المستخدم غير مصرح")
    #     return jsonify({'error': 'غير مصرح'}), 401
    #
    # # التحقق من صلاحيات المستخدم (فقط المدير يمكنه استخدام الماسح الضوئي)
    # if not session.get('is_admin'):
    #     print("خطأ: المستخدم ليس مديرًا")
    #     return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه الوظيفة'}), 403

    print("تم تجاوز التحقق من صلاحيات المستخدم مؤقتًا لأغراض التصحيح")

    try:
        # الحصول على بيانات الطلب
        data = request.json
        print(f"بيانات الطلب المستلمة: {data}")

        if not data:
            print("خطأ: لم يتم استلام أي بيانات")
            # استخدام القيمة الافتراضية
            data = {
                'device_id': 'pdf_scanner'  # استخدام ماسح PDF كقيمة افتراضية
            }
            print(f"استخدام البيانات الافتراضية: {data}")

        device_id = data.get('device_id', 'pdf_scanner')

        print(f"بيانات الطلب المعالجة: device_id={device_id}")

        # التعامل مع ماسح PDF فقط
        if device_id == 'pdf_scanner':
            print("استخدام ماسح PDF فقط")

            # فتح واجهة المستخدم الخاصة بماسح PDF
            result = pdf_only_scanner.open_scanner_ui(device_id)

            print(f"نتيجة فتح واجهة المستخدم: {result}")

            if not result.get('success', False):
                error_message = result.get('message', 'فشل في فتح واجهة المستخدم الخاصة بماسح PDF')
                print(f"خطأ في فتح واجهة المستخدم: {error_message}")
                return jsonify({'error': error_message}), 500

            print("تم فتح واجهة المستخدم بنجاح")
            return jsonify({
                'success': True,
                'message': 'تم فتح واجهة المستخدم الخاصة بماسح PDF بنجاح'
            })

        # التعامل مع ماسح CANON DR-M260
        elif device_id == '0' or device_id == 0:
            print("استخدام ماسح CANON DR-M260")

            # فتح واجهة المستخدم الخاصة بماسح CANON DR-M260
            result = pdf_only_scanner.open_scanner_ui('0')

            print(f"نتيجة فتح واجهة المستخدم: {result}")

            if not result.get('success', False):
                error_message = result.get('message', 'فشل في فتح واجهة المستخدم الخاصة بماسح CANON DR-M260')
                print(f"خطأ في فتح واجهة المستخدم: {error_message}")

                # محاولة استخدام ماسح PDF فقط كبديل
                print("محاولة استخدام ماسح PDF فقط كبديل")
                fallback_result = pdf_only_scanner.open_scanner_ui('pdf_scanner')

                if not fallback_result.get('success', False):
                    print(f"فشل في فتح واجهة المستخدم الخاصة بماسح PDF البديل: {fallback_result.get('message', '')}")
                    return jsonify({'error': error_message}), 500

                print("تم فتح واجهة المستخدم الخاصة بماسح PDF البديل بنجاح")
                return jsonify({
                    'success': True,
                    'message': 'تم فتح واجهة المستخدم الخاصة بماسح PDF البديل بنجاح',
                    'used_fallback': True
                })

            print("تم فتح واجهة المستخدم بنجاح")
            return jsonify({
                'success': True,
                'message': 'تم فتح واجهة المستخدم الخاصة بماسح CANON DR-M260 بنجاح'
            })

        # التعامل مع أي ماسح آخر
        else:
            print(f"استخدام ماسح ضوئي آخر: {device_id}")

            # فتح واجهة المستخدم الخاصة بالماسح المحدد
            result = pdf_only_scanner.open_scanner_ui(device_id)

            print(f"نتيجة فتح واجهة المستخدم: {result}")

            if not result.get('success', False):
                error_message = result.get('message', f'فشل في فتح واجهة المستخدم الخاصة بالماسح {device_id}')
                print(f"خطأ في فتح واجهة المستخدم: {error_message}")

                # محاولة استخدام ماسح PDF فقط كبديل
                print("محاولة استخدام ماسح PDF فقط كبديل")
                fallback_result = pdf_only_scanner.open_scanner_ui('pdf_scanner')

                if not fallback_result.get('success', False):
                    print(f"فشل في فتح واجهة المستخدم الخاصة بماسح PDF البديل: {fallback_result.get('message', '')}")
                    return jsonify({'error': error_message}), 500

                print("تم فتح واجهة المستخدم الخاصة بماسح PDF البديل بنجاح")
                return jsonify({
                    'success': True,
                    'message': 'تم فتح واجهة المستخدم الخاصة بماسح PDF البديل بنجاح',
                    'used_fallback': True
                })

            print("تم فتح واجهة المستخدم بنجاح")
            return jsonify({
                'success': True,
                'message': f'تم فتح واجهة المستخدم الخاصة بالماسح {device_id} بنجاح'
            })

    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"خطأ في فتح واجهة المستخدم الخاصة بالماسح الضوئي: {str(e)}")

        # محاولة استخدام ماسح PDF فقط كبديل في حالة الخطأ
        try:
            print("محاولة استخدام ماسح PDF فقط كبديل بعد حدوث خطأ")
            fallback_result = pdf_only_scanner.open_scanner_ui('pdf_scanner')

            if fallback_result.get('success', False):
                print("تم فتح واجهة المستخدم الخاصة بماسح PDF البديل بنجاح")
                return jsonify({
                    'success': True,
                    'message': 'تم فتح واجهة المستخدم الخاصة بماسح PDF البديل بنجاح',
                    'used_fallback': True,
                    'error_handled': str(e)
                })
        except Exception as fallback_error:
            print(f"خطأ في استخدام ماسح PDF البديل: {str(fallback_error)}")

        return jsonify({'error': f'حدث خطأ أثناء فتح واجهة المستخدم الخاصة بالماسح الضوئي: {str(e)}'}), 500

# دالة مساعدة للحصول على مستندات الإجازة
@app.template_global()
def get_documents(leave_id):
    """الحصول على مستندات الإجازة"""
    return Document.query.filter_by(leave_id=leave_id).order_by(Document.upload_date.desc()).all()

# صفحة التقارير الرئيسية
@app.route('/reports')
def reports():
    """صفحة التقارير الرئيسية"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    return render_template('reports.html')

# تقارير الموظفين
@app.route('/employee_reports')
def employee_reports():
    """تقارير الموظفين"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على معلمات البحث
    employee_id = request.args.get('employee_id', '')
    leave_type_id = request.args.get('leave_type_id', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    report_period = request.args.get('report_period', '')

    # الحصول على التاريخ الحالي
    today = datetime.now().date()

    # تحديد الفترة الزمنية بناءً على الاختيار
    if report_period == 'current_month' or report_period == 'month':
        # الشهر الحالي
        start_date = datetime(today.year, today.month, 1).date()
        end_date = today
    elif report_period == 'last_month':
        # الشهر الماضي
        if today.month == 1:
            # إذا كان الشهر الحالي هو يناير، فالشهر الماضي هو ديسمبر من السنة الماضية
            start_date = datetime(today.year - 1, 12, 1).date()
            end_date = datetime(today.year, 1, 1).date() - timedelta(days=1)
        else:
            # غير ذلك، الشهر الماضي هو الشهر السابق من نفس السنة
            start_date = datetime(today.year, today.month - 1, 1).date()
            # آخر يوم في الشهر الماضي
            if today.month == 3:  # مارس
                # التعامل مع فبراير بشكل خاص (28 أو 29 يوم)
                if (today.year % 4 == 0 and today.year % 100 != 0) or (today.year % 400 == 0):  # سنة كبيسة
                    end_date = datetime(today.year, 2, 29).date()
                else:
                    end_date = datetime(today.year, 2, 28).date()
            else:
                # آخر يوم في الشهر الماضي هو اليوم السابق لأول يوم في الشهر الحالي
                end_date = datetime(today.year, today.month, 1).date() - timedelta(days=1)
    elif report_period == 'current_year' or report_period == 'year':
        # السنة الحالية
        start_date = datetime(today.year, 1, 1).date()
        end_date = today
    elif report_period == 'last_year':
        # السنة الماضية
        start_date = datetime(today.year - 1, 1, 1).date()
        end_date = datetime(today.year - 1, 12, 31).date()
    else:
        # تحويل التواريخ إلى كائنات datetime
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            except ValueError:
                # في حالة وجود خطأ في تنسيق التاريخ، استخدم بداية الشهر الحالي
                start_date = datetime(today.year, today.month, 1).date()
        else:
            # استخدام بداية الشهر الحالي كتاريخ افتراضي
            start_date = datetime(today.year, today.month, 1).date()

        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                # في حالة وجود خطأ في تنسيق التاريخ، استخدم اليوم الحالي
                end_date = today
        else:
            # استخدام اليوم الحالي كتاريخ افتراضي
            end_date = today

    # بناء الاستعلام - تعديل الاستعلام ليشمل الإجازات التي تقع ضمن الفترة المحددة تمامًا
    # مع التأكيد على الالتزام بتاريخ البداية والنهاية
    query = LeaveRequest.query.filter(
        # الإجازات التي تبدأ بعد أو في تاريخ البداية المحدد
        (LeaveRequest.start_date >= start_date) &
        # والإجازات التي تنتهي قبل أو في تاريخ النهاية المحدد
        (LeaveRequest.end_date <= end_date)
    )

    # تطبيق تصفية الموظف إذا تم تحديدها
    if employee_id:
        query = query.filter(LeaveRequest.employee_id == employee_id)

    # تطبيق تصفية نوع الإجازة إذا تم تحديدها
    if leave_type_id:
        query = query.filter(LeaveRequest.leave_type_id == leave_type_id)

    # الحصول على الإجازات
    leaves = query.order_by(LeaveRequest.start_date.desc()).all()

    # الحصول على بيانات التقرير
    report_data = []
    for leave in leaves:
        employee = Employee.query.get(leave.employee_id)
        leave_type = LeaveType.query.get(leave.leave_type_id)

        if employee and leave_type:
            report_data.append({
                'leave': leave,
                'employee': employee,
                'leave_type': leave_type
            })

    # الحصول على قائمة الموظفين وأنواع الإجازات للتصفية
    employees = Employee.query.order_by(Employee.full_name).all()
    leave_types = LeaveType.query.order_by(LeaveType.name).all()

    # حساب إحصائيات الفترة الزمنية
    total_leaves = len(report_data)
    total_days = sum(item['leave'].days_count for item in report_data)

    # حساب متوسط مدة الإجازة
    average_days = round(total_days / total_leaves, 1) if total_leaves > 0 else 0

    # الحصول على التاريخ الحالي
    today = datetime.now().date()

    return render_template('employee_reports.html',
                          report_data=report_data,
                          employees=employees,
                          leave_types=leave_types,
                          employee_id=employee_id,
                          leave_type_id=leave_type_id,
                          start_date=start_date.strftime('%Y-%m-%d'),
                          end_date=end_date.strftime('%Y-%m-%d'),
                          total_leaves=total_leaves,
                          total_days=total_days,
                          average_days=average_days,
                          current_date=today)

# تقارير أنواع الإجازات
@app.route('/leave_type_reports')
def leave_type_reports():
    """تقارير أنواع الإجازات"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على معلمات البحث
    leave_type_id = request.args.get('leave_type_id', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    report_period = request.args.get('report_period', '')

    # الحصول على التاريخ الحالي
    today = datetime.now().date()

    # تحديد الفترة الزمنية بناءً على الاختيار
    if report_period == 'current_month' or report_period == 'month':
        # الشهر الحالي
        start_date = datetime(today.year, today.month, 1).date()
        end_date = today
    elif report_period == 'last_month':
        # الشهر الماضي
        if today.month == 1:
            # إذا كان الشهر الحالي هو يناير، فالشهر الماضي هو ديسمبر من السنة الماضية
            start_date = datetime(today.year - 1, 12, 1).date()
            end_date = datetime(today.year, 1, 1).date() - timedelta(days=1)
        else:
            # غير ذلك، الشهر الماضي هو الشهر السابق من نفس السنة
            start_date = datetime(today.year, today.month - 1, 1).date()
            # آخر يوم في الشهر الماضي
            if today.month == 3:  # مارس
                # التعامل مع فبراير بشكل خاص (28 أو 29 يوم)
                if (today.year % 4 == 0 and today.year % 100 != 0) or (today.year % 400 == 0):  # سنة كبيسة
                    end_date = datetime(today.year, 2, 29).date()
                else:
                    end_date = datetime(today.year, 2, 28).date()
            else:
                # آخر يوم في الشهر الماضي هو اليوم السابق لأول يوم في الشهر الحالي
                end_date = datetime(today.year, today.month, 1).date() - timedelta(days=1)
    elif report_period == 'current_year' or report_period == 'year':
        # السنة الحالية
        start_date = datetime(today.year, 1, 1).date()
        end_date = today
    elif report_period == 'last_year':
        # السنة الماضية
        start_date = datetime(today.year - 1, 1, 1).date()
        end_date = datetime(today.year - 1, 12, 31).date()
    else:
        # تحويل التواريخ إلى كائنات datetime
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            except ValueError:
                # في حالة وجود خطأ في تنسيق التاريخ، استخدم بداية الشهر الحالي
                start_date = datetime(today.year, today.month, 1).date()
        else:
            # استخدام بداية الشهر الحالي كتاريخ افتراضي
            start_date = datetime(today.year, today.month, 1).date()

        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                # في حالة وجود خطأ في تنسيق التاريخ، استخدم اليوم الحالي
                end_date = today
        else:
            # استخدام اليوم الحالي كتاريخ افتراضي
            end_date = today

    # بناء الاستعلام - تعديل الاستعلام ليشمل الإجازات التي تقع ضمن الفترة المحددة تمامًا
    # مع التأكيد على الالتزام بتاريخ البداية والنهاية
    query = LeaveRequest.query.filter(
        # الإجازات التي تبدأ بعد أو في تاريخ البداية المحدد
        (LeaveRequest.start_date >= start_date) &
        # والإجازات التي تنتهي قبل أو في تاريخ النهاية المحدد
        (LeaveRequest.end_date <= end_date)
    )

    # تطبيق تصفية نوع الإجازة إذا تم تحديدها
    if leave_type_id:
        query = query.filter(LeaveRequest.leave_type_id == leave_type_id)

    # الحصول على الإجازات
    leaves = query.order_by(LeaveRequest.start_date.desc()).all()

    # الحصول على بيانات التقرير
    report_data = []
    for leave in leaves:
        employee = Employee.query.get(leave.employee_id)
        leave_type = LeaveType.query.get(leave.leave_type_id)

        if employee and leave_type:
            report_data.append({
                'leave': leave,
                'employee': employee,
                'leave_type': leave_type
            })

    # الحصول على أنواع الإجازات للتصفية
    leave_types = LeaveType.query.order_by(LeaveType.name).all()

    # حساب إحصائيات أنواع الإجازات
    leave_type_stats = {}
    for lt in leave_types:
        leave_type_stats[lt.id] = {
            'name': lt.name,
            'count': 0,
            'total_days': 0
        }

    for item in report_data:
        lt_id = item['leave_type'].id
        if lt_id in leave_type_stats:
            leave_type_stats[lt_id]['count'] += 1
            leave_type_stats[lt_id]['total_days'] += item['leave'].days_count

    # حساب متوسط مدة الإجازة
    total_leaves = len(report_data)
    total_days = sum(item['leave'].days_count for item in report_data)
    average_days = round(total_days / total_leaves, 1) if total_leaves > 0 else 0

    # الحصول على التاريخ الحالي
    today = datetime.now().date()

    # إنشاء ملخص حسب نوع الإجازة
    summary_by_type = {}
    for item in report_data:
        leave_type_name = item['leave_type'].name
        if leave_type_name not in summary_by_type:
            summary_by_type[leave_type_name] = {
                'count': 0,
                'total_days': 0,
                'percentage': 0
            }
        summary_by_type[leave_type_name]['count'] += 1
        summary_by_type[leave_type_name]['total_days'] += item['leave'].days_count

    # حساب النسب المئوية
    if total_days > 0:
        for leave_type_name in summary_by_type:
            summary_by_type[leave_type_name]['percentage'] = round(
                (summary_by_type[leave_type_name]['total_days'] / total_days) * 100, 1
            )

    # إنشاء ملخص حسب القسم
    summary_by_dept = {}
    for item in report_data:
        dept = item['employee'].department or 'غير محدد'
        if dept not in summary_by_dept:
            summary_by_dept[dept] = {
                'count': 0,
                'total_days': 0,
                'percentage': 0
            }
        summary_by_dept[dept]['count'] += 1
        summary_by_dept[dept]['total_days'] += item['leave'].days_count

    # حساب النسب المئوية
    if total_days > 0:
        for dept in summary_by_dept:
            summary_by_dept[dept]['percentage'] = round(
                (summary_by_dept[dept]['total_days'] / total_days) * 100, 1
            )

    return render_template('leave_type_reports.html',
                          report_data=report_data,
                          leave_types=leave_types,
                          leave_type_id=leave_type_id,
                          start_date=start_date.strftime('%Y-%m-%d'),
                          end_date=end_date.strftime('%Y-%m-%d'),
                          leave_type_stats=leave_type_stats,
                          total_leaves=total_leaves,
                          total_days=total_days,
                          average_days=average_days,
                          current_date=today,
                          summary_by_type=summary_by_type,
                          summary_by_dept=summary_by_dept)

# تقارير الفترات الزمنية
@app.route('/date_range_reports')
def date_range_reports():
    """تقارير الفترات الزمنية"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على معلمات البحث
    report_period = request.args.get('report_period', 'month')
    start_date_str = request.args.get('start_date', '')
    end_date_str = request.args.get('end_date', '')

    # تحديد الفترة الزمنية بناءً على الاختيار
    today = datetime.now().date()

    if report_period == 'week':
        # الأسبوع الحالي
        start_date = today - timedelta(days=today.weekday())
        end_date = today
    elif report_period == 'month' or report_period == 'current_month':
        # الشهر الحالي
        start_date = datetime(today.year, today.month, 1).date()
        end_date = today
    elif report_period == 'last_month':
        # الشهر الماضي
        if today.month == 1:
            # إذا كان الشهر الحالي هو يناير، فالشهر الماضي هو ديسمبر من السنة الماضية
            start_date = datetime(today.year - 1, 12, 1).date()
            end_date = datetime(today.year, 1, 1).date() - timedelta(days=1)
        else:
            # غير ذلك، الشهر الماضي هو الشهر السابق من نفس السنة
            start_date = datetime(today.year, today.month - 1, 1).date()
            # آخر يوم في الشهر الماضي
            if today.month == 3:  # مارس
                # التعامل مع فبراير بشكل خاص (28 أو 29 يوم)
                if (today.year % 4 == 0 and today.year % 100 != 0) or (today.year % 400 == 0):  # سنة كبيسة
                    end_date = datetime(today.year, 2, 29).date()
                else:
                    end_date = datetime(today.year, 2, 28).date()
            else:
                # آخر يوم في الشهر الماضي هو اليوم السابق لأول يوم في الشهر الحالي
                end_date = datetime(today.year, today.month, 1).date() - timedelta(days=1)
    elif report_period == 'quarter':
        # الربع الحالي
        quarter = (today.month - 1) // 3
        start_date = datetime(today.year, quarter * 3 + 1, 1).date()
        end_date = today
    elif report_period == 'year' or report_period == 'current_year':
        # السنة الحالية
        start_date = datetime(today.year, 1, 1).date()
        end_date = today
    elif report_period == 'last_year':
        # السنة الماضية
        start_date = datetime(today.year - 1, 1, 1).date()
        end_date = datetime(today.year - 1, 12, 31).date()
    elif report_period == 'custom':
        # فترة مخصصة
        if start_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            except ValueError:
                # في حالة وجود خطأ في تنسيق التاريخ، استخدم بداية الشهر الحالي
                start_date = datetime(today.year, today.month, 1).date()
        else:
            start_date = datetime(today.year, today.month, 1).date()

        if end_date_str:
            try:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            except ValueError:
                # في حالة وجود خطأ في تنسيق التاريخ، استخدم اليوم الحالي
                end_date = today
        else:
            end_date = today

    # بناء الاستعلام - تعديل الاستعلام ليشمل الإجازات التي تقع ضمن الفترة المحددة تمامًا
    # مع التأكيد على الالتزام بتاريخ البداية والنهاية
    query = LeaveRequest.query.filter(
        # الإجازات التي تبدأ بعد أو في تاريخ البداية المحدد
        (LeaveRequest.start_date >= start_date) &
        # والإجازات التي تنتهي قبل أو في تاريخ النهاية المحدد
        (LeaveRequest.end_date <= end_date)
    )

    # الحصول على الإجازات
    leaves = query.order_by(LeaveRequest.start_date.desc()).all()

    # الحصول على بيانات التقرير
    report_data = []
    for leave in leaves:
        employee = Employee.query.get(leave.employee_id)
        leave_type = LeaveType.query.get(leave.leave_type_id)

        if employee and leave_type:
            report_data.append({
                'leave': leave,
                'employee': employee,
                'leave_type': leave_type
            })

    # حساب إحصائيات الفترة الزمنية
    total_leaves = len(report_data)
    total_days = sum(item['leave'].days_count for item in report_data)

    # حساب متوسط مدة الإجازة
    average_days = round(total_days / total_leaves, 1) if total_leaves > 0 else 0

    # حساب عدد الإجازات حسب النوع
    leave_type_counts = {}
    for item in report_data:
        lt_name = item['leave_type'].name
        if lt_name not in leave_type_counts:
            leave_type_counts[lt_name] = 0
        leave_type_counts[lt_name] += 1

    # إعداد توزيع الإجازات حسب الشهر
    month_names = ["يناير", "فبراير", "مارس", "إبريل", "مايو", "يونيو",
                  "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]

    monthly_distribution = {
        'counts': [0] * 12,
        'days': [0] * 12
    }

    # حساب توزيع الإجازات حسب الشهر
    for item in report_data:
        leave = item['leave']
        # حساب الشهر من تاريخ بداية الإجازة
        month_index = leave.start_date.month - 1
        monthly_distribution['counts'][month_index] += 1
        monthly_distribution['days'][month_index] += leave.days_count

    return render_template('date_range_reports.html',
                          report_data=report_data,
                          report_period=report_period,
                          start_date=start_date.strftime('%Y-%m-%d'),
                          end_date=end_date.strftime('%Y-%m-%d'),
                          total_leaves=total_leaves,
                          total_days=total_days,
                          average_days=average_days,
                          leave_type_counts=leave_type_counts,
                          month_names=month_names,
                          monthly_distribution=monthly_distribution,
                          current_date=today)

# تقارير الأقسام
@app.route('/department_reports')
def department_reports():
    """تقارير الأقسام"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    if not session.get('is_admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على معلمات البحث
    department = request.args.get('department', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    report_period = request.args.get('report_period', '')

    # الحصول على التاريخ الحالي
    today = datetime.now().date()

    # تحديد الفترة الزمنية بناءً على الاختيار
    if report_period == 'current_month' or report_period == 'month':
        # الشهر الحالي
        start_date = datetime(today.year, today.month, 1).date()
        end_date = today
    elif report_period == 'last_month':
        # الشهر الماضي
        if today.month == 1:
            # إذا كان الشهر الحالي هو يناير، فالشهر الماضي هو ديسمبر من السنة الماضية
            start_date = datetime(today.year - 1, 12, 1).date()
            end_date = datetime(today.year, 1, 1).date() - timedelta(days=1)
        else:
            # غير ذلك، الشهر الماضي هو الشهر السابق من نفس السنة
            start_date = datetime(today.year, today.month - 1, 1).date()
            # آخر يوم في الشهر الماضي
            if today.month == 3:  # مارس
                # التعامل مع فبراير بشكل خاص (28 أو 29 يوم)
                if (today.year % 4 == 0 and today.year % 100 != 0) or (today.year % 400 == 0):  # سنة كبيسة
                    end_date = datetime(today.year, 2, 29).date()
                else:
                    end_date = datetime(today.year, 2, 28).date()
            else:
                # آخر يوم في الشهر الماضي هو اليوم السابق لأول يوم في الشهر الحالي
                end_date = datetime(today.year, today.month, 1).date() - timedelta(days=1)
    elif report_period == 'current_year' or report_period == 'year':
        # السنة الحالية
        start_date = datetime(today.year, 1, 1).date()
        end_date = today
    elif report_period == 'last_year':
        # السنة الماضية
        start_date = datetime(today.year - 1, 1, 1).date()
        end_date = datetime(today.year - 1, 12, 31).date()
    else:
        # تحويل التواريخ إلى كائنات datetime
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            except ValueError:
                # في حالة وجود خطأ في تنسيق التاريخ، استخدم بداية الشهر الحالي
                start_date = datetime(today.year, today.month, 1).date()
        else:
            # استخدام بداية الشهر الحالي كتاريخ افتراضي
            start_date = datetime(today.year, today.month, 1).date()

        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                # في حالة وجود خطأ في تنسيق التاريخ، استخدم اليوم الحالي
                end_date = today
        else:
            # استخدام اليوم الحالي كتاريخ افتراضي
            end_date = today

    # الحصول على قائمة الأقسام
    departments = db.session.query(Employee.department).filter(Employee.department != None).distinct().all()
    departments = [dept[0] for dept in departments if dept[0]]

    # بناء الاستعلام - تعديل الاستعلام ليشمل الإجازات التي تقع ضمن الفترة المحددة تمامًا
    # مع التأكيد على الالتزام بتاريخ البداية والنهاية
    query = LeaveRequest.query.filter(
        # الإجازات التي تبدأ بعد أو في تاريخ البداية المحدد
        (LeaveRequest.start_date >= start_date) &
        # والإجازات التي تنتهي قبل أو في تاريخ النهاية المحدد
        (LeaveRequest.end_date <= end_date)
    )

    # الحصول على الإجازات
    leaves = query.order_by(LeaveRequest.start_date.desc()).all()

    # الحصول على بيانات التقرير
    report_data = []
    for leave in leaves:
        employee = Employee.query.get(leave.employee_id)
        leave_type = LeaveType.query.get(leave.leave_type_id)

        if employee and leave_type:
            # تطبيق تصفية القسم إذا تم تحديدها
            if department and employee.department != department:
                continue

            report_data.append({
                'leave': leave,
                'employee': employee,
                'leave_type': leave_type
            })

    # الحصول على عدد الموظفين في كل قسم
    employees_by_dept = {}
    all_employees = Employee.query.all()
    total_employees = len(all_employees)

    for emp in all_employees:
        dept = emp.department or 'غير محدد'
        if dept not in employees_by_dept:
            employees_by_dept[dept] = 0
        employees_by_dept[dept] += 1

    # حساب إحصائيات الأقسام
    department_stats = {}
    for dept in departments:
        department_stats[dept] = {
            'count': 0,
            'total_days': 0
        }

    for item in report_data:
        dept = item['employee'].department
        if dept in department_stats:
            department_stats[dept]['count'] += 1
            department_stats[dept]['total_days'] += item['leave'].days_count

    # إنشاء ملخص الأقسام
    department_summary = {}
    for dept in departments:
        employee_count = employees_by_dept.get(dept, 0)
        leave_count = department_stats.get(dept, {}).get('count', 0)
        total_days = department_stats.get(dept, {}).get('total_days', 0)

        department_summary[dept] = {
            'employee_count': employee_count,
            'leave_count': leave_count,
            'total_days': total_days,
            'avg_days_per_employee': round(total_days / employee_count, 1) if employee_count > 0 else 0
        }

    # إنشاء ملخص أنواع الإجازات
    leave_type_summary = {}
    for item in report_data:
        leave_type_name = item['leave_type'].name
        if leave_type_name not in leave_type_summary:
            leave_type_summary[leave_type_name] = {
                'count': 0,
                'total_days': 0,
                'percentage': 0
            }
        leave_type_summary[leave_type_name]['count'] += 1
        leave_type_summary[leave_type_name]['total_days'] += item['leave'].days_count

    # حساب إحصائيات الفترة الزمنية
    total_leaves = len(report_data)
    total_days = sum(item['leave'].days_count for item in report_data)

    # حساب متوسط مدة الإجازة
    average_days = round(total_days / total_leaves, 1) if total_leaves > 0 else 0

    # حساب متوسط أيام الإجازة لكل موظف
    overall_avg_days_per_employee = round(total_days / total_employees, 1) if total_employees > 0 else 0

    # حساب النسب المئوية لأنواع الإجازات
    if total_days > 0:
        for leave_type_name in leave_type_summary:
            leave_type_summary[leave_type_name]['percentage'] = round(
                (leave_type_summary[leave_type_name]['total_days'] / total_days) * 100, 1
            )

    # إذا تم تحديد قسم، قم بتحديث إحصائيات القسم
    if department:
        department_stats = {
            'employee_count': employees_by_dept.get(department, 0),
            'leave_count': department_stats.get(department, {}).get('count', 0),
            'total_days': department_stats.get(department, {}).get('total_days', 0)
        }

    # الحصول على التاريخ الحالي
    today = datetime.now().date()

    return render_template('department_reports.html',
                          report_data=report_data,
                          departments=departments,
                          selected_department=department,
                          start_date=start_date.strftime('%Y-%m-%d'),
                          end_date=end_date.strftime('%Y-%m-%d'),
                          department_stats=department_stats,
                          total_leaves=total_leaves,
                          total_days=total_days,
                          average_days=average_days,
                          current_date=today,
                          department_summary=department_summary,
                          total_employees=total_employees,
                          overall_avg_days_per_employee=overall_avg_days_per_employee,
                          leave_type_summary=leave_type_summary)

# ملاحظة: تم إزالة المسار المكرر '/save_scanned_document' لتجنب تعارض المسارات

# صفحة تعيين صلاحيات المستخدمين
@app.route('/set_permissions/<int:user_id>', methods=['GET', 'POST'])
def set_permissions(user_id):
    """تعيين صلاحيات المستخدم"""
    if not session.get('employee_id'):
        return redirect(url_for('login'))

    # التحقق من صلاحيات المستخدم
    employee = Employee.query.get(session.get('employee_id'))
    if not employee or not employee.is_admin:  # فقط المدراء يمكنهم تعيين الصلاحيات
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على المستخدم
    user = Employee.query.get_or_404(user_id)

    # قائمة الصلاحيات المتاحة
    available_permissions = [
        {'name': 'view_employees', 'description': 'عرض الموظفين'},
        {'name': 'add_employees', 'description': 'إضافة موظفين'},
        {'name': 'edit_employees', 'description': 'تعديل الموظفين'},
        {'name': 'delete_employees', 'description': 'حذف الموظفين'},
        {'name': 'view_leaves', 'description': 'عرض الإجازات'},
        {'name': 'add_leaves', 'description': 'إضافة إجازات'},
        {'name': 'edit_leaves', 'description': 'تعديل الإجازات'},
        {'name': 'delete_leaves', 'description': 'حذف الإجازات'},
        {'name': 'manage_leave_types', 'description': 'إدارة أنواع الإجازات'},
        {'name': 'manage_documents', 'description': 'إدارة المستندات'},
        {'name': 'view_documents', 'description': 'عرض المستندات'},
        {'name': 'search_leaves', 'description': 'البحث في الإجازات'},
        {'name': 'backup_database', 'description': 'النسخ الاحتياطي'},
        {'name': 'update_return_status', 'description': 'تحديث حالة العودة من الإجازة'},
        {'name': 'manage_users', 'description': 'إدارة المستخدمين'},
    ]

    # الصلاحيات الحالية للمستخدم
    current_permissions = user.permissions.split(',') if user.permissions else []

    if request.method == 'POST':
        # الحصول على الصلاحيات المحددة
        selected_permissions = request.form.getlist('permissions')

        # تحديث صلاحيات المستخدم
        if 'all' in selected_permissions:
            user.permissions = 'all'
        else:
            user.permissions = ','.join(selected_permissions)

        # حفظ التغييرات
        db.session.commit()

        flash(f'تم تحديث صلاحيات المستخدم {user.full_name} بنجاح', 'success')
        return redirect(url_for('user_management'))

    return render_template('set_permissions.html',
                          user=user,
                          available_permissions=available_permissions,
                          current_permissions=current_permissions)

if __name__ == '__main__':
    import os
    import webbrowser

    url = "http://localhost:5000"

    # Flask يعيد تشغيل نفسه بمتغير بيئي اسمه "WERKZEUG_RUN_MAIN"
    if os.environ.get("WERKZEUG_RUN_MAIN") == "true":
        webbrowser.open(url)

    app.run(debug=True, host='0.0.0.0')
