{% extends 'base.html' %}

{% block styles %}
<style>
    #scanner-container {
        width: 100%;
        height: 400px;
        border: 2px dashed #ccc;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        position: relative;
    }

    #scanner-preview {
        max-width: 100%;
        max-height: 100%;
        display: none;
    }

    #scanner-placeholder {
        text-align: center;
        padding: 20px;
    }

    .scanner-controls {
        margin-top: 20px;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .scanner-btn {
        min-width: 120px;
    }

    .scanner-icon {
        font-size: 48px;
        color: #6c757d;
        margin-bottom: 15px;
    }

    .flash-animation {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: white;
        opacity: 0;
        pointer-events: none;
        border-radius: 6px;
    }

    @keyframes flash {
        0% { opacity: 0; }
        50% { opacity: 1; }
        100% { opacity: 0; }
    }

    .flashing {
        animation: flash 0.5s;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h2 class="mb-0"><i class="fas fa-scanner me-2"></i> مسح مستند ضوئيًا</h2>
            <a href="{{ url_for('employee_leaves', employee_id=employee.id) }}" class="btn btn-light btn-sm">
                <i class="fas fa-arrow-right me-1"></i> العودة لإجازات الموظف
            </a>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title mb-3">معلومات الموظف</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>الاسم:</span>
                                    <strong>{{ employee.full_name }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>الرقم الوظيفي:</span>
                                    <strong>{{ employee.employee_number or '-' }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>المسمى الوظيفي:</span>
                                    <strong>{{ employee.job_title }}</strong>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title mb-3">معلومات الإجازة</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>نوع الإجازة:</span>
                                    <strong>{{ leave.leave_type.name }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>تاريخ البداية:</span>
                                    <strong>{{ leave.start_date.strftime('%Y-%m-%d') }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>تاريخ النهاية:</span>
                                    <strong>{{ leave.end_date.strftime('%Y-%m-%d') }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>المدة:</span>
                                    <strong>{{ leave.days_count }} يوم</strong>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <form method="POST" id="scan-form">
                <input type="hidden" name="scanned_image" id="scanned_image">

                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">مسح ضوئي للمستند</h5>
                    </div>
                    <div class="card-body">
                        <div id="scanner-container">
                            <div id="scanner-placeholder">
                                <i class="fas fa-file-image scanner-icon"></i>
                                <h4>جاهز للمسح الضوئي</h4>
                                <p class="text-muted">قم بتوصيل الماسح الضوئي واضغط على زر "بدء المسح الضوئي". سيتم حفظ المستند تلقائياً بعد المسح.</p>
                                <div id="scanner-list" class="mt-3 d-none">
                                    <label for="scanner-select" class="form-label">اختر جهاز المسح الضوئي:</label>
                                    <select id="scanner-select" class="form-select mb-2">
                                        <option value="">-- يرجى الاختيار --</option>
                                    </select>
                                </div>
                            </div>
                            <img id="scanner-preview" alt="معاينة المسح الضوئي">
                            <div class="flash-animation"></div>
                        </div>

                        <div class="scanner-select-container mb-4" style="background-color: #f8f9fa; padding: 15px; border-radius: 10px; border: 2px solid #0d6efd;">
                            <label for="scanner-select" class="form-label fw-bold fs-5 mb-3" style="color: #0d6efd;">اختر جهاز المسح الضوئي:</label>
                            <select id="scanner-select" class="form-select form-select-lg mb-3" style="border: 2px solid #0d6efd; padding: 12px; font-size: 1.1rem; background-color: #f0f8ff;">
                                <option value="" selected disabled>-- جاري البحث عن أجهزة المسح الضوئي... --</option>
                            </select>
                            <div class="d-flex justify-content-between">
                                <button type="button" id="detect-scanners" class="btn btn-info scanner-btn btn-lg fw-bold" style="background-color: #17a2b8; color: white; font-size: 1.2rem; padding: 10px 20px; width: 48%;">
                                    <i class="fas fa-sync-alt me-2"></i> تحديث قائمة الأجهزة
                                </button>
                                <button type="button" id="start-scan" class="btn btn-primary scanner-btn btn-lg fw-bold" style="background-color: #0d6efd; color: white; font-size: 1.2rem; padding: 10px 20px; width: 48%;">
                                    <i class="fas fa-scanner me-2"></i> بدء المسح الضوئي
                                </button>
                            </div>
                            <button type="button" id="retry-scan" class="btn btn-secondary scanner-btn btn-lg fw-bold mt-2 w-100" disabled style="font-size: 1.2rem; padding: 10px 20px;">
                                <i class="fas fa-redo me-2"></i> إعادة المحاولة
                            </button>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3 mt-3">
                                    <label for="description" class="form-label">وصف المستند (اختياري)</label>
                                    <input type="text" class="form-control" id="description" name="description" placeholder="مثال: أمر إداري، تقرير طبي، إلخ">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3 mt-3">
                                    <label class="form-label">صيغة الحفظ</label>
                                    <div class="d-flex">
                                        <div class="form-check me-3">
                                            <input class="form-check-input" type="radio" name="file_format" id="format_jpg" value="jpg" checked>
                                            <label class="form-check-label" for="format_jpg">
                                                صورة (JPG)
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="file_format" id="format_pdf" value="pdf">
                                            <label class="form-check-label" for="format_pdf">
                                                ملف PDF
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3 mt-3">
                                    <div class="card border-primary">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="mb-0">إعدادات التغذية التلقائية (ADF)</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" name="use_adf" id="use_adf" value="1" checked>
                                                <label class="form-check-label fw-bold" for="use_adf">
                                                    استخدام وحدة التغذية التلقائية (ADF) إذا كانت متوفرة
                                                </label>
                                            </div>
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>
                                                <small>عند تفعيل هذا الخيار، سيتم استخدام وحدة التغذية التلقائية للمستندات إذا كانت متوفرة في الماسح الضوئي. هذا يسمح بمسح عدة صفحات تلقائياً.</small>
                                            </div>
                                            <div class="alert alert-info mt-3">
                                                <i class="fas fa-info-circle me-2"></i>
                                                <strong>ميزة جديدة!</strong> تم تحسين دعم التغذية التلقائية (ADF). عند تفعيل هذا الخيار، سيحاول النظام مسح جميع الصفحات الموجودة في وحدة التغذية التلقائية ودمجها في ملف PDF واحد.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ url_for('employee_leaves', employee_id=employee.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i> إلغاء
                            </a>
                            <button type="submit" id="submit-scan" class="btn btn-primary" disabled>
                                <i class="fas fa-save me-1"></i> حفظ المستند
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            <!-- عرض المستندات الحالية -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">المستندات الحالية</h5>
                </div>
                <div class="card-body">
                    {% if documents %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>اسم المستند</th>
                                        <th>النوع</th>
                                        <th>تاريخ الرفع</th>
                                        <th>الوصف</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for doc in documents %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ doc.file_name }}</td>
                                            <td>
                                                {% if doc.document_type == 'uploaded' %}
                                                    <span class="badge bg-primary">مرفوع</span>
                                                {% elif doc.document_type == 'scanned' %}
                                                    <span class="badge bg-info">ممسوح ضوئياً</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ doc.document_type }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ doc.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                            <td>{{ doc.description or '-' }}</td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{{ url_for('view_document', document_id=doc.id) }}" class="btn btn-sm btn-info" target="_blank">
                                                        <i class="fas fa-eye"></i> عرض
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteDocModal{{ doc.id }}">
                                                        <i class="fas fa-trash-alt"></i> حذف
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- نوافذ منبثقة لتأكيد حذف المستندات -->
                        {% for doc in documents %}
                            <div class="modal fade" id="deleteDocModal{{ doc.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger text-white">
                                            <h5 class="modal-title">تأكيد حذف المستند</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>هل أنت متأكد من رغبتك في حذف هذا المستند؟</p>
                                            <p><strong>اسم المستند:</strong> {{ doc.file_name }}</p>
                                            <p class="text-danger"><strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <a href="{{ url_for('delete_document', document_id=doc.id) }}" class="btn btn-danger">تأكيد الحذف</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد مستندات مرفقة بهذه الإجازة.
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="mt-4">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> إذا كنت ترغب في رفع ملف موجود بدلاً من المسح الضوئي، يمكنك استخدام
                    <a href="{{ url_for('upload_document', leave_id=leave.id) }}" class="alert-link">صفحة رفع المستندات</a>.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const detectScannersBtn = document.getElementById('detect-scanners');
        const startScanBtn = document.getElementById('start-scan');
        const retryScanBtn = document.getElementById('retry-scan');
        const submitScanBtn = document.getElementById('submit-scan');
        const scannerContainer = document.getElementById('scanner-container');
        const scannerPlaceholder = document.getElementById('scanner-placeholder');
        const scannerPreview = document.getElementById('scanner-preview');
        const scannedImageInput = document.getElementById('scanned_image');
        const flashElement = document.querySelector('.flash-animation');
        const scannerSelect = document.getElementById('scanner-select');
        const openScannerUIBtn = document.getElementById('open-scanner-ui');
        const scannerList = document.getElementById('scanner-list');

        // البحث عن أجهزة المسح الضوئي تلقائيًا عند تحميل الصفحة
        setTimeout(function() {
            // استدعاء وظيفة البحث عن أجهزة المسح الضوئي تلقائيًا
            detectScannersBtn.click();
        }, 500);

        // التحقق من توفر مكتبات المسح الضوئي
        const wiaAvailable = {{ wia_available|tojson }};
        const directWiaAvailable = {{ direct_wia_available|tojson }};
        const wiaScanAvailable = {{ wia_scan_available|tojson }};

        if (!wiaAvailable) {
            // إذا كانت مكتبات المسح الضوئي غير متوفرة، عرض رسالة تحذير
            const warningMessage = document.createElement('div');
            warningMessage.className = 'alert alert-warning mt-3';

            // الحصول على رسالة الخطأ من الخادم إذا كانت متوفرة
            const errorMessage = {{ wia_error_message|tojson }};
            let messageText = '<i class="fas fa-exclamation-triangle me-2"></i> لم يتم العثور على أي جهاز مسح ضوئي. يرجى توصيل جهاز مسح ضوئي والمحاولة مرة أخرى.';

            if (errorMessage) {
                messageText += '<br><small class="text-muted mt-2">تفاصيل الخطأ: ' + errorMessage.replace(/\n/g, '<br>') + '</small>';
            }

            warningMessage.innerHTML = messageText;
            scannerContainer.prepend(warningMessage);
        } else {
            // إذا كانت إحدى المكتبات متوفرة، عرض رسالة إعلامية
            const infoMessage = document.createElement('div');
            infoMessage.className = 'alert alert-info mt-3';

            let methodText = '';
            if (directWiaAvailable) {
                methodText += 'WIA المباشر';
                if (wiaScanAvailable) {
                    methodText += ' و wia_scan';
                }
            } else if (wiaScanAvailable) {
                methodText += 'wia_scan';
            }

            infoMessage.innerHTML = '<i class="fas fa-info-circle me-2"></i> تم اكتشاف مكتبات المسح الضوئي: ' + methodText;
            scannerContainer.prepend(infoMessage);
        }

        // البحث عن أجهزة المسح الضوئي
        detectScannersBtn.addEventListener('click', async function() {
            try {
                // إظهار قائمة أجهزة المسح
                scannerList.classList.remove('d-none');

                // تفريغ القائمة أولاً
                while (scannerSelect.options.length > 1) {
                    scannerSelect.remove(1);
                }

                // عرض مؤشر التحميل
                scannerPlaceholder.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">جاري البحث عن أجهزة المسح الضوئي...</p></div>';

                if (wiaAvailable) {
                    // استخدام واجهة برمجة التطبيقات للحصول على قائمة أجهزة المسح الضوئي الحقيقية
                    fetch('/get_available_scanners_api')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                const scanners = data.scanners;

                                if (scanners.length === 0) {
                                    // إذا لم يتم العثور على أجهزة مسح ضوئي
                                    handleScannerNotFound();
                                    return;
                                }

                                // إضافة أجهزة المسح المتوفرة إلى القائمة
                                scanners.forEach(scanner => {
                                    const option = document.createElement('option');
                                    option.value = scanner.id;
                                    option.textContent = scanner.name;
                                    scannerSelect.appendChild(option);
                                });

                                // إعادة عرض المحتوى الأصلي
                                scannerPlaceholder.innerHTML = '<i class="fas fa-file-image scanner-icon"></i><h4>جاهز للمسح الضوئي</h4><p class="text-muted">تم العثور على ' + scanners.length + ' جهاز مسح ضوئي. يرجى اختيار الجهاز من القائمة.</p><div id="scanner-list" class="mt-3"><label for="scanner-select" class="form-label">اختر جهاز المسح الضوئي:</label><select id="scanner-select" class="form-select mb-2"><option value="">-- يرجى الاختيار --</option></select></div>';

                                // إعادة إضافة الخيارات إلى القائمة الجديدة
                                const newScannerSelect = document.getElementById('scanner-select');
                                scanners.forEach(scanner => {
                                    const option = document.createElement('option');
                                    option.value = scanner.id;
                                    option.textContent = scanner.name;
                                    newScannerSelect.appendChild(option);
                                });
                            } else {
                                // إذا حدث خطأ في الحصول على قائمة أجهزة المسح الضوئي
                                handleScannerNotFound();
                            }
                        })
                        .catch(error => {
                            console.error('خطأ في البحث عن أجهزة المسح الضوئي:', error);
                            handleScannerNotFound();
                        });
                } else {
                    // إذا كانت مكتبة wia_scan غير متوفرة، استخدام البيانات الوهمية
                    setTimeout(function() {
                        // إضافة أجهزة المسح المتوفرة (محاكاة)
                        const mockScanners = [
                            { id: 'scanner1', name: 'HP ScanJet Pro 2000 (محاكاة)' },
                            { id: 'scanner2', name: 'Canon PIXMA TS3520 (محاكاة)' },
                            { id: 'scanner3', name: 'Epson WorkForce ES-400 (محاكاة)' }
                        ];

                        mockScanners.forEach(scanner => {
                            const option = document.createElement('option');
                            option.value = scanner.id;
                            option.textContent = scanner.name;
                            scannerSelect.appendChild(option);
                        });

                        // إعادة عرض المحتوى الأصلي
                        scannerPlaceholder.innerHTML = '<i class="fas fa-file-image scanner-icon"></i><h4>جاهز للمسح الضوئي</h4><p class="text-muted">تم العثور على ' + mockScanners.length + ' جهاز مسح ضوئي (محاكاة). يرجى اختيار الجهاز من القائمة.</p><div id="scanner-list" class="mt-3"><label for="scanner-select" class="form-label">اختر جهاز المسح الضوئي:</label><select id="scanner-select" class="form-select mb-2"><option value="">-- يرجى الاختيار --</option></select></div>';

                        // إعادة إضافة الخيارات إلى القائمة الجديدة
                        const newScannerSelect = document.getElementById('scanner-select');
                        mockScanners.forEach(scanner => {
                            const option = document.createElement('option');
                            option.value = scanner.id;
                            option.textContent = scanner.name;
                            newScannerSelect.appendChild(option);
                        });
                    }, 1000);
                }
            } catch (error) {
                console.error('خطأ في البحث عن أجهزة المسح الضوئي:', error);
                handleScannerNotFound();
            }
        });

        // بدء المسح الضوئي
        startScanBtn.addEventListener('click', async function() {
            try {
                // التحقق من اختيار جهاز مسح ضوئي
                const selectedScanner = document.getElementById('scanner-select').value;

                if (!selectedScanner) {
                    // إذا لم يتم اختيار جهاز
                    alert('يرجى اختيار جهاز مسح ضوئي أولاً');
                    return;
                }

                // عرض مؤشر التحميل
                scannerPlaceholder.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">جاري المسح الضوئي...</p></div>';

                // تعطيل الأزرار أثناء المسح
                startScanBtn.disabled = true;
                detectScannersBtn.disabled = true;

                if (wiaAvailable) {
                    // استخدام واجهة برمجة التطبيقات للمسح الضوئي
                    // الحصول على حالة خيار استخدام وحدة التغذية التلقائية
                    const useAdf = document.getElementById('use_adf').checked;

                    fetch('/scan_with_device_api', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            scanner_id: selectedScanner,
                            dpi: 200,
                            brightness: 0,
                            contrast: 0,
                            mode: 'RGB',
                            use_adf: useAdf
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // عرض الصورة الممسوحة ضوئيًا
                            scannerPlaceholder.style.display = 'none';

                            // التحقق مما إذا كانت النتيجة تحتوي على صور متعددة
                            if (data.is_multi_page && data.image_count > 1) {
                                console.log(`تم مسح ${data.image_count} صفحة`);

                                // إنشاء عنصر div لعرض الصور
                                const imagesContainer = document.createElement('div');
                                imagesContainer.className = 'scanned-images-container';
                                imagesContainer.style.maxHeight = '500px';
                                imagesContainer.style.overflowY = 'auto';
                                imagesContainer.style.border = '1px solid #ddd';
                                imagesContainer.style.padding = '10px';
                                imagesContainer.style.marginBottom = '20px';

                                // إضافة عنوان
                                const title = document.createElement('h5');
                                title.textContent = `تم مسح ${data.image_count} صفحة`;
                                title.className = 'mb-3';
                                imagesContainer.appendChild(title);

                                // إضافة الصور
                                data.images.forEach(img => {
                                    const imgWrapper = document.createElement('div');
                                    imgWrapper.className = 'mb-3 p-2 border';

                                    const imgElement = document.createElement('img');
                                    imgElement.src = img.url;
                                    imgElement.className = 'img-fluid';
                                    imgElement.alt = `الصفحة ${img.index}`;

                                    const imgCaption = document.createElement('p');
                                    imgCaption.className = 'text-center mt-2';
                                    imgCaption.textContent = `الصفحة ${img.index}`;

                                    imgWrapper.appendChild(imgElement);
                                    imgWrapper.appendChild(imgCaption);
                                    imagesContainer.appendChild(imgWrapper);
                                });

                                // إضافة عنصر div إلى scannerPreview
                                scannerPreview.innerHTML = '';
                                scannerPreview.appendChild(imagesContainer);

                                // إضافة الصورة المدمجة أيضاً
                                const combinedImgWrapper = document.createElement('div');
                                combinedImgWrapper.className = 'mt-4';

                                const combinedTitle = document.createElement('h5');
                                combinedTitle.textContent = 'الصورة المدمجة';
                                combinedTitle.className = 'mb-3';
                                combinedImgWrapper.appendChild(combinedTitle);

                                const combinedImg = document.createElement('img');
                                combinedImg.src = data.combined_image.url;
                                combinedImg.className = 'img-fluid border';
                                combinedImg.alt = 'الصورة المدمجة';
                                combinedImgWrapper.appendChild(combinedImg);

                                scannerPreview.appendChild(combinedImgWrapper);
                                scannerPreview.style.display = 'block';

                                // تخزين بيانات الصور في حقل مخفي
                                scannedImageInput.value = JSON.stringify({
                                    type: 'multi_page',
                                    images: data.images,
                                    combined_image: data.combined_image.url,
                                    group_id: data.group_id
                                });

                                console.log("تم تخزين بيانات الصور المتعددة في النموذج");
                            } else {
                                // حالة صورة واحدة
                                // استخدام URL الصورة إذا كان متوفراً، وإلا استخدام بيانات الصورة مباشرة
                                if (data.image_url) {
                                    scannerPreview.innerHTML = `<img src="${data.image_url}" class="img-fluid" alt="صورة ممسوحة ضوئيًا">`;
                                    console.log("استخدام URL الصورة: " + data.image_url);

                                    // تخزين URL الصورة في حقل مخفي
                                    scannedImageInput.value = data.image_url;
                                } else {
                                    scannerPreview.innerHTML = `<img src="${data.image_data}" class="img-fluid" alt="صورة ممسوحة ضوئيًا">`;
                                    console.log("استخدام بيانات الصورة المباشرة");

                                    // تخزين بيانات الصورة في حقل مخفي
                                    scannedImageInput.value = data.image_data;
                                }
                                scannerPreview.style.display = 'block';
                            }

                            // تم تعيين قيمة الصورة في النموذج بالفعل في الخطوات السابقة
                            console.log("تم تعيين قيمة الصورة في النموذج بنجاح");

                            // تفعيل زر الحفظ
                            submitScanBtn.disabled = false;
                            retryScanBtn.disabled = false;

                            // تأثير الفلاش
                            flashElement.classList.add('flashing');
                            setTimeout(() => {
                                flashElement.classList.remove('flashing');
                            }, 500);

                            // تعيين وصف افتراضي للمستند إذا لم يكن موجودًا
                            const description = document.getElementById('description').value || 'مستند ممسوح ضوئياً';
                            document.getElementById('description').value = description;

                            // التحقق من وجود بيانات الصورة
                            if (!scannedImageInput.value) {
                                console.error("لم يتم تعيين بيانات الصورة بعد!");
                                alert("حدث خطأ في معالجة الصورة. يرجى المحاولة مرة أخرى.");
                                return;
                            }

                            // إظهار رسالة للمستخدم
                            const processingMessage = document.createElement('div');
                            processingMessage.className = 'alert alert-success mt-3';
                            processingMessage.innerHTML = '<i class="fas fa-check-circle me-2"></i> تم المسح الضوئي بنجاح. اضغط على زر "حفظ" لحفظ المستند.';
                            document.getElementById('scanner-container').appendChild(processingMessage);

                            // تفعيل زر الحفظ
                            submitScanBtn.disabled = false;
                        } else {
                            // إذا حدث خطأ في المسح الضوئي
                            alert('حدث خطأ في المسح الضوئي: ' + (data.error || 'خطأ غير معروف'));

                            // إعادة تفعيل الأزرار
                            startScanBtn.disabled = false;
                            detectScannersBtn.disabled = false;

                            // إعادة عرض المحتوى الأصلي
                            scannerPlaceholder.innerHTML = '<i class="fas fa-file-image scanner-icon"></i><h4>جاهز للمسح الضوئي</h4><p class="text-muted">يرجى المحاولة مرة أخرى.</p><div id="scanner-list" class="mt-3"><label for="scanner-select" class="form-label">اختر جهاز المسح الضوئي:</label><select id="scanner-select" class="form-select mb-2"><option value="">-- يرجى الاختيار --</option></select></div>';
                        }
                    })
                    .catch(error => {
                        console.error('خطأ في المسح الضوئي:', error);
                        alert('حدث خطأ في المسح الضوئي. يرجى المحاولة مرة أخرى.');

                        // إعادة تفعيل الأزرار
                        startScanBtn.disabled = false;
                        detectScannersBtn.disabled = false;
                    });
                } else {
                    // إذا كانت مكتبة wia_scan غير متوفرة، استخدام المحاكاة
                    setTimeout(function() {
                        // إنشاء صورة وهمية للمسح الضوئي
                        const mockScanImage = '/static/img/sample_scan.jpg'; // استبدل هذا بمسار صورة موجودة في مشروعك

                        // عرض الصورة الممسوحة ضوئيًا
                        scannerPreview.src = mockScanImage;
                        scannerPreview.style.display = 'block';
                        scannerPlaceholder.style.display = 'none';

                        // تعيين قيمة الصورة في النموذج (بيانات base64 وهمية)
                        const mockImageData = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAMCAgMCAgMDAwMEAwMEBQgFBQQEBQoHBwYIDAoMDAsKCwsNDhIQDQ4RDgsLEBYQERMUFRUVDA8XGBYUGBIUFRT/2wBDAQMEBAUEBQkFBQkUDQsNFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBT/wAARCAABAAEDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9/KKKKAP/2Q==';

                        // تقليل حجم البيانات إذا كان تنسيق الملف PDF
                        const fileFormat = document.querySelector('input[name="file_format"]:checked').value;
                        if (fileFormat === 'pdf') {
                            // في حالة المحاكاة، نستخدم البيانات الوهمية مباشرة
                            scannedImageInput.value = mockImageData;
                        } else {
                            scannedImageInput.value = mockImageData;
                        }

                        // تفعيل زر الحفظ
                        submitScanBtn.disabled = false;
                        retryScanBtn.disabled = false;

                        // تأثير الفلاش
                        flashElement.classList.add('flashing');
                        setTimeout(() => {
                            flashElement.classList.remove('flashing');
                        }, 500);

                        // تعيين وصف افتراضي للمستند إذا لم يكن موجودًا
                        const description = document.getElementById('description').value || 'مستند ممسوح ضوئياً';
                        document.getElementById('description').value = description;

                        // التحقق من وجود بيانات الصورة
                        if (!scannedImageInput.value) {
                            console.error("لم يتم تعيين بيانات الصورة بعد!");
                            alert("حدث خطأ في معالجة الصورة. يرجى المحاولة مرة أخرى.");
                            return;
                        }

                        // إظهار رسالة للمستخدم
                        const processingMessage = document.createElement('div');
                        processingMessage.className = 'alert alert-success mt-3';
                        processingMessage.innerHTML = '<i class="fas fa-check-circle me-2"></i> تم المسح الضوئي بنجاح. اضغط على زر "حفظ" لحفظ المستند.';
                        document.getElementById('scanner-container').appendChild(processingMessage);

                        // تفعيل زر الحفظ
                        submitScanBtn.disabled = false;
                    }, 2000);
                }
            } catch (error) {
                console.error('خطأ في بدء المسح الضوئي:', error);
                alert('حدث خطأ في بدء المسح الضوئي. يرجى المحاولة مرة أخرى.');

                // إعادة تفعيل الأزرار
                startScanBtn.disabled = false;
                detectScannersBtn.disabled = false;
            }
        });

        // وظيفة للتعامل مع حالة عدم العثور على أجهزة مسح ضوئي
        function handleScannerNotFound() {
            alert('لم يتم العثور على أي جهاز مسح ضوئي. يرجى توصيل جهاز مسح ضوئي والمحاولة مرة أخرى.');

            // إعادة عرض المحتوى الأصلي
            scannerPlaceholder.innerHTML = '<i class="fas fa-file-image scanner-icon"></i><h4>جاهز للمسح الضوئي</h4><p class="text-muted">لم يتم العثور على أجهزة مسح ضوئي. يرجى توصيل جهاز مسح ضوئي والضغط على زر "البحث عن أجهزة المسح".</p><div id="scanner-list" class="mt-3 d-none"><label for="scanner-select" class="form-label">اختر جهاز المسح الضوئي:</label><select id="scanner-select" class="form-select mb-2"><option value="">-- يرجى الاختيار --</option></select></div>';

            // إعادة تفعيل الأزرار
            startScanBtn.disabled = false;
            detectScannersBtn.disabled = false;
        }



        // إعادة المحاولة
        retryScanBtn.addEventListener('click', function() {
            // إعادة تعيين الحالة
            scannerPreview.style.display = 'none';
            scannerPlaceholder.innerHTML = '<i class="fas fa-file-image scanner-icon"></i><h4>جاهز للمسح الضوئي</h4><p class="text-muted">قم بتوصيل الماسح الضوئي واضغط على زر "البحث عن أجهزة المسح"</p><div id="scanner-list" class="mt-3 d-none"><label for="scanner-select" class="form-label">اختر جهاز المسح الضوئي:</label><select id="scanner-select" class="form-select mb-2"><option value="">-- يرجى الاختيار --</option></select></div>';
            scannerPlaceholder.style.display = 'flex';
            scannedImageInput.value = '';

            // إعادة تعيين الأزرار
            startScanBtn.disabled = false;
            detectScannersBtn.disabled = false;
            submitScanBtn.disabled = true;

            // إعادة إخفاء قائمة أجهزة المسح الضوئي
            scannerList.classList.add('d-none');
        });
    });
</script>
{% endblock %}
