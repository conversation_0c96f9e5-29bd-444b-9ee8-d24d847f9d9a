// تنسيق حقول التاريخ بالعربية (RTL) - حل مخصص
// تم تعطيل هذا الملف مؤقتًا لتجنب التعارض مع الكود المباشر في add_leave.html
/* (function() {
    // تكوين flatpickr للغة العربية واتجاه RTL
    const flatpickrConfig = {
        locale: 'ar',
        dateFormat: 'Y-m-d', // تنسيق التاريخ المخزن
        allowInput: false,
        disableMobile: true, // تعطيل واجهة التاريخ الأصلية للجوال
        position: 'auto',
        static: false, // تغيير إلى false لحل مشكلة عدم ظهور التقويم
        monthSelectorType: 'dropdown',
        theme: 'material_blue',
        appendTo: document.body, // إضافة التقويم إلى body لتجنب مشاكل العرض
        // استخدام دالة مخصصة لتنسيق التاريخ
        formatDate: function(date, format) {
            if (format === 'Y-m-d') {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }

            // تنسيق العرض المخصص (يوم-شهر-سنة)
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            return `${day}-${month}-${year}`;
        }
    };

    // دالة لتطبيق flatpickr على حقول التاريخ
    function applyFlatpickrToDateInputs() {
        // تحديد جميع حقول التاريخ
        const dateInputs = document.querySelectorAll('input[type="date"]');

        dateInputs.forEach(input => {
            // تغيير نوع الحقل من date إلى text
            input.type = 'text';

            // إضافة سمات للحقول لضمان عرض التاريخ بشكل صحيح
            input.setAttribute('lang', 'ar');
            input.setAttribute('dir', 'rtl');

            // إضافة فئة CSS مخصصة
            input.classList.add('rtl-date-input');

            // تعيين الاتجاه بشكل مباشر
            input.style.direction = 'rtl';
            input.style.textAlign = 'right';

            // الحصول على القيمة الحالية
            const currentValue = input.value;

            // تكوين خاص لهذا الحقل
            const customConfig = {
                ...flatpickrConfig,
                defaultDate: currentValue || null
            };

            // إذا كان الحقل للقراءة فقط
            if (input.hasAttribute('readonly')) {
                customConfig.clickOpens = false;
                customConfig.allowInput = false;
                input.setAttribute('disabled', 'disabled');
            }

            // تطبيق flatpickr مع الإعدادات المخصصة
            const fp = flatpickr(input, customConfig);

            // تأكد من أن التقويم يعمل
            console.log('تم تطبيق flatpickr على الحقل:', input.id || 'حقل بدون معرف');

            // إنشاء عنصر div لعرض التاريخ بالتنسيق العربي
            const displayDiv = document.createElement('div');
            displayDiv.className = 'date-display rtl-date-input';
            displayDiv.style.direction = 'rtl';
            displayDiv.style.textAlign = 'right';
            displayDiv.style.padding = '0.375rem 0.75rem';
            displayDiv.style.border = '2px solid #d1d9ff';
            displayDiv.style.borderRadius = '0.25rem';
            displayDiv.style.backgroundColor = '#f8f9ff';
            displayDiv.style.cursor = 'pointer';

            // إضافة أيقونة التقويم
            const calendarIcon = document.createElement('i');
            calendarIcon.className = 'fas fa-calendar-alt';
            calendarIcon.style.marginLeft = '8px';
            calendarIcon.style.color = '#4e73df';

            // إضافة عنصر span لعرض التاريخ
            const dateSpan = document.createElement('span');
            dateSpan.className = 'date-text';

            // تحديث نص التاريخ
            function updateDisplayText() {
                if (input.value) {
                    const dateParts = input.value.split('-');
                    if (dateParts.length === 3) {
                        const [year, month, day] = dateParts;
                        dateSpan.textContent = `${day}-${month}-${year}`;
                    } else {
                        dateSpan.textContent = input.value;
                    }
                } else {
                    dateSpan.textContent = input.getAttribute('placeholder') || 'اختر تاريخ';
                }
            }

            // تحديث النص الأولي
            updateDisplayText();

            // إضافة العناصر إلى div العرض
            displayDiv.appendChild(calendarIcon);
            displayDiv.appendChild(dateSpan);

            // إخفاء الحقل الأصلي
            input.style.display = 'none';

            // إضافة div العرض بعد الحقل
            input.parentNode.insertBefore(displayDiv, input.nextSibling);

            // فتح التقويم عند النقر على div العرض
            displayDiv.addEventListener('click', function() {
                if (!input.hasAttribute('readonly') && !input.hasAttribute('disabled')) {
                    fp.open();
                    console.log('تم فتح التقويم');
                }
            });

            // إضافة زر التقويم
            const calendarButton = document.createElement('button');
            calendarButton.type = 'button';
            calendarButton.className = 'btn btn-primary calendar-button';
            calendarButton.innerHTML = '<i class="fas fa-calendar-alt"></i>';
            calendarButton.style.position = 'absolute';
            calendarButton.style.left = '0';
            calendarButton.style.top = '0';
            calendarButton.style.height = '100%';
            calendarButton.style.borderTopLeftRadius = '0.25rem';
            calendarButton.style.borderBottomLeftRadius = '0.25rem';
            calendarButton.style.borderTopRightRadius = '0';
            calendarButton.style.borderBottomRightRadius = '0';
            calendarButton.style.zIndex = '1';

            // فتح التقويم عند النقر على زر التقويم
            calendarButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                if (!input.hasAttribute('readonly') && !input.hasAttribute('disabled')) {
                    fp.open();
                    console.log('تم فتح التقويم من الزر');
                }
            });

            // إضافة زر التقويم إلى الحاوية
            const inputContainer = input.parentNode;
            inputContainer.style.position = 'relative';
            inputContainer.appendChild(calendarButton);

            // تحديث النص عند تغيير التاريخ
            fp.config.onChange.push(function(selectedDates, dateStr) {
                updateDisplayText();
            });
        });
    }

    // تطبيق flatpickr عند تحميل DOM
    document.addEventListener('DOMContentLoaded', function() {
        // تأخير قصير للتأكد من تحميل flatpickr بالكامل
        setTimeout(applyFlatpickrToDateInputs, 100);
    });

    // تطبيق flatpickr بعد تحميل الصفحة بالكامل
    window.addEventListener('load', function() {
        // تأخير قصير للتأكد من تحميل flatpickr بالكامل
        setTimeout(applyFlatpickrToDateInputs, 100);
    });
}); */
