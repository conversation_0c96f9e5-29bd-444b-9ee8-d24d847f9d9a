@echo off
echo 🔄 جاري حذف الملفات القديمة...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist

echo 🔧 بدء تحويل app.py إلى exe باستخدام ملف المواصفات المحدث...
pyinstaller app.spec

echo 📁 نسخ الملفات الضرورية إلى مجلد dist...
if not exist dist\instance mkdir dist\instance
if not exist dist\uploads mkdir dist\uploads
if not exist dist\uploads\documents mkdir dist\uploads\documents
if not exist dist\uploads\scans mkdir dist\uploads\scans
if not exist dist\backups mkdir dist\backups

echo 🔧 نسخ قاعدة البيانات إذا كانت موجودة...
if exist instance\ajazat.db copy instance\ajazat.db dist\instance\
if exist instance\employees.db copy instance\employees.db dist\instance\

echo ✅ تم إنشاء الملف التنفيذي! الملف في مجلد dist\app.exe
echo 📝 ملاحظة: تم تفعيل وضع التشخيص لرؤية رسائل الخطأ
pause
