from app import app
from models import db, LeaveType

def update_database_schema():
    with app.app_context():
        try:
            # تحديث هيكل قاعدة البيانات
            db.create_all()
            print("تم تحديث هيكل قاعدة البيانات بنجاح")

            # التحقق من وجود أنواع الإجازات الجديدة
            leave_types = [
                {'name': 'إجازة اعتيادية', 'days_allowed': 30},
                {'name': 'إجازة مرضية', 'days_allowed': 30},
                {'name': 'إجازة المعيل', 'days_allowed': 15},
                {'name': 'إجازة خمس سنوات', 'days_allowed': 60},
                {'name': 'إجازة بدون راتب', 'days_allowed': None},
                {'name': 'إجازة ما قبل الوضع', 'days_allowed': 21, 'description': '21 يوم'},
                {'name': 'إجازة ما بعد الوضع', 'days_allowed': 51, 'description': '51 يوم'},
                {'name': 'إجازة أمومة', 'days_allowed': 365, 'description': 'سنة كاملة'},
                {'name': 'إجازة دراسية', 'days_allowed': 180},
                {'name': 'إجازة طويلة', 'days_allowed': 365}
            ]

            # الحصول على أنواع الإجازات الحالية
            existing_leave_types = {lt.name: lt for lt in LeaveType.query.all()}

            # إضافة أنواع الإجازات الجديدة فقط
            for lt_data in leave_types:
                name = lt_data['name']
                if name not in existing_leave_types:
                    leave_type = LeaveType(**lt_data)
                    db.session.add(leave_type)
                    print(f"تمت إضافة نوع الإجازة: {name}")
                else:
                    # تحديث البيانات الموجودة
                    lt = existing_leave_types[name]
                    lt.days_allowed = lt_data['days_allowed']
                    if 'description' in lt_data:
                        lt.description = lt_data['description']
                    print(f"تم تحديث نوع الإجازة: {name}")

            db.session.commit()
            print("تم تحديث أنواع الإجازات بنجاح")

        except Exception as e:
            db.session.rollback()
            print(f"حدث خطأ أثناء تحديث قاعدة البيانات: {str(e)}")

if __name__ == "__main__":
    update_database_schema()
