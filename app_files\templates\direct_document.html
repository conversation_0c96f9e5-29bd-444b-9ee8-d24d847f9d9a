<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض المستند - {{ document_name }}</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        
        .document-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .document-header {
            background-color: #0d47a1;
            color: white;
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 100;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .document-header h3 {
            margin: 0;
            font-size: 18px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 70%;
        }
        
        .document-actions {
            display: flex;
            gap: 10px;
        }
        
        .document-actions a {
            color: white;
            text-decoration: none;
            padding: 5px 10px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .document-actions a:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        
        .document-content {
            flex: 1;
            overflow: hidden;
            position: relative;
        }
        
        /* تحسين عرض ملفات PDF */
        .pdf-container {
            width: 100%;
            height: 100%;
            position: relative;
        }
        
        embed {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        /* تحسين عرض الصور */
        .image-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #000;
        }
        
        img {
            max-width: 100%;
            max-height: 100%;
            display: block;
            object-fit: contain;
        }
    </style>
</head>
<body>
    <div class="document-container">
        <div class="document-header">
            <h3>{{ document_name }}</h3>
            <div class="document-actions">
                <a href="{{ url_for('employee_leaves', employee_id=employee_id) }}">رجوع</a>
            </div>
        </div>
        <div class="document-content">
            {% if file_ext == 'pdf' %}
                <div class="pdf-container">
                    <embed src="{{ url_for('static', filename='documents/' + filename) }}" type="application/pdf" width="100%" height="100%">
                </div>
            {% elif file_ext in ['jpg', 'jpeg', 'png'] %}
                <div class="image-container">
                    <img src="{{ url_for('static', filename='documents/' + filename) }}" alt="{{ document_name }}">
                </div>
            {% else %}
                <div style="padding: 20px; text-align: center;">
                    <p>لا يمكن عرض هذا النوع من الملفات مباشرة. يرجى تنزيل الملف.</p>
                    <a href="{{ url_for('static', filename='documents/' + filename) }}" download style="display: inline-block; padding: 10px 20px; background-color: #0d47a1; color: white; text-decoration: none; border-radius: 4px; margin-top: 10px;">تنزيل الملف</a>
                </div>
            {% endif %}
        </div>
    </div>
</body>
</html>
