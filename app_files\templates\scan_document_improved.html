{% extends 'base.html' %}

{% block title %}مسح مستند{% endblock %}

{% block styles %}
<style>
    .card {
        border: 1px solid #0d6efd;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .card-header {
        background-color: #0d6efd;
        color: white;
        font-weight: bold;
    }
    .scanner-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid #dee2e6;
    }
    .btn-scanner {
        background-color: #0d6efd;
        color: white;
        font-weight: bold;
        padding: 10px 20px;
    }
    .btn-save {
        background-color: #198754;
        color: white;
        font-weight: bold;
        padding: 10px 20px;
    }
    .alert-pdf {
        background-color: #f8d7da;
        border-color: #f5c2c7;
        color: #842029;
    }
    .table-documents {
        background-color: white;
    }
    .table-documents th {
        background-color: #0d6efd;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">مسح مستند لإجازة {{ employee.full_name }}</h5>
                        <a href="{{ url_for('employee_leaves', employee_id=leave.employee_id) }}" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-right"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div id="alerts-container"></div>

                    <!-- معلومات الموظف والإجازة -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title mb-3">معلومات الموظف</h5>
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>الاسم:</span>
                                            <strong>{{ employee.full_name }}</strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>الرقم الوظيفي:</span>
                                            <strong>{{ employee.employee_number or '-' }}</strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>المسمى الوظيفي:</span>
                                            <strong>{{ employee.job_title }}</strong>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title mb-3">معلومات الإجازة</h5>
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>نوع الإجازة:</span>
                                            <strong>{{ leave.leave_type.name }}</strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>تاريخ البداية:</span>
                                            <strong>{{ leave.start_date.strftime('%Y-%m-%d') }}</strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>تاريخ النهاية:</span>
                                            <strong>{{ leave.end_date.strftime('%Y-%m-%d') }}</strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>المدة:</span>
                                            <strong>{{ leave.days_count }} يوم</strong>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قسم المسح الضوئي -->
                    <div class="scanner-section">
                        <!-- قسم اختيار الماسح الضوئي -->
                        <div class="mb-4">
                            <h5 class="text-primary">اختيار جهاز المسح الضوئي</h5>
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="input-group">
                                        <select id="scanner-select" class="form-select form-select-lg">
                                            <option value="">جاري تحميل قائمة أجهزة المسح الضوئي...</option>
                                        </select>
                                        <button id="refresh-scanners" class="btn btn-outline-primary" type="button">
                                            <i class="fas fa-sync-alt"></i> تحديث
                                        </button>
                                    </div>
                                    <div id="scanners-loading" class="mt-2">
                                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <span class="ms-2">جاري البحث عن أجهزة المسح الضوئي...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- قسم إعدادات المسح الضوئي -->
                        <div class="mb-4">
                            <h5 class="text-primary">إعدادات المسح الضوئي</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="dpi-select" class="form-label">الدقة (DPI)</label>
                                        <select id="dpi-select" class="form-select">
                                            <option value="100">100 DPI</option>
                                            <option value="200">200 DPI</option>
                                            <option value="300" selected>300 DPI</option>
                                            <option value="600">600 DPI</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3 form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="use-adf" checked>
                                        <label class="form-check-label" for="use-adf">استخدام التغذية التلقائية (ADF)</label>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات PDF فقط -->
                            <div class="alert alert-pdf">
                                <i class="fas fa-file-pdf"></i> <strong>ملاحظة:</strong> سيتم مسح المستند وحفظه بصيغة PDF فقط. يمكنك إضافة صفحات متعددة للمستند.
                            </div>

                            <!-- معلومات الصفحات الممسوحة -->
                            <div class="mb-3">
                                <div class="d-flex align-items-center">
                                    <span>عدد الصفحات الممسوحة: <span id="scanned-pages-count" class="badge bg-primary">0</span></span>
                                    <button id="add-more-pages-button" class="btn btn-sm btn-outline-primary ms-3">
                                        <i class="fas fa-plus"></i> إضافة صفحات أخرى
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- زر بدء المسح الضوئي -->
                        <div class="mb-4">
                            <button id="scan-button" class="btn btn-scanner btn-lg" disabled>
                                <i class="fas fa-scanner me-2"></i> بدء المسح الضوئي
                            </button>
                            <div id="scanning-progress" class="mt-2 alert alert-info" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                        <span class="visually-hidden">جاري المسح الضوئي...</span>
                                    </div>
                                    <span>جاري المسح الضوئي، يرجى الانتظار حتى اكتمال العملية...</span>
                                </div>
                            </div>
                        </div>

                        <!-- قسم حفظ المستند -->
                        <div class="mb-4">
                            <h5 class="text-primary">حفظ المستند</h5>
                            <div class="mb-3">
                                <label for="document-description" class="form-label">وصف المستند</label>
                                <input type="text" class="form-control" id="document-description" placeholder="أدخل وصفاً للمستند">
                            </div>
                            <button id="save-document-button" class="btn btn-save btn-lg" disabled data-leave-id="{{ leave_id }}">
                                <i class="fas fa-save me-2"></i> حفظ المستند
                            </button>
                            <div id="saving-progress" class="mt-2 alert alert-success" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <div class="spinner-border spinner-border-sm text-success me-2" role="status">
                                        <span class="visually-hidden">جاري الحفظ...</span>
                                    </div>
                                    <span>جاري حفظ المستند، يرجى الانتظار حتى اكتمال العملية...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قسم المستندات الحالية -->
                    <div class="mb-4">
                        <h5 class="text-primary">المستندات الحالية</h5>
                        {% if documents %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover table-documents">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الوصف</th>
                                        <th>نوع الملف</th>
                                        <th>تاريخ الرفع</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for document in documents %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>{{ document.description or 'بدون وصف' }}</td>
                                        <td>{{ document.file_type }}</td>
                                        <td>{{ document.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ url_for('view_document', document_id=document.id) }}" class="btn btn-sm btn-primary" target="_blank">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                                <a href="{{ url_for('download_document', document_id=document.id) }}" class="btn btn-sm btn-secondary">
                                                    <i class="fas fa-download"></i> تنزيل
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="if(confirm('هل أنت متأكد من حذف هذا المستند؟')) window.location.href='{{ url_for('delete_document', document_id=document.id) }}'">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            لا توجد مستندات مرفقة بهذه الإجازة.
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/scanner_improved.js') }}"></script>
{% endblock %}
