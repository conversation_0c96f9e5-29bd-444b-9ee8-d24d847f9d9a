<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض المستند</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
            background-color: #f0f0f0;
        }

        .document-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .document-header {
            background-color: #0d47a1;
            color: white;
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 100;
        }

        .document-header h3 {
            margin: 0;
        }

        .document-header a {
            color: white;
            text-decoration: none;
            margin-left: 15px;
            padding: 5px 10px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        .document-content {
            flex: 1;
            overflow: hidden;
            position: relative;
        }

        /* تحسين عرض ملفات PDF */
        .pdf-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        object {
            width: 100%;
            height: 100%;
            display: block;
        }

        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* تحسين عرض الصور */
        .image-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #000;
        }

        img {
            max-width: 100%;
            max-height: 100%;
            display: block;
            object-fit: contain;
        }
    </style>
</head>
<body>
    <div class="document-container">
        <div class="document-header">
            <h3>{{ document_name }}</h3>
            <div>
                <a href="{{ download_url }}" download>تنزيل</a>
                <a href="{{ back_url }}">رجوع</a>
            </div>
        </div>
        <div class="document-content">
            {% if document_type == 'pdf' %}
                <div class="pdf-container">
                    <!-- استخدام عنصر object بدلاً من iframe لعرض ملفات PDF -->
                    <object data="{{ document_url }}" type="application/pdf">
                        <p>يبدو أن متصفحك لا يدعم عرض ملفات PDF. يمكنك <a href="{{ download_url }}" download>تنزيل الملف</a> بدلاً من ذلك.</p>
                    </object>
                </div>
            {% elif document_type in ['jpg', 'jpeg', 'png'] %}
                <div class="image-container">
                    <img src="{{ document_url }}" alt="{{ document_name }}">
                </div>
            {% else %}
                <div style="padding: 20px; text-align: center;">
                    <p>لا يمكن عرض هذا النوع من الملفات مباشرة. يرجى تنزيل الملف.</p>
                    <a href="{{ download_url }}" download style="display: inline-block; padding: 10px 20px; background-color: #0d47a1; color: white; text-decoration: none; border-radius: 4px; margin-top: 10px;">تنزيل الملف</a>
                </div>
            {% endif %}
        </div>
    </div>
</body>
</html>
