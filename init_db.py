from app import app, db
from models import Employee

def init_database():
    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        
        # إضافة موظف تجريبي للاختبار
        test_employee = Employee(
            full_name='موظف تجريبي',
            job_title='مبرمج',
            work_location='المكتب الرئيسي',
            leave_balance=30
        )
        
        try:
            db.session.add(test_employee)
            db.session.commit()
            print("تم إضافة موظف تجريبي بنجاح")
        except Exception as e:
            print("خطأ:", str(e))
            db.session.rollback()

if __name__ == '__main__':
    init_database()


