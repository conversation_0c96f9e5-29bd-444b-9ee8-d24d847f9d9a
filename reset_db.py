from app import app, db
from models import Employee, LeaveType
import os

def reset_database():
    with app.app_context():
        # حذف كل الجداول وإعادة إنشائها
        db.drop_all()
        db.create_all()
        print("تم إعادة إنشاء قاعدة البيانات")

        # إنشاء حساب المدير
        admin = Employee(
            username='admin',
            full_name='مدير النظام',
            work_location='المقر الرئيسي',
            job_title='مدير النظام',
            is_admin=True,
            leave_balance=36,
            is_active=True
        )
        admin.set_password('admin')

        # إضافة أنواع الإجازات الافتراضية
        leave_types = [
            LeaveType(name='اعتيادية', description='إجازة اعتيادية'),
            LeaveType(name='مرضية', description='إجازة مرضية'),
            LeaveType(name='أمومة', days_allowed=365, description='إجازة أمومة'),
            LeaveType(name='دراسية', description='إجازة دراسية'),
            LeaveType(name='طويلة', description='إجازة طويلة')
        ]

        # إضافة البيانات الافتراضية إلى قاعدة البيانات
        db.session.add(admin)
        for leave_type in leave_types:
            db.session.add(leave_type)

        db.session.commit()

        print("تم إنشاء حساب المدير")
        print("اسم المستخدم: admin")
        print("كلمة المرور: admin")

if __name__ == '__main__':
    reset_database()

