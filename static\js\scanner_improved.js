/**
 * ملف JavaScript محسن للماسح الضوئي
 * يستخدم نفس الكود الناجح من صفحة الاختبار
 */

// متغيرات عامة
let scannersList = [];
let selectedScannerId = null;
let selectedScannerSource = 'pdf_only';
let debugMode = true;

// تحديث قائمة أجهزة المسح الضوئي
function updateScannersList() {
    // إظهار مؤشر التحميل
    $('#scanners-loading').show();
    $('#scanners-list').hide();

    if (debugMode) {
        console.log("جاري تحديث قائمة أجهزة المسح الضوئي...");
    }

    // إضافة رسالة تشخيصية في واجهة المستخدم
    showAlert('info', 'جاري البحث عن أجهزة المسح الضوئي...');

    // الحصول على قائمة أجهزة المسح الضوئي
    $.ajax({
        url: '/get_available_scanners_api',
        type: 'GET',
        success: function(response) {
            // إخفاء مؤشر التحميل
            $('#scanners-loading').hide();
            $('#scanners-list').show();

            if (debugMode) {
                console.log("استجابة الخادم:", response);
            }

            if (response.success) {
                scannersList = response.scanners;

                if (debugMode) {
                    console.log("تم استلام قائمة الماسحات الضوئية:", scannersList);
                    console.log("عدد الماسحات الضوئية:", scannersList.length);
                }

                // تفريغ القائمة
                $('#scanner-select').empty();
                $('#scanner-select').append(
                    $('<option></option>')
                        .val('')
                        .text('-- يرجى الاختيار --')
                        .attr('disabled', true)
                        .attr('selected', true)
                );

                // إضافة الخيارات
                if (scannersList && scannersList.length > 0) {
                    // إضافة رسالة تشخيصية في واجهة المستخدم
                    showAlert('success', `تم العثور على ${scannersList.length} جهاز مسح ضوئي`);

                    // إضافة كل ماسح ضوئي إلى القائمة
                    scannersList.forEach(function(scanner, index) {
                        let source = scanner.source || 'unknown';
                        let sourceLabel = '';

                        if (source === 'vuescan') {
                            sourceLabel = ' (VueScan)';
                        } else if (source === 'pdf_only') {
                            sourceLabel = ' (PDF)';
                        } else if (source === 'direct_wia') {
                            sourceLabel = ' (WIA)';
                        } else if (source === 'wia') {
                            sourceLabel = ' (WIA)';
                        } else if (source === 'detect_scanners') {
                            sourceLabel = ' (Auto)';
                        } else if (source === 'wmi') {
                            sourceLabel = ' (WMI)';
                        } else if (source === 'device_manager') {
                            sourceLabel = ' (Device Manager)';
                        } else if (source === 'twain') {
                            sourceLabel = ' (TWAIN)';
                        } else if (source === 'manual') {
                            sourceLabel = ' (Manual)';
                        }

                        if (debugMode) {
                            console.log(`إضافة ماسح ضوئي: ${scanner.name}, المصدر: ${source}, المعرف: ${scanner.id}`);
                        }

                        // إضافة الماسح الضوئي إلى القائمة
                        try {
                            $('#scanner-select').append(
                                $('<option></option>')
                                    .val(index)
                                    .text(scanner.name + sourceLabel)
                                    .attr('data-source', source)
                                    .attr('data-id', scanner.id)
                            );

                            if (debugMode) {
                                console.log(`تمت إضافة الماسح الضوئي ${scanner.name} إلى القائمة`);
                            }
                        } catch (error) {
                            console.error(`خطأ في إضافة الماسح الضوئي ${scanner.name} إلى القائمة:`, error);
                        }
                    });

                    // تفعيل زر المسح الضوئي
                    $('#scan-button').prop('disabled', false);

                    // إضافة رسالة تشخيصية في واجهة المستخدم
                    let scannersNames = scannersList.map(s => s.name).join(', ');
                    showAlert('info', `الماسحات الضوئية المتوفرة: ${scannersNames}`);
                } else {
                    if (debugMode) {
                        console.log("لا توجد ماسحات ضوئية متوفرة");
                    }

                    // إضافة رسالة تشخيصية في واجهة المستخدم
                    showAlert('warning', 'لم يتم العثور على أي جهاز مسح ضوئي');

                    $('#scanner-select').append(
                        $('<option></option>')
                            .val('')
                            .text('لا توجد أجهزة مسح ضوئي متوفرة')
                    );

                    // تعطيل زر المسح الضوئي
                    $('#scan-button').prop('disabled', true);
                }
            } else {
                console.error("خطأ في استجابة الخادم:", response.error);
                showAlert('danger', response.error || 'حدث خطأ أثناء الحصول على قائمة أجهزة المسح الضوئي');
            }
        },
        error: function(xhr, status, error) {
            // إخفاء مؤشر التحميل
            $('#scanners-loading').hide();

            console.error("خطأ في الاتصال بالخادم:", error);
            console.error("الحالة:", status);
            console.error("استجابة الخادم:", xhr.responseText);

            showAlert('danger', 'حدث خطأ أثناء الاتصال بالخادم: ' + error);
        }
    });
}

// تحديث معلومات الماسح الضوئي المحدد
function updateSelectedScanner() {
    const selectedIndex = $('#scanner-select').val();

    if (debugMode) {
        console.log("تحديث معلومات الماسح الضوئي المحدد، الفهرس:", selectedIndex);
    }

    if (selectedIndex !== null && selectedIndex !== '' && scannersList && scannersList.length > 0) {
        const scanner = scannersList[selectedIndex];

        if (debugMode) {
            console.log("الماسح الضوئي المحدد:", scanner);
        }

        // تعيين معرف الماسح الضوئي
        if (scanner.id) {
            selectedScannerId = scanner.id;
        } else if (scanner.device_id) {
            // استخدام device_id إذا كان id غير موجود
            selectedScannerId = scanner.device_id;
        } else {
            // استخدام القيمة الافتراضية إذا لم يكن هناك معرف
            selectedScannerId = "0";
        }

        // تعيين المصدر
        if (scanner.source) {
            selectedScannerSource = scanner.source;
        } else {
            // استخدام القيمة الافتراضية إذا لم يكن هناك مصدر
            selectedScannerSource = 'pdf_only';
        }

        if (debugMode) {
            console.log('تم تحديد المصدر:', selectedScannerSource);
            console.log('تم تحديد الماسح الضوئي:', scanner.name, 'المعرف:', selectedScannerId, 'المصدر:', selectedScannerSource);
        }

        // إضافة رسالة تشخيصية في واجهة المستخدم
        showAlert('info', `تم تحديد الماسح الضوئي: ${scanner.name}`);

        // تفعيل زر المسح الضوئي
        $('#scan-button').prop('disabled', false);

        // إظهار نتائج المسح الضوئي
        $('#scan-results').show();
    } else {
        // إذا لم يتم تحديد أي ماسح ضوئي، استخدم القيمة الافتراضية
        selectedScannerId = "0";
        selectedScannerSource = 'pdf_only';

        if (debugMode) {
            console.log('لم يتم تحديد أي ماسح ضوئي، استخدام القيمة الافتراضية:', selectedScannerId);
        }

        // إضافة رسالة تشخيصية في واجهة المستخدم
        showAlert('warning', 'لم يتم تحديد أي ماسح ضوئي، سيتم استخدام القيمة الافتراضية');

        // تعطيل زر المسح الضوئي
        $('#scan-button').prop('disabled', true);

        // إخفاء نتائج المسح الضوئي
        $('#scan-results').hide();
    }
}

// بدء المسح الضوئي
function startScanning() {
    // مسح الصفحات الممسوحة من الذاكرة قبل بدء المسح الجديد
    clearScannedPages();

    // التأكد من وجود معرف للماسح الضوئي
    if (!selectedScannerId) {
        // استخدام القيمة الافتراضية إذا لم يكن هناك معرف
        selectedScannerId = "0";
        if (debugMode) {
            console.log("لم يتم تحديد جهاز المسح الضوئي، استخدام القيمة الافتراضية:", selectedScannerId);
        }
    }

    if (debugMode) {
        console.log("بدء المسح الضوئي باستخدام الماسح:", selectedScannerId, "المصدر:", selectedScannerSource);
    }

    // إضافة رسالة تشخيصية في واجهة المستخدم
    showAlert('info', `جاري بدء المسح الضوئي باستخدام الماسح: ${selectedScannerId}`);

    // إظهار مؤشر التحميل
    $('#scanning-progress').show();
    $('#scan-button').prop('disabled', true);
    $('#scanner-select').prop('disabled', true);

    // الحصول على إعدادات المسح الضوئي
    const dpi = $('#dpi-select').val() || "300";
    const useAdf = $('#use-adf').prop('checked');

    if (debugMode) {
        console.log("إعدادات المسح الضوئي:", {
            dpi: dpi,
            useAdf: useAdf
        });
    }

    // إعداد بيانات الطلب
    const requestData = {
        device_id: selectedScannerId,
        scanner_source: selectedScannerSource || 'pdf_only',
        dpi: parseInt(dpi),
        use_adf: useAdf
    };

    if (debugMode) {
        console.log("بيانات الطلب:", requestData);
    }

    // إضافة رسالة تشخيصية في واجهة المستخدم
    showAlert('info', 'جاري إرسال طلب المسح الضوئي...');

    // إرسال طلب المسح الضوئي
    $.ajax({
        url: '/scan_with_device_api',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(requestData),
        success: function(response) {
            // إخفاء مؤشر التحميل
            $('#scanning-progress').hide();
            $('#scan-button').prop('disabled', false);
            $('#scanner-select').prop('disabled', false);

            if (debugMode) {
                console.log("استجابة المسح الضوئي:", response);
            }

            if (response.success) {
                // تحديث عدد الصفحات الممسوحة
                updateScannedPagesCount(response.page_count);
                showAlert('success', response.message || 'تم مسح المستند بنجاح');

                // إظهار نتائج المسح الضوئي
                $('#scan-results').show();
            } else {
                console.error("خطأ في المسح الضوئي:", response.error);
                showAlert('danger', response.error || 'حدث خطأ أثناء المسح الضوئي');

                // إضافة رسالة تشخيصية إضافية في واجهة المستخدم
                if (response.details) {
                    showAlert('warning', `تفاصيل الخطأ: ${response.details}`);
                }

                // محاولة استخدام ماسح CANON DR-M260 كبديل
                if (selectedScannerId !== '0') {
                    showAlert('info', 'محاولة استخدام ماسح CANON DR-M260 كبديل...');

                    // تغيير معرف الماسح الضوئي إلى '0' (CANON DR-M260)
                    const fallbackRequestData = {
                        device_id: '0',
                        scanner_source: 'pdf_only',
                        dpi: parseInt(dpi),
                        use_adf: useAdf
                    };

                    // إرسال طلب المسح الضوئي باستخدام ماسح CANON DR-M260
                    $.ajax({
                        url: '/scan_with_device_api',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(fallbackRequestData),
                        success: function(fallbackResponse) {
                            if (debugMode) {
                                console.log("استجابة المسح الضوئي البديل:", fallbackResponse);
                            }

                            if (fallbackResponse.success) {
                                // تحديث عدد الصفحات الممسوحة
                                updateScannedPagesCount(fallbackResponse.page_count);
                                showAlert('success', fallbackResponse.message || 'تم مسح المستند بنجاح باستخدام ماسح CANON DR-M260');

                                // إظهار نتائج المسح الضوئي
                                $('#scan-results').show();
                            } else {
                                console.error("خطأ في المسح الضوئي البديل:", fallbackResponse.error);
                                showAlert('danger', fallbackResponse.error || 'حدث خطأ أثناء المسح الضوئي البديل');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error("خطأ في الاتصال بالخادم أثناء المسح الضوئي البديل:", error);
                            showAlert('danger', 'حدث خطأ أثناء الاتصال بالخادم أثناء المسح الضوئي البديل: ' + error);
                        }
                    });
                }
            }
        },
        error: function(xhr, status, error) {
            // إخفاء مؤشر التحميل
            $('#scanning-progress').hide();
            $('#scan-button').prop('disabled', false);
            $('#scanner-select').prop('disabled', false);

            console.error("خطأ في الاتصال بالخادم:", error);
            console.error("الحالة:", status);
            console.error("استجابة الخادم:", xhr.responseText);

            showAlert('danger', 'حدث خطأ أثناء الاتصال بالخادم: ' + error);

            // محاولة استخدام ماسح CANON DR-M260 كبديل
            showAlert('info', 'محاولة استخدام ماسح CANON DR-M260 كبديل بعد فشل الاتصال...');

            // تغيير معرف الماسح الضوئي إلى '0' (CANON DR-M260)
            const fallbackRequestData = {
                device_id: '0',
                scanner_source: 'pdf_only',
                dpi: parseInt(dpi),
                use_adf: useAdf
            };

            // إرسال طلب المسح الضوئي باستخدام ماسح CANON DR-M260
            $.ajax({
                url: '/scan_with_device_api',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(fallbackRequestData),
                success: function(fallbackResponse) {
                    if (debugMode) {
                        console.log("استجابة المسح الضوئي البديل:", fallbackResponse);
                    }

                    if (fallbackResponse.success) {
                        // تحديث عدد الصفحات الممسوحة
                        updateScannedPagesCount(fallbackResponse.page_count);
                        showAlert('success', fallbackResponse.message || 'تم مسح المستند بنجاح باستخدام ماسح CANON DR-M260');

                        // إظهار نتائج المسح الضوئي
                        $('#scan-results').show();
                    } else {
                        console.error("خطأ في المسح الضوئي البديل:", fallbackResponse.error);
                        showAlert('danger', fallbackResponse.error || 'حدث خطأ أثناء المسح الضوئي البديل');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("خطأ في الاتصال بالخادم أثناء المسح الضوئي البديل:", error);
                    showAlert('danger', 'حدث خطأ أثناء الاتصال بالخادم أثناء المسح الضوئي البديل: ' + error);
                }
            });
        }
    });
}

// تحديث عدد الصفحات الممسوحة
function updateScannedPagesCount(count) {
    $('#scanned-pages-count').text(count);

    // تفعيل زر الحفظ إذا كان هناك صفحات ممسوحة
    if (count > 0) {
        $('#save-document-button').prop('disabled', false);
    } else {
        $('#save-document-button').prop('disabled', true);
    }
}

// مسح الصفحات الممسوحة من الذاكرة
function clearScannedPages() {
    if (debugMode) {
        console.log("جاري مسح الصفحات الممسوحة من الذاكرة...");
    }

    // إرسال طلب لمسح الصفحات الممسوحة
    $.ajax({
        url: '/clear_scanned_pages',
        type: 'POST',
        success: function(response) {
            if (debugMode) {
                console.log("استجابة مسح الصفحات:", response);
            }

            if (response.success) {
                if (debugMode) {
                    console.log("تم مسح الصفحات الممسوحة بنجاح");
                }
                showAlert('info', 'تم مسح الصفحات الممسوحة من الذاكرة');
            } else {
                console.error("خطأ في مسح الصفحات الممسوحة:", response.error);
                showAlert('warning', response.error || 'حدث خطأ أثناء مسح الصفحات الممسوحة');
            }
        },
        error: function(xhr, status, error) {
            console.error("خطأ في الاتصال بالخادم:", error);
            console.error("استجابة الخادم:", xhr.responseText);

            if (debugMode) {
                console.log("خطأ في مسح الصفحات الممسوحة:", error);
            }
        }
    });
}

// عرض رسالة تنبيه
function showAlert(type, message) {
    const alertDiv = $('<div>')
        .addClass('alert alert-' + type + ' alert-dismissible fade show')
        .attr('role', 'alert')
        .html(message + '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>');

    $('#alerts-container').append(alertDiv);

    // إخفاء التنبيه تلقائياً بعد 5 ثوانٍ
    setTimeout(function() {
        alertDiv.alert('close');
    }, 5000);
}

// حفظ المستند الممسوح ضوئياً
function saveScannedDocument() {
    // التحقق من وجود مستند ممسوح ضوئياً
    const pagesCount = parseInt($('#scanned-pages-count').text());
    if (pagesCount <= 0) {
        showAlert('warning', 'لا توجد صفحات ممسوحة للحفظ');
        return;
    }

    // الحصول على وصف المستند
    const description = $('#document-description').val();

    // إظهار مؤشر التحميل
    $('#saving-progress').show();
    $('#save-document-button').prop('disabled', true);

    // إعداد بيانات الطلب - استخدام PDF فقط
    let requestData = {
        description: description,
        scan_method: 'pdf_only'
    };

    if (debugMode) {
        console.log("إرسال طلب حفظ المستند:", requestData);
    }

    // إضافة رسالة تشخيصية في واجهة المستخدم
    showAlert('info', 'جاري حفظ المستند...');

    // إرسال طلب حفظ المستند
    $.ajax({
        url: '/save_scanned_document',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(requestData),
        success: function(response) {
            // إخفاء مؤشر التحميل
            $('#saving-progress').hide();

            if (response.success) {
                showAlert('success', 'تم حفظ المستند بنجاح');
                if (debugMode) {
                    console.log("تم حفظ المستند بنجاح:", response);
                }

                // إضافة رابط لعرض المستند
                if (response.document_url) {
                    const viewLink = $('<a>')
                        .attr('href', response.document_url)
                        .attr('target', '_blank')
                        .addClass('btn btn-info mt-2')
                        .html('<i class="bi bi-eye"></i> عرض المستند');

                    $('#scan-results').append(viewLink);
                }

                // إعادة تعيين عداد الصفحات
                $('#scanned-pages-count').text('0');
                $('#save-document-button').prop('disabled', true);

                // مسح الصفحات الممسوحة من الذاكرة
                clearScannedPages();
            } else {
                $('#save-document-button').prop('disabled', false);
                console.error("خطأ في حفظ المستند:", response.error);
                showAlert('danger', response.error || 'حدث خطأ أثناء حفظ المستند');
            }
        },
        error: function(xhr, status, error) {
            // إخفاء مؤشر التحميل
            $('#saving-progress').hide();
            $('#save-document-button').prop('disabled', false);

            console.error("خطأ في الاتصال بالخادم:", error);
            console.error("استجابة الخادم:", xhr.responseText);

            showAlert('danger', 'حدث خطأ أثناء الاتصال بالخادم: ' + error);
        }
    });
}

// تهيئة الصفحة
$(document).ready(function() {
    if (debugMode) {
        console.log("تهيئة صفحة الماسح الضوئي...");
    }

    // إضافة رسالة تشخيصية في واجهة المستخدم
    showAlert('info', 'جاري تهيئة صفحة الماسح الضوئي...');

    try {
        // مسح الصفحات الممسوحة من الذاكرة عند تحميل الصفحة
        clearScannedPages();

        // تحديث قائمة أجهزة المسح الضوئي عند تحميل الصفحة
        updateScannersList();

        // تحديث معلومات الماسح الضوئي المحدد عند تغيير الاختيار
        $('#scanner-select').on('change', function() {
            if (debugMode) {
                console.log("تم تغيير اختيار الماسح الضوئي");
            }
            updateSelectedScanner();
        });

        // بدء المسح الضوئي عند النقر على زر المسح
        $('#scan-button').on('click', function() {
            if (debugMode) {
                console.log("تم النقر على زر المسح الضوئي");
            }
            startScanning();
        });

        // تحديث قائمة أجهزة المسح الضوئي عند النقر على زر التحديث
        $('#refresh-scanners').on('click', function() {
            if (debugMode) {
                console.log("تم النقر على زر تحديث قائمة الماسحات الضوئية");
            }
            // مسح الصفحات الممسوحة من الذاكرة عند تحديث قائمة الماسحات
            clearScannedPages();
            updateScannersList();
        });

        // إضافة صفحات أخرى عند النقر على زر الإضافة
        $('#add-more-pages-button').on('click', function() {
            if (debugMode) {
                console.log("تم النقر على زر إضافة صفحات أخرى");
            }
            startScanning();
        });

        // حفظ المستند الممسوح ضوئياً عند النقر على زر الحفظ
        $('#save-document-button').on('click', function() {
            if (debugMode) {
                console.log("تم النقر على زر حفظ المستند");
            }
            saveScannedDocument();
        });

        if (debugMode) {
            console.log("تم تهيئة صفحة الماسح الضوئي بنجاح");
        }

        // إضافة رسالة تشخيصية في واجهة المستخدم
        showAlert('success', 'تم تهيئة صفحة الماسح الضوئي بنجاح');
    } catch (error) {
        console.error("حدث خطأ أثناء تهيئة صفحة الماسح الضوئي:", error);
        showAlert('danger', 'حدث خطأ أثناء تهيئة صفحة الماسح الضوئي: ' + error.message);
    }

    // محاولة تحديث قائمة الماسحات الضوئية مرة أخرى بعد 3 ثوانٍ
    setTimeout(function() {
        if (debugMode) {
            console.log("محاولة تحديث قائمة الماسحات الضوئية مرة أخرى...");
        }
        updateScannersList();
    }, 3000);
});
