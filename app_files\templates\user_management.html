{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2 class="mb-0">إدارة المستخدمين</h2>
            <a href="{{ url_for('add_user') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-1"></i> إضافة مستخدم جديد
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover table-bordered">
                    <thead class="table-dark">
                        <tr>
                            <th style="width: 5%;">#</th>
                            <th style="width: 15%;">اسم المستخدم</th>
                            <th style="width: 20%;">الاسم الكامل</th>
                            <th style="width: 30%;">الصلاحيات</th>
                            <th style="width: 30%;">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if users %}
                            {% for user in users %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ user.username }}</td>
                                <td>{{ user.full_name }}</td>
                                <td>
                                    {% if user.permissions %}
                                        <ul class="list-unstyled mb-0">
                                            {% for permission in user.permissions.split(',') %}
                                                <li><span class="badge bg-info">{{ permission }}</span></li>
                                            {% endfor %}
                                        </ul>
                                    {% else %}
                                        <span class="badge bg-secondary">لا توجد صلاحيات محددة</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('edit_user', user_id=user.id) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        <a href="{{ url_for('set_permissions', user_id=user.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-key"></i> الصلاحيات
                                        </a>
                                        {% if user.id != session.get('employee_id') %}
                                            <a href="{{ url_for('delete_user', user_id=user.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                                <i class="fas fa-trash"></i> حذف
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="5" class="text-center">لا يوجد مستخدمين مسجلين</td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
