<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض المستند</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .toolbar {
            background-color: #0d47a1;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .toolbar-title {
            font-size: 18px;
            font-weight: bold;
        }
        
        .toolbar-actions {
            display: flex;
            gap: 10px;
        }
        
        .toolbar-button {
            background-color: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .toolbar-button:hover {
            background-color: rgba(255,255,255,0.3);
        }
        
        .content {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            overflow: auto;
        }
        
        .pdf-container {
            width: 100%;
            height: 100%;
        }
        
        .image-container {
            display: flex;
            justify-content: center;
            align-items: center;
            max-width: 100%;
            max-height: 100%;
        }
        
        .image-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        .a4-container {
            width: 210mm;
            height: 297mm;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }
        
        .a4-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        @media print {
            .toolbar {
                display: none;
            }
            
            body {
                background-color: white;
            }
            
            .content {
                padding: 0;
            }
            
            .a4-container {
                box-shadow: none;
                width: 100%;
                height: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="toolbar">
        <div class="toolbar-title">عرض المستند</div>
        <div class="toolbar-actions">
            <button class="toolbar-button" onclick="printDocument()">طباعة</button>
            <button class="toolbar-button" onclick="window.close()">إغلاق</button>
        </div>
    </div>
    
    <div class="content">
        {% if mime_type == 'application/pdf' %}
            <iframe class="pdf-container" src="/raw?path={{ file_path }}" frameborder="0"></iframe>
        {% elif mime_type.startswith('image/') %}
            <div class="image-container">
                <div class="a4-container">
                    <img src="/raw?path={{ file_path }}" alt="المستند" onerror="handleImageError()">
                </div>
            </div>
        {% else %}
            <div class="error-message">نوع الملف غير مدعوم</div>
        {% endif %}
    </div>
    
    <script>
        function printDocument() {
            window.print();
        }
        
        function handleImageError() {
            window.location.href = "/viewer_error.html?message=حدث خطأ في تحميل الصورة";
        }
    </script>
</body>
</html>
