{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h3 class="card-title">الموظفون في إجازة</h3>
            <div>
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>نوع الإجازة</th>
                            <th>من تاريخ</th>
                            <th>إلى تاريخ</th>
                            <th>عدد الأيام</th>
                            <th>المباشرة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave in leaves %}
                        <tr>
                            <td>{{ leave.employee.full_name }}</td>
                            <td>{{ leave.leave_type.name }}</td>
                            <td class="date-cell">{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                            <td class="date-cell">{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ (leave.end_date - leave.start_date).days + 1 }}</td>
                            <td class="date-cell">{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}