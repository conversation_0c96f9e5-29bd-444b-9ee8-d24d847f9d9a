@app.route('/save_scanned_document/<int:leave_id>', methods=['POST'])
def save_scanned_document(leave_id):
    """
    حفظ المستند الممسوح ضوئيًا
    """
    if not session.get('employee_id'):
        return jsonify({'error': 'غير مصرح'}), 401

    # التحقق من صلاحيات المستخدم (فقط المدير يمكنه مسح المستندات ضوئيًا)
    if not session.get('is_admin'):
        return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه الوظيفة'}), 403

    # الحصول على بيانات المستند
    image_data = request.json.get('image_data')
    description = request.json.get('description', '')
    scan_method = request.json.get('scan_method', '')

    try:
        # التحقق من وجود الإجازة
        leave = LeaveRequest.query.get(leave_id)
        if not leave:
            return jsonify({'error': 'الإجازة غير موجودة'}), 404

        # حفظ المستند بصيغة PDF فقط
        # حفظ الصفحات الممسوحة كملف PDF
        result = pdf_only_scanner.save_as_pdf()

        if not result:
            return jsonify({'error': 'فشل في حفظ ملف PDF'}), 500

        # الحصول على مسار الملف وبيانات الملف
        filepath = result['file_path']
        file_type = 'pdf'

        # إنشاء اسم فريد للملف
        import uuid
        from datetime import datetime
        unique_filename = f"{uuid.uuid4().hex}.{file_type}"
        dest_path = os.path.join(app.config['DOCUMENTS_FOLDER'], unique_filename)

        # نسخ الملف إلى مجلد المستندات
        import shutil
        shutil.copy2(filepath, dest_path)

        # مسح قائمة الصفحات الممسوحة
        pdf_only_scanner.clear_scanned_pages()

        # إنشاء سجل للمستند
        document = Document(
            leave_id=leave_id,
            filename=unique_filename,
            original_filename=f"scanned_document_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{file_type}",
            file_type=file_type,
            description=description,
            upload_date=datetime.now()
        )

        # حفظ السجل في قاعدة البيانات
        db.session.add(document)
        db.session.commit()

        return jsonify({
            'success': True,
            'document_id': document.id,
            'message': 'تم حفظ المستند بنجاح'
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"خطأ في حفظ المستند: {str(e)}")
        return jsonify({'error': f'حدث خطأ أثناء حفظ المستند: {str(e)}'}), 500
