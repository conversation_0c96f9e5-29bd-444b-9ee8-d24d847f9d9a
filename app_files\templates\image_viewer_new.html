<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ document_name }}</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }

        .image-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .toolbar {
            background-color: #0d47a1;
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 100;
        }

        .toolbar h3 {
            margin: 0;
            font-size: 18px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 70%;
        }

        .toolbar-actions {
            display: flex;
            gap: 10px;
        }

        .toolbar-button {
            color: white;
            text-decoration: none;
            padding: 6px 12px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
        }

        .toolbar-button:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .image-content {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            overflow: auto;
            background-color: #f0f0f0;
        }

        .image-frame {
            max-width: 100%;
            max-height: 80vh;
            object-fit: contain;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            background-color: white;
        }

        .image-error {
            text-align: center;
            padding: 20px;
            background-color: #ffebee;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 500px;
        }

        .image-error h3 {
            color: #d32f2f;
            margin-top: 0;
        }

        .image-error p {
            color: #555;
            margin-bottom: 20px;
        }

        .image-error-icon {
            font-size: 48px;
            color: #d32f2f;
            margin-bottom: 20px;
        }

        @media print {
            .toolbar {
                display: none;
            }
            
            .image-content {
                padding: 0;
                background-color: white;
            }
            
            .image-frame {
                max-width: 100%;
                max-height: 100%;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="image-container">
        <div class="toolbar">
            <h3>{{ document_name }}</h3>
            <div class="toolbar-actions">
                <button class="toolbar-button" onclick="printImage()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <a href="{{ back_url }}" class="toolbar-button">
                    <i class="fas fa-arrow-right"></i> رجوع
                </a>
            </div>
        </div>
        <div class="image-content" id="imageContent">
            <!-- سيتم تحميل الصورة بواسطة JavaScript -->
        </div>
    </div>

    <script>
        // تحميل الصورة باستخدام JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            const imageContent = document.getElementById('imageContent');
            const imageUrl = "{{ document_url }}";
            
            // إنشاء عنصر الصورة
            const img = new Image();
            
            // معالجة حدث تحميل الصورة
            img.onload = function() {
                img.className = 'image-frame';
                imageContent.innerHTML = '';
                imageContent.appendChild(img);
            };
            
            // معالجة حدث خطأ تحميل الصورة
            img.onerror = function() {
                imageContent.innerHTML = `
                    <div class="image-error">
                        <div class="image-error-icon">⚠️</div>
                        <h3>حدث خطأ في معالجة الصورة</h3>
                        <p>لم نتمكن من تحميل الصورة. يرجى المحاولة مرة أخرى أو التواصل مع مسؤول النظام.</p>
                        <button class="toolbar-button" onclick="window.location.reload()">
                            إعادة المحاولة
                        </button>
                    </div>
                `;
            };
            
            // تعيين مصدر الصورة لبدء التحميل
            img.src = imageUrl;
            img.alt = "{{ document_name }}";
        });
        
        // وظيفة الطباعة
        function printImage() {
            window.print();
        }
    </script>

    <!-- تضمين Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
        integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
</body>
</html>
