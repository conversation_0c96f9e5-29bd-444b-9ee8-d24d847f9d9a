<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عارض المستندات</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }

        .container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .toolbar {
            background-color: #0d47a1;
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 100;
        }

        .toolbar h3 {
            margin: 0;
            font-size: 18px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 70%;
        }

        .toolbar-actions {
            display: flex;
            gap: 10px;
        }

        .toolbar-button {
            color: white;
            text-decoration: none;
            padding: 6px 12px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
        }

        .toolbar-button:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .content {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            overflow: auto;
            background-color: #f0f0f0;
        }

        .fallback-container {
            max-width: 600px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
        }

        .fallback-title {
            color: #0d47a1;
            margin-top: 0;
            margin-bottom: 20px;
        }

        .fallback-message {
            color: #555;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .fallback-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
        }

        .fallback-button {
            background-color: #0d47a1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
            text-decoration: none;
            display: inline-block;
            min-width: 200px;
        }

        .fallback-button:hover {
            background-color: #0a3880;
        }

        .fallback-note {
            color: #777;
            font-size: 14px;
            margin-top: 20px;
        }

        @media print {
            .toolbar, .fallback-actions, .fallback-note {
                display: none;
            }
            
            .content {
                padding: 0;
                background-color: white;
            }
            
            .fallback-container {
                box-shadow: none;
                padding: 0;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
        integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
</head>
<body>
    <div class="container">
        <div class="toolbar">
            <h3 id="document-title">عارض المستندات</h3>
            <div class="toolbar-actions">
                <button class="toolbar-button" onclick="goBack()">
                    <i class="fas fa-arrow-right"></i> رجوع
                </button>
            </div>
        </div>
        <div class="content">
            <div class="fallback-container">
                <h2 class="fallback-title">تنزيل المستند</h2>
                <p class="fallback-message">
                    لعرض هذا المستند، يمكنك تنزيله على جهازك ثم فتحه باستخدام البرنامج المناسب.
                </p>
                <div class="fallback-actions">
                    <a id="download-link" href="#" class="fallback-button" download>
                        <i class="fas fa-download"></i> تنزيل المستند
                    </a>
                </div>
                <p class="fallback-note">
                    ملاحظة: إذا كنت تواجه مشكلة في عرض المستند، يرجى التأكد من وجود برنامج مناسب لفتح هذا النوع من الملفات على جهازك.
                </p>
            </div>
        </div>
    </div>

    <script>
        // استخراج معلمات URL
        function getUrlParams() {
            const params = {};
            const queryString = window.location.search.substring(1);
            const pairs = queryString.split('&');
            
            for (const pair of pairs) {
                const [key, value] = pair.split('=');
                params[decodeURIComponent(key)] = decodeURIComponent(value || '');
            }
            
            return params;
        }
        
        // تحميل المستند
        function loadDocument() {
            const params = getUrlParams();
            const documentUrl = params.url;
            const documentTitle = params.title || 'مستند';
            
            // تعيين عنوان المستند
            document.getElementById('document-title').textContent = documentTitle;
            document.title = documentTitle;
            
            // التحقق من وجود URL للمستند
            if (!documentUrl) {
                alert('لم يتم تحديد مسار المستند');
                return;
            }
            
            // تعيين رابط التنزيل
            const downloadLink = document.getElementById('download-link');
            downloadLink.href = documentUrl;
            downloadLink.setAttribute('download', documentTitle);
        }
        
        // الرجوع للصفحة السابقة
        function goBack() {
            window.history.back();
        }
        
        // تحميل المستند عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', loadDocument);
    </script>
</body>
</html>
