from app import app
from models import db, LeaveType

def add_leave_types():
    with app.app_context():
        # التحقق من وجود أنواع إجازات
        if LeaveType.query.count() > 0:
            print("أنواع الإجازات موجودة بالفعل")
            return
            
        # إضافة أنواع الإجازات
        leave_types = [
            {'name': 'إجازة اعتيادية', 'days_allowed': 30},
            {'name': 'إجازة مرضية', 'days_allowed': 30},
            {'name': 'إجازة المعيل', 'days_allowed': 15},
            {'name': 'إجازة خمس سنوات', 'days_allowed': 60},
            {'name': 'إجازة بدون راتب', 'days_allowed': None},
            {'name': 'إجازة ما قبل الوضع', 'days_allowed': 21},
            {'name': 'إجازة ما بعد الوضع', 'days_allowed': 51},
            {'name': 'إجازة أمومة', 'days_allowed': 72}
        ]
        
        for lt_data in leave_types:
            leave_type = LeaveType(**lt_data)
            db.session.add(leave_type)
        
        db.session.commit()
        print("تم إضافة أنواع الإجازات بنجاح")

if __name__ == '__main__':
    add_leave_types()
