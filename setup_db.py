import os
import sys
from pathlib import Path

# Get the absolute path of the current script's directory
BASE_DIR = Path(__file__).resolve().parent

# Add the project directory to Python path
sys.path.append(str(BASE_DIR))

from app import app
from models import db, Employee, LeaveType
from datetime import datetime

def setup_database():
    db_path = os.path.join(BASE_DIR, 'leaves.db')
    
    if os.path.exists(db_path):
        try:
            os.remove(db_path)
            print("تم حذف قاعدة البيانات القديمة")
        except PermissionError:
            print("خطأ: لا يمكن حذف قاعدة البيانات. تأكد من إغلاق جميع الاتصالات")
            return

    with app.app_context():
        try:
            db.create_all()
            print("تم إنشاء قاعدة البيانات الجديدة")

            # إضافة موظف تجريبي
            test_employee = Employee(
                full_name='موظف تجريبي',
                work_location='المكتب الرئيسي',
                job_title='موظف',
                leave_balance=30,
                children_count=0
            )
            db.session.add(test_employee)

            # إضافة أنواع الإجازات
            leave_types = [
                {'name': 'إجازة اعتيادية', 'days_allowed': 30},
                {'name': 'إجازة مرضية', 'days_allowed': 30},
                {'name': 'إجازة المعيل', 'days_allowed': 15},
                {'name': 'إجازة خمس سنوات', 'days_allowed': 60},
                {'name': 'إجازة بدون راتب', 'days_allowed': None},
                {'name': 'إجازة ما قبل الوضع', 'days_allowed': 21},
                {'name': 'إجازة ما بعد الوضع', 'days_allowed': 51},
                {'name': 'إجازة أمومة', 'days_allowed': 72}
            ]
            
            for leave_type in leave_types:
                lt = LeaveType(**leave_type)
                db.session.add(lt)

            db.session.commit()
            print("تم إضافة البيانات الأولية بنجاح")
            
        except Exception as e:
            print(f"حدث خطأ: {str(e)}")
            db.session.rollback()
            if os.path.exists(db_path):
                os.remove(db_path)

if __name__ == '__main__':
    setup_database()




