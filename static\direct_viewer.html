<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عارض المستندات المباشر</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }

        .container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .toolbar {
            background-color: #0d47a1;
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 100;
        }

        .toolbar h3 {
            margin: 0;
            font-size: 18px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 70%;
        }

        .toolbar-actions {
            display: flex;
            gap: 10px;
        }

        .toolbar-button {
            color: white;
            text-decoration: none;
            padding: 6px 12px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
        }

        .toolbar-button:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .content {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            overflow: auto;
            background-color: #f0f0f0;
        }

        .image-container {
            max-width: 100%;
            max-height: 80vh;
            background-color: white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
        }

        .image-frame {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .pdf-container {
            width: 100%;
            height: 80vh;
            border: none;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .error-message {
            text-align: center;
            padding: 20px;
            background-color: #ffebee;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 500px;
        }

        .error-message h3 {
            color: #d32f2f;
            margin-top: 0;
        }

        .error-message p {
            color: #555;
            margin-bottom: 20px;
        }

        .loading-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #0d47a1;
            animation: spin 1s linear infinite;
            margin-bottom: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media print {
            .toolbar {
                display: none;
            }
            
            .content {
                padding: 0;
                background-color: white;
            }
            
            .image-container {
                box-shadow: none;
                padding: 0;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
        integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
</head>
<body>
    <div class="container">
        <div class="toolbar">
            <h3 id="document-title">عارض المستندات</h3>
            <div class="toolbar-actions">
                <button class="toolbar-button" onclick="printDocument()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="toolbar-button" onclick="goBack()">
                    <i class="fas fa-arrow-right"></i> رجوع
                </button>
            </div>
        </div>
        <div class="content" id="content">
            <div class="loading-indicator">
                <div class="spinner"></div>
                <p>جاري تحميل المستند...</p>
            </div>
        </div>
    </div>

    <script>
        // استخراج معلمات URL
        function getUrlParams() {
            const params = {};
            const queryString = window.location.search.substring(1);
            const pairs = queryString.split('&');
            
            for (const pair of pairs) {
                const [key, value] = pair.split('=');
                params[decodeURIComponent(key)] = decodeURIComponent(value || '');
            }
            
            return params;
        }
        
        // تحميل المستند
        function loadDocument() {
            const params = getUrlParams();
            const documentUrl = params.url;
            const documentTitle = params.title || 'مستند';
            const documentType = params.type || '';
            
            // تعيين عنوان المستند
            document.getElementById('document-title').textContent = documentTitle;
            document.title = documentTitle;
            
            // التحقق من وجود URL للمستند
            if (!documentUrl) {
                showError('لم يتم تحديد مسار المستند');
                return;
            }
            
            // تحديد نوع المستند
            const isPdf = documentType.toLowerCase() === 'pdf' || documentUrl.toLowerCase().endsWith('.pdf');
            
            if (isPdf) {
                loadPdf(documentUrl);
            } else {
                loadImage(documentUrl);
            }
        }
        
        // تحميل صورة
        function loadImage(url) {
            const content = document.getElementById('content');
            
            // إنشاء عنصر الصورة
            const container = document.createElement('div');
            container.className = 'image-container';
            
            const img = new Image();
            img.className = 'image-frame';
            
            // معالجة حدث تحميل الصورة
            img.onload = function() {
                content.innerHTML = '';
                container.appendChild(img);
                content.appendChild(container);
            };
            
            // معالجة حدث خطأ تحميل الصورة
            img.onerror = function() {
                showError('حدث خطأ في تحميل الصورة');
            };
            
            // تعيين مصدر الصورة لبدء التحميل
            img.src = url;
        }
        
        // تحميل ملف PDF
        function loadPdf(url) {
            const content = document.getElementById('content');
            
            // إنشاء عنصر iframe
            const iframe = document.createElement('iframe');
            iframe.className = 'pdf-container';
            
            // معالجة حدث تحميل الملف
            iframe.onload = function() {
                // التحقق مما إذا كان الملف قد تم تحميله بنجاح
                try {
                    // محاولة الوصول إلى محتوى الإطار
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    
                    // إذا كان المحتوى فارغًا أو يحتوي على رسالة خطأ
                    if (!iframeDoc || iframeDoc.body.innerHTML.includes('error') || iframeDoc.body.innerHTML === '') {
                        throw new Error('فشل تحميل ملف PDF');
                    }
                } catch (e) {
                    // في حالة حدوث خطأ، عرض رسالة خطأ
                    showError('حدث خطأ في تحميل ملف PDF');
                    return;
                }
            };
            
            // معالجة حدث خطأ تحميل الملف
            iframe.onerror = function() {
                showError('حدث خطأ في تحميل ملف PDF');
            };
            
            // تعيين مصدر الملف لبدء التحميل
            iframe.src = url;
            
            // إضافة الإطار إلى المحتوى بعد مهلة قصيرة
            setTimeout(function() {
                content.innerHTML = '';
                content.appendChild(iframe);
            }, 500);
        }
        
        // عرض رسالة خطأ
        function showError(message) {
            const content = document.getElementById('content');
            
            content.innerHTML = `
                <div class="error-message">
                    <h3>حدث خطأ</h3>
                    <p>${message}</p>
                    <button class="toolbar-button" onclick="window.location.reload()">
                        إعادة المحاولة
                    </button>
                </div>
            `;
        }
        
        // طباعة المستند
        function printDocument() {
            window.print();
        }
        
        // الرجوع للصفحة السابقة
        function goBack() {
            window.history.back();
        }
        
        // تحميل المستند عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', loadDocument);
    </script>
</body>
</html>
