from app import app, db
from models import Document, LeaveRequest
from sqlalchemy import text
import os

def check_document_table():
    """التحقق من جدول المستندات في قاعدة البيانات"""
    with app.app_context():
        try:
            # التحقق من وجود جدول document
            result = db.session.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='document'"))
            tables = result.fetchall()
            
            if not tables:
                print("جدول 'document' غير موجود في قاعدة البيانات!")
                return
                
            print("جدول 'document' موجود في قاعدة البيانات.")
            
            # عرض هيكل الجدول
            result = db.session.execute(text("PRAGMA table_info(document)"))
            columns = result.fetchall()
            print("\nهيكل جدول document:")
            for column in columns:
                print(f"  {column}")
                
            # عرض عدد السجلات في الجدول
            result = db.session.execute(text("SELECT COUNT(*) FROM document"))
            count = result.scalar()
            print(f"\nعدد السجلات في جدول document: {count}")
            
            # عرض بعض السجلات إذا كانت موجودة
            if count > 0:
                documents = Document.query.limit(5).all()
                print("\nأمثلة من سجلات جدول document:")
                for doc in documents:
                    print(f"  المعرف: {doc.id}, معرف الإجازة: {doc.leave_id}, اسم الملف: {doc.file_name}")
                    
            # التحقق من وجود إجازات
            leave_count = LeaveRequest.query.count()
            print(f"\nعدد الإجازات في النظام: {leave_count}")
            
            if leave_count > 0:
                leaves = LeaveRequest.query.limit(5).all()
                print("\nأمثلة من سجلات الإجازات:")
                for leave in leaves:
                    print(f"  المعرف: {leave.id}, معرف الموظف: {leave.employee_id}, نوع الإجازة: {leave.leave_type_id}")
                    
                    # محاولة الوصول إلى المستندات المرتبطة بالإجازة
                    try:
                        docs = Document.query.filter_by(leave_id=leave.id).all()
                        print(f"    عدد المستندات المرتبطة: {len(docs)}")
                        for doc in docs:
                            print(f"      المستند: {doc.id}, {doc.file_name}")
                    except Exception as e:
                        print(f"    خطأ في الوصول إلى المستندات المرتبطة: {str(e)}")
                        
        except Exception as e:
            print(f"خطأ في التحقق من جدول document: {str(e)}")

if __name__ == '__main__':
    # التحقق من جدول المستندات
    check_document_table()
