"""
تطبيق Flask بسيط جدًا للاختبار
"""
from flask import Flask, redirect, url_for, request, session, flash
import os
import sys
import threading
import webbrowser
import socket
import time

# إنشاء تطبيق Flask
app = Flask(__name__)
app.secret_key = 'your_secret_key'

# الحصول على عنوان IP المحلي
def get_local_ip():
    try:
        # إنشاء اتصال مؤقت للحصول على عنوان IP المحلي
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        print(f"خطأ في الحصول على عنوان IP المحلي: {str(e)}")
        # محاولة بديلة للحصول على عنوان IP المحلي
        try:
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            return local_ip
        except Exception:
            return "127.0.0.1"

# فتح المتصفح بعد تأخير قصير
def open_browser(url):
    # انتظار 1.5 ثانية لبدء تشغيل الخادم
    time.sleep(1.5)
    print(f"فتح المتصفح على العنوان: {url}")
    webbrowser.open(url)

# الصفحة الرئيسية
@app.route('/')
def index():
    return """
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>اختبار الاتصال</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                text-align: center;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            h1 {
                color: #4CAF50;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>تم الاتصال بنجاح!</h1>
            <p>إذا كنت ترى هذه الرسالة، فهذا يعني أن الاتصال بالخادم يعمل بشكل صحيح.</p>
            <p>يمكنك الآن تجربة تشغيل التطبيق الرئيسي.</p>
        </div>
    </body>
    </html>
    """

if __name__ == '__main__':
    # استخدام المنفذ 5000 دائمًا
    port = 5000
    
    # الحصول على عنوان IP المحلي
    local_ip = get_local_ip()
    
    # عرض معلومات الاتصال
    print("\n" + "=" * 50)
    print("معلومات الاتصال بالتطبيق:")
    print("=" * 50)
    print(f"عنوان IP المحلي: {local_ip}")
    print(f"المنفذ: {port}")
    print(f"الوصول المحلي: http://localhost:{port}")
    print(f"الوصول من الشبكة المحلية: http://{local_ip}:{port}")
    print("=" * 50)
    print("للوصول من جهاز آخر (موبايل أو كمبيوتر آخر):")
    print(f"1. تأكد من أن الجهاز متصل بنفس شبكة WiFi")
    print(f"2. افتح متصفح الويب وأدخل العنوان: http://{local_ip}:{port}")
    print("=" * 50 + "\n")
    
    # فتح المتصفح تلقائيًا
    url = f"http://localhost:{port}"
    threading.Thread(target=open_browser, args=(url,)).start()
    
    print(f"بدء تشغيل التطبيق على المنفذ {port}...")
    # تشغيل التطبيق على جميع واجهات الشبكة (0.0.0.0) ليكون متاحًا على الشبكة المحلية
    app.run(host='0.0.0.0', port=port, threaded=True)
