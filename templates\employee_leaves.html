{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2 class="mb-0">إجازات الموظف: {{ employee.full_name }}</h2>
            <span class="badge bg-primary">رصيد الإجازات: {{ employee.leave_balance }} يوم</span>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <a href="{{ url_for('add_leave', employee_id=employee.id) }}" class="btn btn-primary">إضافة إجازة جديدة</a>
                <a href="{{ url_for('employees') }}" class="btn btn-secondary">عودة</a>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>نوع الإجازة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>عدد الأيام</th>
                            <th>تاريخ الانفكاك</th>
                            <th>تاريخ المباشرة</th>
                            <th>الأمر الإداري</th>
                            <th>المستندات</th>
                            <th>الحالة</th>
                            <th>ملاحظات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if leaves %}
                            {% for leave in leaves %}
                            <tr>
                                <td>{{ leave.leave_type.name if leave.leave_type else 'غير محدد' }}</td>
                                <td class="date-cell">{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                                <td class="date-cell">{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ leave.days_count }}</td>
                                <td class="date-cell">{{ leave.departure_date.strftime('%Y-%m-%d') if leave.departure_date else leave.start_date.strftime('%Y-%m-%d') }}</td>
                                <td class="date-cell">{{ leave.return_date.strftime('%Y-%m-%d') if leave.return_date else '-' }}</td>
                                <td>
                                    {% if leave.order_number or leave.order_date or leave.order_source %}
                                        <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="popover"
                                            data-bs-placement="top"
                                            data-bs-html="true"
                                            data-bs-title="تفاصيل الأمر الإداري"
                                            data-bs-content="
                                                <strong>الرقم:</strong> {{ leave.order_number or '-' }}<br>
                                                <strong>التاريخ:</strong> <span class="date-cell">{{ leave.order_date.strftime('%Y-%m-%d') if leave.order_date else '-' }}</span><br>
                                                <strong>الجهة المصدرة:</strong> {{ leave.order_source or '-' }}
                                            ">
                                            <i class="fas fa-file-alt"></i> عرض الأمر
                                        </button>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="document-buttons">
                                        <!-- زر عرض المستندات -->
                                        <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#documentsModal{{ leave.id }}">
                                            <i class="fas fa-file-alt"></i> المستندات
                                            {% set documents = get_documents(leave.id) %}
                                            <span class="badge bg-light text-dark">{{ documents|length }}</span>
                                        </button>

                                        <!-- زر رفع مستند جديد -->
                                        <a href="{{ url_for('upload_document', leave_id=leave.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-upload"></i> رفع
                                        </a>

                                        <!-- زر مسح مستند جديد -->
                                        <a href="{{ url_for('scan_document_page', leave_id=leave.id) }}" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-scanner"></i> مسح
                                        </a>

                                        <!-- نافذة منبثقة لعرض المستندات -->
                                        <div class="modal fade" id="documentsModal{{ leave.id }}" tabindex="-1" aria-hidden="true">
                                            <div class="modal-dialog modal-lg modal-dialog-centered">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-primary text-white">
                                                        <h5 class="modal-title">مستندات الإجازة</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        {% set documents = get_documents(leave.id) %}
                                                        {% if documents %}
                                                            <div class="table-responsive">
                                                                <table class="table table-striped table-hover">
                                                                    <thead class="table-dark">
                                                                        <tr>
                                                                            <th>#</th>
                                                                            <th>اسم المستند</th>
                                                                            <th>النوع</th>
                                                                            <th>تاريخ الرفع</th>
                                                                            <th>الوصف</th>
                                                                            <th>الإجراءات</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        {% for doc in documents %}
                                                                            <tr>
                                                                                <td>{{ loop.index }}</td>
                                                                                <td>{{ doc.file_name }}</td>
                                                                                <td>
                                                                                    {% if doc.document_type == 'uploaded' %}
                                                                                        <span class="badge bg-primary">مرفوع</span>
                                                                                    {% elif doc.document_type == 'scanned' %}
                                                                                        <span class="badge bg-info">ممسوح ضوئياً</span>
                                                                                    {% else %}
                                                                                        <span class="badge bg-secondary">{{ doc.document_type }}</span>
                                                                                    {% endif %}
                                                                                </td>
                                                                                <td class="date-cell">{{ doc.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                                                                <td>{{ doc.description or '-' }}</td>
                                                                                <td>
                                                                                    <div class="btn-group">
                                                                                        <a href="{{ url_for('view_document', document_id=doc.id) }}" class="btn btn-sm btn-info" target="_blank">
                                                                                            <i class="fas fa-eye"></i> عرض
                                                                                        </a>
                                                                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteDocModal{{ doc.id }}">
                                                                                            <i class="fas fa-trash-alt"></i> حذف
                                                                                        </button>
                                                                                    </div>
                                                                                </td>
                                                                            </tr>
                                                                        {% endfor %}
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        {% else %}
                                                            <div class="alert alert-info">
                                                                <i class="fas fa-info-circle"></i> لا توجد مستندات مرفقة بهذه الإجازة.
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                    <div class="modal-footer">
                                                        <a href="{{ url_for('upload_document', leave_id=leave.id) }}" class="btn btn-primary">
                                                            <i class="fas fa-upload"></i> رفع مستند جديد
                                                        </a>
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- نوافذ منبثقة لتأكيد حذف المستندات -->
                                        {% for doc in get_documents(leave.id) %}
                                            <div class="modal fade" id="deleteDocModal{{ doc.id }}" tabindex="-1" aria-hidden="true">
                                                <div class="modal-dialog modal-dialog-centered">
                                                    <div class="modal-content">
                                                        <div class="modal-header bg-danger text-white">
                                                            <h5 class="modal-title">تأكيد حذف المستند</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p>هل أنت متأكد من رغبتك في حذف هذا المستند؟</p>
                                                            <p><strong>اسم المستند:</strong> {{ doc.file_name }}</p>
                                                            <p class="text-danger"><strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء.</p>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                            <a href="{{ url_for('delete_document', document_id=doc.id) }}" class="btn btn-danger">تأكيد الحذف</a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </td>
                                <td>
                                    {% if leave.status == 'pending' %}
                                        <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif leave.status == 'approved' %}
                                        <span class="badge bg-success">معتمدة</span>
                                    {% elif leave.status == 'rejected' %}
                                        <span class="badge bg-danger">مرفوضة</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ leave.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span>{{ leave.comment or '' }}</span>
                                        {% if leave.status == 'approved' and leave.end_date <= today and not 'تمت المباشرة' in leave.comment %}
                                            <button type="button" class="btn btn-sm btn-success ms-2" data-bs-toggle="modal" data-bs-target="#returnModal{{ leave.id }}">
                                                <i class="fas fa-check-circle"></i> تحديث المباشرة
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('edit_leave', leave_id=leave.id) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteLeaveModal{{ leave.id }}">
                                            <i class="fas fa-trash-alt"></i> حذف
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}

                            <!-- النوافذ المنبثقة -->
                            {% for leave in leaves %}
                            <!-- نافذة منبثقة لتأكيد حذف الإجازة -->
                            <div class="modal fade" id="deleteLeaveModal{{ leave.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger text-white">
                                            <h5 class="modal-title">تأكيد حذف الإجازة</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>هل أنت متأكد من رغبتك في حذف هذه الإجازة؟</p>
                                            <p><strong>نوع الإجازة:</strong> {{ leave.leave_type.name }}</p>
                                            <p><strong>تاريخ البداية:</strong> <span class="date-cell">{{ leave.start_date.strftime('%Y-%m-%d') }}</span></p>
                                            <p><strong>تاريخ النهاية:</strong> <span class="date-cell">{{ leave.end_date.strftime('%Y-%m-%d') }}</span></p>
                                            <p class="text-danger"><strong>تحذير:</strong> سيتم حذف جميع المستندات المرتبطة بهذه الإجازة أيضًا. لا يمكن التراجع عن هذا الإجراء.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <form method="POST" action="{{ url_for('delete_leave', leave_id=leave.id) }}">
                                                <button type="submit" class="btn btn-danger">تأكيد الحذف</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- نافذة منبثقة لتحديث تاريخ المباشرة -->
                            {% if leave.status == 'approved' and leave.end_date <= today and not 'تمت المباشرة' in leave.comment %}
                            <div class="modal fade" id="returnModal{{ leave.id }}" tabindex="-1" aria-labelledby="returnModalLabel{{ leave.id }}" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header bg-success text-white">
                                            <h5 class="modal-title" id="returnModalLabel{{ leave.id }}">تحديث تاريخ المباشرة</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <form method="POST" action="{{ url_for('update_return_status', leave_id=leave.id) }}">
                                            <div class="modal-body">
                                                <div class="mb-3">
                                                    <label for="return_date{{ leave.id }}" class="form-label">تاريخ المباشرة:</label>
                                                    <input type="date" id="return_date{{ leave.id }}" name="return_date" class="form-control" lang="en" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="comment{{ leave.id }}" class="form-label">ملاحظات إضافية:</label>
                                                    <textarea id="comment{{ leave.id }}" name="additional_comment" class="form-control" rows="2"></textarea>
                                                </div>
                                                <div class="mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="return_status" id="status_returned{{ leave.id }}" value="returned" checked>
                                                        <label class="form-check-label" for="status_returned{{ leave.id }}">
                                                            <span class="text-success"><i class="fas fa-check-circle"></i> تمت المباشرة</span>
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="return_status" id="status_not_returned{{ leave.id }}" value="not_returned">
                                                        <label class="form-check-label" for="status_not_returned{{ leave.id }}">
                                                            <span class="text-danger"><i class="fas fa-times-circle"></i> لم يباشر</span>
                                                        </label>
                                                    </div>
                                                </div>

                                                <div class="alert alert-info">
                                                    <i class="fas fa-info-circle"></i> إذا اخترت "تمت المباشرة"، سيختفي الموظف من قائمة الموظفين في إجازة. إذا اخترت "لم يباشر"، سيظل الموظف في القائمة حتى يتم تحديث حالته.
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <button type="submit" class="btn btn-success">تأكيد الحالة</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="11" class="text-center">لا توجد إجازات مسجلة لهذا الموظف</td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>

    /* تحسين عرض القوائم المنسدلة */
    .dropdown-menu {
        z-index: 1050 !important;
        min-width: 180px !important;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        border: none !important;
        border-radius: 0.5rem !important;
        padding: 0.5rem 0 !important;
    }

    .dropdown-item {
        padding: 0.5rem 1rem !important;
        font-size: 0.9rem !important;
        transition: background-color 0.2s ease-in-out !important;
    }

    .dropdown-item:hover {
        background-color: #f0f7ff !important;
    }

    .dropdown-item i {
        width: 20px !important;
        text-align: center !important;
        margin-right: 8px !important;
    }

    /* تحسين عرض القوائم المنسدلة للأعلى */
    .dropup .dropdown-menu {
        top: auto !important;
        bottom: 100% !important;
        margin-bottom: 0.125rem !important;
    }

    /* تأكيد ظهور القائمة المنسدلة فوق العناصر الأخرى */
    .dropdown-menu.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* تحسين مظهر أزرار المستندات */
    .document-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 3px;
        justify-content: flex-start;
    }

    .document-buttons .btn {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
        white-space: nowrap;
        transition: all 0.2s ease-in-out;
        border-radius: 4px;
        margin-bottom: 2px;
    }

    .document-buttons .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .document-buttons .btn i {
        margin-left: 3px;
        font-size: 0.7rem;
    }

    /* أنماط الأزرار المختلفة */
    .document-buttons .btn-info {
        background-color: #36b9cc;
        border-color: #36b9cc;
        color: white;
    }

    .document-buttons .btn-info:hover {
        background-color: #2a9aaa;
        border-color: #2a9aaa;
    }

    .document-buttons .btn-outline-primary {
        border-color: #4e73df;
        color: #4e73df;
    }

    .document-buttons .btn-outline-primary:hover {
        background-color: #4e73df;
        color: white;
    }

    .document-buttons .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
    }

    .document-buttons .btn-outline-secondary:hover {
        background-color: #6c757d;
        color: white;
    }

    .document-buttons .btn-outline-danger {
        border-color: #e74a3b;
        color: #e74a3b;
    }

    .document-buttons .btn-outline-danger:hover {
        background-color: #e74a3b;
        color: white;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تفعيل خاصية popover لعرض تفاصيل الأمر الإداري
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });

        // تعيين تاريخ اليوم كقيمة افتراضية لحقول تاريخ المباشرة
        var returnDateInputs = document.querySelectorAll('input[type="date"][name="return_date"]');
        var today = new Date().toISOString().split('T')[0];

        returnDateInputs.forEach(function(input) {
            input.value = today;
        });

        // تحسين عرض القوائم المنسدلة
        var dropdownButtons = document.querySelectorAll('.dropdown-toggle');
        dropdownButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                // التأكد من أن القائمة المنسدلة تظهر في المكان المناسب
                setTimeout(function() {
                    var dropdown = button.nextElementSibling;
                    if (dropdown) {
                        // التأكد من أن القائمة المنسدلة لا تتجاوز حدود الشاشة
                        var rect = dropdown.getBoundingClientRect();
                        if (rect.bottom > window.innerHeight) {
                            dropdown.classList.add('dropdown-menu-up');
                        }
                    }
                }, 0);
            });
        });
    });
</script>
{% endblock %}
