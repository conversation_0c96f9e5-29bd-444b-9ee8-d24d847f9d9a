import os
import sys
import socket
from flask import Flask, send_file, render_template_string

# قالب HTML بسيط
HTML = """
<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تشخيص المشكلة</title>
    <style>
        body { font-family: Arial; padding: 20px; }
        .success { color: green; }
        .error { color: red; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>تشخيص مشكلة عرض الصور</h1>
    
    <h2>معلومات النظام</h2>
    <pre>{{ system_info }}</pre>
    
    <h2>مجلدات التحميل</h2>
    <ul>
    {% for folder in folders %}
        <li>
            {{ folder.path }}: 
            {% if folder.exists %}
                <span class="success">موجود</span>
            {% else %}
                <span class="error">غير موجود</span>
            {% endif %}
        </li>
    {% endfor %}
    </ul>
    
    <h2>اختبار المنافذ</h2>
    <ul>
    {% for port in ports %}
        <li>
            المنفذ {{ port.number }}: 
            {% if port.available %}
                <span class="success">متاح</span>
            {% else %}
                <span class="error">مشغول</span>
            {% endif %}
        </li>
    {% endfor %}
    </ul>
    
    <h2>اختبار مكتبات معالجة الصور</h2>
    <ul>
    {% for lib in libraries %}
        <li>
            {{ lib.name }}: 
            {% if lib.installed %}
                <span class="success">مثبتة ({{ lib.version }})</span>
            {% else %}
                <span class="error">غير مثبتة</span>
            {% endif %}
        </li>
    {% endfor %}
    </ul>
    
    <h2>الخطوات التالية</h2>
    <p>بناءً على نتائج التشخيص، يرجى اتباع الخطوات التالية:</p>
    <ol>
    {% for step in next_steps %}
        <li>{{ step }}</li>
    {% endfor %}
    </ol>
</body>
</html>
"""

app = Flask(__name__)

@app.route('/')
def diagnose():
    # جمع معلومات النظام
    system_info = f"Python: {sys.version}\nOS: {sys.platform}"
    
    # التحقق من مجلدات التحميل
    folders_to_check = [
        'uploads',
        'uploads/documents',
        'uploads/scans',
        'backups'
    ]
    
    folders = []
    for folder in folders_to_check:
        folders.append({
            'path': folder,
            'exists': os.path.exists(folder)
        })
        
        # إنشاء المجلد إذا لم يكن موجوداً
        if not os.path.exists(folder):
            try:
                os.makedirs(folder)
                print(f"تم إنشاء المجلد: {folder}")
            except Exception as e:
                print(f"خطأ في إنشاء المجلد {folder}: {str(e)}")
    
    # التحقق من المنافذ
    ports_to_check = [5000, 5001, 8080]
    ports = []
    
    for port in ports_to_check:
        is_available = True
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.bind(('127.0.0.1', port))
            sock.close()
        except:
            is_available = False
        
        ports.append({
            'number': port,
            'available': is_available
        })
    
    # التحقق من مكتبات معالجة الصور
    libraries = []
    
    # التحقق من PIL/Pillow
    try:
        from PIL import Image, __version__ as pil_version
        libraries.append({
            'name': 'PIL/Pillow',
            'installed': True,
            'version': pil_version
        })
    except:
        libraries.append({
            'name': 'PIL/Pillow',
            'installed': False,
            'version': None
        })
    
    # التحقق من Flask
    try:
        import flask
        libraries.append({
            'name': 'Flask',
            'installed': True,
            'version': flask.__version__
        })
    except:
        libraries.append({
            'name': 'Flask',
            'installed': False,
            'version': None
        })
    
    # تحديد الخطوات التالية
    next_steps = []
    
    # التحقق من مجلدات التحميل
    if not all(folder['exists'] for folder in folders):
        next_steps.append("قم بإنشاء مجلدات التحميل المفقودة يدوياً")
    
    # التحقق من المنافذ
    if not any(port['available'] for port in ports):
        next_steps.append("جميع المنافذ مشغولة. قم بإغلاق البرامج التي تستخدم هذه المنافذ أو استخدم منفذ آخر")
    
    # التحقق من مكتبات معالجة الصور
    if not any(lib['installed'] for lib in libraries if lib['name'] == 'PIL/Pillow'):
        next_steps.append("قم بتثبيت مكتبة PIL/Pillow باستخدام الأمر: pip install Pillow")
    
    # إذا لم تكن هناك مشاكل واضحة
    if not next_steps:
        next_steps.append("قم بتشغيل التطبيق باستخدام الأمر: python app.py")
        next_steps.append("تأكد من أن مجلدات التحميل لديها صلاحيات القراءة والكتابة")
        next_steps.append("تحقق من سجلات الأخطاء في نافذة موجه الأوامر")
    
    return render_template_string(HTML, 
                                 system_info=system_info,
                                 folders=folders,
                                 ports=ports,
                                 libraries=libraries,
                                 next_steps=next_steps)

if __name__ == '__main__':
    # تشغيل التطبيق على منفذ متاح
    for port in [5000, 5001, 8080, 8000]:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.bind(('127.0.0.1', port))
            sock.close()
            print(f"تشغيل التطبيق على المنفذ {port}...")
            app.run(host='0.0.0.0', port=port)
            break
        except:
            print(f"المنفذ {port} مشغول، جاري المحاولة على منفذ آخر...")
