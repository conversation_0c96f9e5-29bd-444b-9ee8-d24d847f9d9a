from app import app, db
from models import Employee

def create_admin():
    with app.app_context():
        # التحقق من وجود مدير
        admin = Employee.query.filter_by(username='admin').first()
        if not admin:
            admin = Employee(
                username='admin',
                full_name='مدير النظام',
                work_location='المكتب الرئيسي',
                job_title='مدير النظام',
                is_admin=True,
                leave_balance=30,
                permissions='all'
            )
            admin.set_password('admin123')  # كلمة المرور الافتراضية
            
            try:
                db.session.add(admin)
                db.session.commit()
                print("تم إنشاء حساب المدير بنجاح")
                print("اسم المستخدم: admin")
                print("كلمة المرور: admin123")
            except Exception as e:
                db.session.rollback()
                print(f"حدث خطأ: {str(e)}")
        else:
            print("حساب المدير موجود بالفعل")

if __name__ == '__main__':
    # إنشاء جميع الجداول إذا لم تكن موجودة
    with app.app_context():
        db.create_all()
    create_admin()
