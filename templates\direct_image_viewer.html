<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ document_name }}</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #f0f0f0;
        }

        .toolbar {
            background-color: #0d47a1;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 100;
        }

        .toolbar h3 {
            margin: 0;
            font-size: 18px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 70%;
        }

        .toolbar-actions {
            display: flex;
            gap: 10px;
        }

        .toolbar a {
            color: white;
            text-decoration: none;
            padding: 6px 12px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .toolbar a:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .image-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: auto;
            padding: 20px;
        }

        /* تحديد حجم A4 بالبكسل (210mm × 297mm) */
        .a4-container {
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            display: block;
        }

        /* زر الطباعة بحجم A4 */
        .print-a4-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #0d47a1;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .print-a4-btn:hover {
            background-color: #0a3880;
        }

        /* زر حذف المستند */
        .delete-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background-color: #d32f2f;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .delete-btn:hover {
            background-color: #b71c1c;
        }

        @page {
            size: A4;
            margin: 0;
        }

        @media print {
            .toolbar, .print-a4-btn, .delete-btn {
                display: none !important;
            }

            body, html {
                margin: 0 !important;
                padding: 0 !important;
                height: 100%;
                background-color: white;
            }

            .image-container {
                height: 100%;
                padding: 0;
                margin: 0;
                display: block;
                overflow: visible;
            }

            .a4-container {
                box-shadow: none;
                margin: 0;
                padding: 0;
                width: 100%;
                height: 100%;
                page-break-inside: avoid;
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
                margin: 0;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="toolbar">
        <h3>{{ document_name }}</h3>
        <div class="toolbar-actions">
            <a href="javascript:window.history.back()">رجوع</a>
        </div>
    </div>
    <div class="image-container">
        <div class="a4-container">
            <img src="{{ document_url }}" alt="{{ document_name }}" onerror="handleImageError(this)">
        </div>
    </div>

    <script>
        function handleImageError(img) {
            // إظهار رسالة خطأ عند فشل تحميل الصورة
            img.style.display = 'none';
            var errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #d32f2f;">' +
                                '<i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 15px;"></i>' +
                                '<h3>حدث خطأ في معالجة الصورة</h3>' +
                                '<p>يرجى المحاولة مرة أخرى أو التواصل مع مسؤول النظام.</p>' +
                                '</div>';
            img.parentNode.appendChild(errorDiv);
        }
    </script>

    <!-- زر الطباعة -->
    <button class="print-a4-btn" onclick="window.print()" title="طباعة بحجم A4">
        <i class="fas fa-print"></i>
    </button>

    <!-- زر الحذف -->
    {% if document_id %}
    <a href="{{ url_for('delete_document', document_id=document_id) }}" class="delete-btn" onclick="return confirm('هل أنت متأكد من حذف هذا المستند؟');" title="حذف المستند">
        <i class="fas fa-trash-alt"></i>
    </a>
    {% endif %}

    <!-- تضمين Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
        integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
</body>
</html>
