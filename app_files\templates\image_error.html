<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطأ في معالجة الصورة</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
        }

        .toolbar {
            background-color: #d32f2f;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 100;
        }

        .toolbar h3 {
            margin: 0;
            font-size: 18px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 70%;
        }

        .toolbar-actions {
            display: flex;
            gap: 10px;
        }

        .toolbar a {
            color: white;
            text-decoration: none;
            padding: 6px 12px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .toolbar a:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .error-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
            text-align: center;
        }

        .error-icon {
            font-size: 72px;
            color: #d32f2f;
            margin-bottom: 20px;
        }

        .error-title {
            font-size: 24px;
            color: #333;
            margin-bottom: 10px;
        }

        .error-message {
            font-size: 16px;
            color: #666;
            max-width: 600px;
            line-height: 1.5;
        }

        .error-actions {
            margin-top: 30px;
        }

        .btn {
            display: inline-block;
            background-color: #0d47a1;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            text-decoration: none;
            transition: background-color 0.3s;
            margin: 0 10px;
        }

        .btn:hover {
            background-color: #0a3880;
        }
    </style>
</head>
<body>
    <div class="toolbar">
        <h3>خطأ في معالجة الصورة</h3>
        <div class="toolbar-actions">
            <a href="javascript:window.history.back()">رجوع</a>
        </div>
    </div>
    <div class="error-container">
        <div class="error-icon">⚠️</div>
        <h2 class="error-title">{{ error_title|default('حدث خطأ في معالجة الصورة') }}</h2>
        <p class="error-message">{{ error_message|default('حدث خطأ أثناء معالجة الصورة. يرجى المحاولة مرة أخرى أو التواصل مع مسؤول النظام.') }}</p>
        <div class="error-actions">
            <a href="{{ back_url|default('/') }}" class="btn">العودة للصفحة الرئيسية</a>
            <a href="javascript:window.history.back()" class="btn">الرجوع للصفحة السابقة</a>
        </div>
    </div>

    <!-- تضمين Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
        integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
</body>
</html>
