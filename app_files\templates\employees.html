{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2 class="mb-0">قائمة الموظفين <small class="text-muted">(باستثناء المدراء)</small></h2>
            <div>
                <a href="{{ url_for('add_employee') }}" class="btn btn-primary">إضافة موظف</a>
                <a href="{{ url_for('import_employees') }}" class="btn btn-success">استيراد من إكسل</a>
            </div>
        </div>
        <div class="card-body">
            <!-- البحث والتصفية المتقدمة -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-search me-2"></i> البحث والتصفية</h5>
                </div>
                <div class="card-body">
                    <form id="searchForm" action="{{ url_for('employees') }}" method="get">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group mb-3">
                                    <span class="input-group-text bg-primary text-white"><i class="fas fa-search"></i></span>
                                    <input type="text" name="search" class="form-control" placeholder="بحث بالاسم أو المسمى الوظيفي أو مكان العمل" value="{{ search_query }}">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i> بحث
                                    </button>
                                    {% if search_query %}
                                        <a href="{{ url_for('employees') }}" class="btn btn-outline-secondary">إلغاء البحث</a>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-3">
                                    <label for="departmentFilter" class="form-label">القسم:</label>
                                    <select id="departmentFilter" name="department" class="form-select">
                                        <option value="">جميع الأقسام</option>
                                        {% for dept in departments %}
                                            <option value="{{ dept }}" {% if department_filter == dept %}selected{% endif %}>{{ dept }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-3">
                                    <label for="locationFilter" class="form-label">مكان العمل:</label>
                                    <select id="locationFilter" name="location" class="form-select">
                                        <option value="">جميع المواقع</option>
                                        {% for loc in locations %}
                                            <option value="{{ loc }}" {% if location_filter == loc %}selected{% endif %}>{{ loc }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-success me-2">
                                <i class="fas fa-filter me-1"></i> تطبيق البحث والتصفية
                            </button>
                            <a href="{{ url_for('employees') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> إلغاء التصفية
                            </a>
                        </div>
                    </form>
                </div>
            </div>



            <!-- عرض النتائج -->
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>الاسم</th>
                            <th>المسمى الوظيفي</th>
                            <th>مكان العمل</th>
                            <th>القسم</th>
                            <th>رصيد الإجازات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if employees %}
                            {% for employee in employees %}
                            <tr>
                                <td>{{ employee.full_name }}</td>
                                <td>{{ employee.job_title }}</td>
                                <td>{{ employee.work_location }}</td>
                                <td>{{ employee.department or '-' }}</td>
                                <td>{{ employee.leave_balance }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('employee_leaves', employee_id=employee.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-calendar-alt"></i> عرض الإجازات
                                        </a>
                                        <a href="{{ url_for('add_leave', employee_id=employee.id) }}" class="btn btn-sm btn-success">
                                            <i class="fas fa-plus-circle"></i> إضافة إجازة
                                        </a>
                                        <a href="{{ url_for('edit_employee', employee_id=employee.id) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        {% if employee.id != session.get('employee_id') %}
                                            <a href="{{ url_for('delete_employee', employee_id=employee.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الموظف؟')">
                                                <i class="fas fa-trash"></i> حذف
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="6" class="text-center">لا يوجد موظفين{% if search_query %} مطابقين لعملية البحث{% endif %}</td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>

            <!-- إحصائيات -->
            <div class="mt-3">
                <p class="text-muted">إجمالي عدد الموظفين: {{ employees|length }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
