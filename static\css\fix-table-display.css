/**
 * أنماط لإصلاح عرض الجداول وأزرار الحذف في واجهة المستخدم
 */

/* إصلاح عرض الجداول */
.table {
    width: 100% !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
    border: 1px solid #ddd !important;
}

/* إصلاح عرض رؤوس الجداول */
.table thead th {
    background-color: #f0f0f0 !important;
    color: #333 !important;
    font-weight: 600 !important;
    border-bottom: 2px solid #ddd !important;
    text-align: right !important;
}

/* إصلاح عرض خلايا الجداول */
.table td, .table th {
    padding: 0.75rem !important;
    vertical-align: middle !important;
    border: 1px solid #ddd !important;
}

/* إصلاح عرض مجموعات الأزرار */
.btn-group {
    display: flex !important;
    flex-wrap: nowrap !important;
    gap: 5px !important;
}

/* إصلاح عرض الأزرار */
.btn {
    display: inline-block !important;
    margin: 0 2px !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* إصلاح عرض زر الحذف */
.btn-danger {
    background-color: #b71c1c !important;
    border-color: #b71c1c !important;
    color: white !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* إصلاح عرض زر التعديل */
.btn-warning {
    background-color: #e65100 !important;
    border-color: #e65100 !important;
    color: white !important;
    display: inline-block !important;
}

/* إصلاح عرض زر الصلاحيات */
.btn-info {
    background-color: #006064 !important;
    border-color: #006064 !important;
    color: white !important;
    display: inline-block !important;
}

/* إصلاح عرض الأيقونات داخل الأزرار */
.btn i {
    display: inline-block !important;
    margin-left: 5px !important;
}

/* إصلاح عرض خلية الإجراءات */
.table td:last-child {
    min-width: 200px !important;
    width: auto !important;
}

/* إصلاح عرض الشارات */
.badge {
    display: inline-block !important;
    padding: 0.25em 0.6em !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    border-radius: 4px !important;
}

/* إصلاح عرض القوائم غير المرتبة */
.list-unstyled {
    padding-right: 0 !important;
    list-style: none !important;
}

/* إصلاح عرض عناصر القوائم */
.list-unstyled li {
    margin-bottom: 3px !important;
}

/* إصلاح عرض الصفوف المتناوبة */
.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02) !important;
}

/* إصلاح عرض الصفوف عند التحويم */
.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05) !important;
}
