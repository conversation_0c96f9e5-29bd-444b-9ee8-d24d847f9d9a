<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>طباعة {{ document_name }}</title>
    <style>
        @page {
            size: A4;
            margin: 0;
        }

        body {
            margin: 0;
            padding: 0;
            width: 210mm;
            height: 297mm;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: white;
        }

        .print-container {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        img {
            max-width: 200mm;
            max-height: 287mm;
            object-fit: contain;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <img src="{{ document_url }}" alt="{{ document_name }}" onerror="handleImageError(this)">
    </div>

    <script>
        function handleImageError(img) {
            // إظهار رسالة خطأ عند فشل تحميل الصورة
            img.style.display = 'none';
            var errorDiv = document.createElement('div');
            errorDiv.style.padding = '20px';
            errorDiv.style.textAlign = 'center';
            errorDiv.style.color = '#d32f2f';
            errorDiv.innerHTML = '<h3>حدث خطأ في معالجة الصورة</h3>' +
                                '<p>يرجى المحاولة مرة أخرى أو التواصل مع مسؤول النظام.</p>';
            img.parentNode.appendChild(errorDiv);
        }

        // طباعة تلقائية عند فتح الصفحة
        window.onload = function() {
            // تأخير قليل للتأكد من تحميل الصورة
            setTimeout(function() {
                // التحقق من وجود خطأ قبل الطباعة
                if (!document.querySelector('.print-container div')) {
                    window.print();
                }
            }, 500);
        };
    </script>
</body>
</html>
