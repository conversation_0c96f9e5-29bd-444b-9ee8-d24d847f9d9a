{% extends 'base.html' %}

{% block styles %}
<style>
    .scanner-container {
        width: 100%;
        border: 2px solid #0d6efd;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        background-color: #f8f9fa;
    }

    .scanner-preview {
        width: 100%;
        height: 400px;
        border: 1px dashed #ccc;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        background-color: white;
        overflow: hidden;
    }

    .scanner-preview img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .scanner-controls {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }

    .scanner-btn {
        flex: 1;
        padding: 12px;
        font-size: 16px;
        font-weight: bold;
    }

    .scanner-icon {
        font-size: 64px;
        color: #0d6efd;
        margin-bottom: 15px;
    }

    .flash-effect {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: white;
        opacity: 0;
        pointer-events: none;
        animation: flash 0.5s;
    }

    @keyframes flash {
        0% { opacity: 0; }
        50% { opacity: 0.8; }
        100% { opacity: 0; }
    }

    .document-card {
        border: none;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .document-card .card-header {
        background-color: #0d6efd;
        color: white;
        font-weight: bold;
    }

    .adf-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .adf-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .adf-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }

    .adf-slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .adf-slider {
        background-color: #0d6efd;
    }

    input:focus + .adf-slider {
        box-shadow: 0 0 1px #0d6efd;
    }

    input:checked + .adf-slider:before {
        transform: translateX(26px);
    }

    .pdf-icon {
        font-size: 64px;
        color: #dc3545;
    }

    .multi-page-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .page-thumbnail {
        width: 150px;
        height: 200px;
        object-fit: cover;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="card document-card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2 class="mb-0"><i class="fas fa-scanner me-2"></i> مسح مستند ضوئيًا</h2>
            <a href="{{ url_for('employee_leaves', employee_id=employee.id) }}" class="btn btn-light btn-sm">
                <i class="fas fa-arrow-right me-1"></i> العودة
            </a>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title mb-3">معلومات الموظف</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>الاسم:</span>
                                    <strong>{{ employee.full_name }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>الرقم الوظيفي:</span>
                                    <strong>{{ employee.employee_number or '-' }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>المسمى الوظيفي:</span>
                                    <strong>{{ employee.job_title }}</strong>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title mb-3">معلومات الإجازة</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>نوع الإجازة:</span>
                                    <strong>{{ leave.leave_type.name }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>تاريخ البداية:</span>
                                    <strong>{{ leave.start_date.strftime('%Y-%m-%d') }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>تاريخ النهاية:</span>
                                    <strong>{{ leave.end_date.strftime('%Y-%m-%d') }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>المدة:</span>
                                    <strong>{{ leave.days_count }} يوم</strong>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <form method="POST" id="scan-form" action="{{ url_for('scan_document', leave_id=leave.id) }}" enctype="multipart/form-data">
                <input type="hidden" name="scanned_image" id="scanned_image">

                <div class="scanner-container">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h4 class="mb-3">اختر جهاز المسح الضوئي</h4>
                            <select id="scanner-select" class="form-select form-select-lg mb-3">
                                <option value="" selected disabled>-- جاري البحث عن أجهزة المسح الضوئي... --</option>
                            </select>
                            <button type="button" id="detect-scanners" class="btn btn-info w-100 mb-3">
                                <i class="fas fa-sync-alt me-2"></i> تحديث قائمة الأجهزة
                            </button>
                        </div>
                        <div class="col-md-6">
                            <h4 class="mb-3">إعدادات المسح الضوئي</h4>
                            <div class="d-flex align-items-center mb-3">
                                <label class="me-3 mb-0">استخدام التغذية التلقائية (ADF):</label>
                                <label class="adf-switch mb-0">
                                    <input type="checkbox" id="use_adf" name="use_adf" checked>
                                    <span class="adf-slider"></span>
                                </label>
                            </div>
                            <div class="form-group mb-3">
                                <label class="mb-2 fw-bold">صيغة الملف:</label>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="radio" name="file_format" id="format_jpg" value="jpg" checked>
                                    <label class="form-check-label" for="format_jpg">
                                        حفظ كصورة (JPG) - يمكن إضافة صفحات متعددة
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="file_format" id="format_pdf" value="pdf">
                                    <label class="form-check-label" for="format_pdf">
                                        حفظ كملف PDF - يدمج جميع الصفحات تلقائياً
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="scanner-preview" id="preview-container">
                        <div id="placeholder" class="text-center">
                            <i class="fas fa-file-image scanner-icon"></i>
                            <h4>جاهز للمسح الضوئي</h4>
                            <p class="text-muted">قم بتوصيل الماسح الضوئي واختر الجهاز من القائمة</p>
                        </div>
                        <div id="preview-content" style="display: none; width: 100%; height: 100%;"></div>
                    </div>

                    <div class="scanner-controls">
                        <button type="button" id="start-scan" class="btn btn-primary scanner-btn">
                            <i class="fas fa-scanner me-2"></i> بدء المسح الضوئي
                        </button>
                        <button type="button" id="retry-scan" class="btn btn-secondary scanner-btn" disabled>
                            <i class="fas fa-redo me-2"></i> إعادة المحاولة
                        </button>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">وصف المستند (اختياري)</label>
                        <input type="text" class="form-control" id="description" name="description" placeholder="مثال: أمر إداري، تقرير طبي، إلخ">
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ url_for('employee_leaves', employee_id=employee.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> إلغاء
                        </a>
                        <button type="submit" id="submit-scan" class="btn btn-primary" disabled>
                            <i class="fas fa-save me-1"></i> حفظ المستند
                        </button>
                    </div>
                </div>
            </form>

            <!-- عرض المستندات الحالية -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">المستندات الحالية</h5>
                </div>
                <div class="card-body">
                    {% if documents %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>اسم المستند</th>
                                        <th>النوع</th>
                                        <th>تاريخ الرفع</th>
                                        <th>الوصف</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for doc in documents %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ doc.file_name }}</td>
                                            <td>
                                                {% if doc.document_type == 'uploaded' %}
                                                    <span class="badge bg-primary">مرفوع</span>
                                                {% elif doc.document_type == 'scanned' %}
                                                    <span class="badge bg-info">ممسوح ضوئياً</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ doc.document_type }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ doc.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                            <td>{{ doc.description or '-' }}</td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{{ url_for('raw_document_by_document_id', document_id=doc.id) }}" class="btn btn-sm btn-info" target="_blank">
                                                        <i class="fas fa-eye"></i> عرض
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteDocModal{{ doc.id }}">
                                                        <i class="fas fa-trash-alt"></i> حذف
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- نوافذ منبثقة لتأكيد حذف المستندات -->
                        {% for doc in documents %}
                            <div class="modal fade" id="deleteDocModal{{ doc.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger text-white">
                                            <h5 class="modal-title">تأكيد حذف المستند</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>هل أنت متأكد من رغبتك في حذف هذا المستند؟</p>
                                            <p><strong>اسم المستند:</strong> {{ doc.file_name }}</p>
                                            <p class="text-danger"><strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <a href="{{ url_for('delete_document', document_id=doc.id) }}" class="btn btn-danger">تأكيد الحذف</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد مستندات مرفقة بهذه الإجازة.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // العناصر الرئيسية
        const scannerSelect = document.getElementById('scanner-select');
        const detectScannersBtn = document.getElementById('detect-scanners');
        const startScanBtn = document.getElementById('start-scan');
        const retryScanBtn = document.getElementById('retry-scan');
        const submitScanBtn = document.getElementById('submit-scan');
        const previewContainer = document.getElementById('preview-container');
        const placeholder = document.getElementById('placeholder');
        const previewContent = document.getElementById('preview-content');
        const scannedImageInput = document.getElementById('scanned_image');
        const useAdfCheckbox = document.getElementById('use_adf');

        // التحقق من توفر مكتبات المسح الضوئي
        const wiaAvailable = {{ wia_available|tojson }};

        // البحث عن أجهزة المسح الضوئي تلقائيًا عند تحميل الصفحة
        setTimeout(() => detectScannersBtn.click(), 500);

        // البحث عن أجهزة المسح الضوئي
        detectScannersBtn.addEventListener('click', async function() {
            // تفريغ القائمة أولاً
            scannerSelect.innerHTML = '<option value="" selected disabled>جاري البحث عن أجهزة المسح الضوئي...</option>';

            // عرض مؤشر التحميل
            placeholder.innerHTML = `
                <div class="spinner-border text-primary" role="status"></div>
                <h4 class="mt-3">جاري البحث عن أجهزة المسح الضوئي...</h4>
            `;

            try {
                // استخدام واجهة برمجة التطبيقات للحصول على قائمة أجهزة المسح الضوئي
                const response = await fetch('/get_available_scanners');
                const data = await response.json();

                if (data.success && data.scanners.length > 0) {
                    // إضافة أجهزة المسح المتوفرة إلى القائمة
                    scannerSelect.innerHTML = '<option value="" selected disabled>-- اختر جهاز المسح الضوئي --</option>';

                    data.scanners.forEach(scanner => {
                        const option = document.createElement('option');
                        option.value = scanner.id;
                        option.textContent = scanner.name;
                        // Store the source as a data attribute
                        option.dataset.source = scanner.source || 'unknown'; // Add source
                        scannerSelect.appendChild(option);
                    });

                    // عرض رسالة نجاح
                    placeholder.innerHTML = `
                        <i class="fas fa-check-circle text-success" style="font-size: 64px;"></i>
                        <h4 class="mt-3">تم العثور على ${data.scanners.length} جهاز مسح ضوئي</h4>
                        <p>اختر الجهاز من القائمة واضغط على "بدء المسح الضوئي"</p>
                    `;
                } else {
                    // إذا لم يتم العثور على أجهزة مسح ضوئي
                    scannerSelect.innerHTML = '<option value="" selected disabled>-- لم يتم العثور على أجهزة --</option>';

                    placeholder.innerHTML = `
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 64px;"></i>
                        <h4 class="mt-3">لم يتم العثور على أجهزة مسح ضوئي</h4>
                        <p>تأكد من توصيل الماسح الضوئي بشكل صحيح ثم اضغط على "تحديث قائمة الأجهزة"</p>
                    `;
                }
            } catch (error) {
                console.error('خطأ في البحث عن أجهزة المسح الضوئي:', error);

                scannerSelect.innerHTML = '<option value="" selected disabled>-- حدث خطأ --</option>';

                placeholder.innerHTML = `
                    <i class="fas fa-times-circle text-danger" style="font-size: 64px;"></i>
                    <h4 class="mt-3">حدث خطأ أثناء البحث عن أجهزة المسح الضوئي</h4>
                    <p>يرجى المحاولة مرة أخرى</p>
                `;
            }
        });

        // بدء المسح الضوئي
        startScanBtn.addEventListener('click', async function() {
            const selectedOption = scannerSelect.options[scannerSelect.selectedIndex]; // Get selected option element
            const selectedScannerId = selectedOption.value;
            const selectedScannerSource = selectedOption.dataset.source; // Get source from data attribute

            if (!selectedScannerId) {
                alert('يرجى اختيار جهاز مسح ضوئي أولاً');
                return;
            }

            // تعطيل الأزرار أثناء المسح
            startScanBtn.disabled = true;
            detectScannersBtn.disabled = true;

            // عرض مؤشر التحميل
            placeholder.style.display = 'flex';
            previewContent.style.display = 'none';
            placeholder.innerHTML = `
                <div class="spinner-border text-primary" role="status"></div>
                <h4 class="mt-3">جاري المسح الضوئي...</h4>
                <p>يرجى الانتظار</p>
            `;

            // إضافة تأثير الفلاش
            const flashEffect = document.createElement('div');
            flashEffect.className = 'flash-effect';
            previewContainer.appendChild(flashEffect);

            // إزالة تأثير الفلاش بعد انتهاء الرسوم المتحركة
            setTimeout(() => {
                if (previewContainer.contains(flashEffect)) {
                    previewContainer.removeChild(flashEffect);
                }
            }, 500);

            try {
                // استخدام واجهة برمجة التطبيقات للمسح الضوئي
                const useAdf = useAdfCheckbox.checked;

                const response = await fetch('/scan_with_device', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        scanner_id: selectedScannerId, // Use ID from option value
                        scanner_source: selectedScannerSource, // *** ADDED SOURCE ***
                        dpi: 300,
                        mode: 'RGB',
                        use_adf: useAdf,
                        file_format: document.querySelector('input[name="file_format"]:checked').value
                    }),
                });

                const data = await response.json();

                if (data.success) {
                    // إخفاء مؤشر التحميل
                    placeholder.style.display = 'none';
                    previewContent.style.display = 'flex';

                    // التحقق من نوع النتيجة
                    console.log("بيانات المسح الضوئي:", data);

                    // محاولة تحليل البيانات إذا كانت نصية
                    if (typeof data === 'string') {
                        try {
                            data = JSON.parse(data);
                            console.log("تم تحليل البيانات كـ JSON:", data);
                        } catch (e) {
                            console.log("البيانات ليست بتنسيق JSON صالح");
                        }
                    }

                    if (data.is_pdf || (data.image_data && data.image_data.startsWith('data:application/pdf'))) {
                        // عرض أيقونة PDF
                        previewContent.innerHTML = `
                            <div class="text-center">
                                <i class="fas fa-file-pdf pdf-icon"></i>
                                <h4 class="mt-3">تم إنشاء ملف PDF</h4>
                                <p>تم مسح المستند ضوئياً وإنشاء ملف PDF</p>
                            </div>
                        `;

                        // تخزين بيانات PDF في النموذج
                        scannedImageInput.value = data.image_url || data.image_data;
                    } else if (data.is_multi_page && data.images && data.images.length > 0) {
                        // عرض الصور المتعددة
                        previewContent.innerHTML = `
                            <div class="multi-page-container">
                                <h4>تم مسح ${data.images.length} صفحة</h4>
                                <div class="d-flex flex-wrap justify-content-center gap-3 mb-3">
                                    ${data.images.map((img, index) => `
                                        <div class="text-center">
                                            <img src="${img.url}" class="page-thumbnail" alt="الصفحة ${index + 1}">
                                            <p class="small">الصفحة ${index + 1}</p>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `;

                        // تخزين بيانات الصور في النموذج
                        scannedImageInput.value = JSON.stringify({
                            type: 'multi_page',
                            images: data.images,
                            group_id: data.group_id
                        });

                        // إضافة زر لمسح صفحات إضافية
                        const addMoreBtn = document.createElement('button');
                        addMoreBtn.type = 'button';
                        addMoreBtn.className = 'btn btn-success mt-3 w-100';
                        addMoreBtn.style.fontSize = '16px';
                        addMoreBtn.style.padding = '10px';
                        addMoreBtn.innerHTML = '<i class="fas fa-plus-circle me-2"></i> إضافة صفحات أخرى';
                        addMoreBtn.onclick = function() {
                            // إعادة تفعيل زر المسح
                            startScanBtn.disabled = false;
                            retryScanBtn.disabled = false;
                            // الاحتفاظ بالصور الحالية
                            const currentImages = JSON.parse(scannedImageInput.value);
                            // تخزين الصور الحالية في متغير عام
                            window.currentScanGroup = currentImages;
                            // إظهار رسالة للمستخدم
                            alert('يمكنك الآن إضافة صفحات أخرى. اضغط على زر "بدء المسح الضوئي" مرة أخرى.');
                        };

                        // إضافة الزر إلى العنصر
                        previewContent.appendChild(document.createElement('div')).appendChild(addMoreBtn);
                    } else if (typeof data === 'string' && data.startsWith('{')) {
                        // محاولة تحليل البيانات كـ JSON
                        try {
                            const jsonData = JSON.parse(data);
                            if (jsonData.is_multi_page && jsonData.images && jsonData.images.length > 0) {
                                // عرض الصور المتعددة
                                previewContent.innerHTML = `
                                    <div class="multi-page-container">
                                        <h4>تم مسح ${jsonData.images.length} صفحة</h4>
                                        <div class="d-flex flex-wrap justify-content-center gap-3 mb-3">
                                            ${jsonData.images.map((img, index) => `
                                                <div class="text-center">
                                                    <img src="${img.url}" class="page-thumbnail" alt="الصفحة ${index + 1}">
                                                    <p class="small">الصفحة ${index + 1}</p>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                `;

                                // تخزين بيانات الصور في النموذج
                                scannedImageInput.value = data;

                                // إضافة زر لمسح صفحات إضافية
                                const addMoreBtn = document.createElement('button');
                                addMoreBtn.type = 'button';
                                addMoreBtn.className = 'btn btn-success mt-3';
                                addMoreBtn.innerHTML = '<i class="fas fa-plus-circle me-2"></i> إضافة صفحات أخرى';
                                addMoreBtn.onclick = function() {
                                    // إعادة تفعيل زر المسح
                                    startScanBtn.disabled = false;
                                    // الاحتفاظ بالصور الحالية
                                    const currentImages = JSON.parse(scannedImageInput.value);
                                    // تخزين الصور الحالية في متغير عام
                                    window.currentScanGroup = currentImages;
                                };

                                previewContent.appendChild(addMoreBtn);
                            } else {
                                // عرض صورة واحدة
                                const imageUrl = jsonData.image_url || jsonData.image_data;
                                previewContent.innerHTML = `<img src="${imageUrl}" class="img-fluid" alt="صورة ممسوحة ضوئياً">`;

                                // تخزين بيانات الصورة في النموذج
                                scannedImageInput.value = imageUrl;
                            }
                        } catch (e) {
                            // إذا فشل التحليل، عرض البيانات كما هي
                            const imageUrl = data;
                            previewContent.innerHTML = `<img src="${imageUrl}" class="img-fluid" alt="صورة ممسوحة ضوئياً">`;

                            // تخزين بيانات الصورة في النموذج
                            scannedImageInput.value = imageUrl;
                        }
                    } else {
                        // عرض صورة واحدة
                        const imageUrl = data.image_url || data.image_data;
                        previewContent.innerHTML = `<img src="${imageUrl}" class="img-fluid" alt="صورة ممسوحة ضوئياً">`;

                        // تخزين بيانات الصورة في النموذج
                        scannedImageInput.value = imageUrl;
                    }

                    // تفعيل أزرار الحفظ وإعادة المحاولة
                    submitScanBtn.disabled = false;
                    retryScanBtn.disabled = false;

                    // إضافة رسالة نجاح
                    const successMessage = document.createElement('div');
                    successMessage.className = 'alert alert-success mt-3';
                    successMessage.innerHTML = '<i class="fas fa-check-circle me-2"></i> تم المسح الضوئي بنجاح. اضغط على زر "حفظ" لحفظ المستند.';
                    previewContainer.insertAdjacentElement('afterend', successMessage);

                    // إزالة الرسالة بعد 5 ثوانٍ
                    setTimeout(() => {
                        if (previewContainer.nextElementSibling === successMessage) {
                            successMessage.remove();
                        }
                    }, 5000);
                } else {
                    // إذا حدث خطأ في المسح الضوئي
                    console.error('خطأ في المسح الضوئي:', data.error);

                    placeholder.innerHTML = `
                        <i class="fas fa-times-circle text-danger" style="font-size: 64px;"></i>
                        <h4 class="mt-3">حدث خطأ أثناء المسح الضوئي</h4>
                        <p>${data.error || 'يرجى المحاولة مرة أخرى'}</p>
                    `;

                    // إعادة تفعيل الأزرار
                    startScanBtn.disabled = false;
                    detectScannersBtn.disabled = false;
                }
            } catch (error) {
                console.error('خطأ في المسح الضوئي:', error);

                placeholder.innerHTML = `
                    <i class="fas fa-times-circle text-danger" style="font-size: 64px;"></i>
                    <h4 class="mt-3">حدث خطأ أثناء المسح الضوئي</h4>
                    <p>يرجى المحاولة مرة أخرى</p>
                `;

                // إعادة تفعيل الأزرار
                startScanBtn.disabled = false;
                detectScannersBtn.disabled = false;
            }
        });

        // إعادة المحاولة
        retryScanBtn.addEventListener('click', function() {
            // إعادة تعيين الحالة
            placeholder.style.display = 'flex';
            previewContent.style.display = 'none';
            placeholder.innerHTML = `
                <i class="fas fa-file-image scanner-icon"></i>
                <h4>جاهز للمسح الضوئي</h4>
                <p class="text-muted">قم بتوصيل الماسح الضوئي واختر الجهاز من القائمة</p>
            `;

            // إعادة تعيين النموذج
            scannedImageInput.value = '';

            // إعادة تعيين الأزرار
            startScanBtn.disabled = false;
            detectScannersBtn.disabled = false;
            submitScanBtn.disabled = true;
            retryScanBtn.disabled = true;

            // إزالة رسائل النجاح
            const successMessage = previewContainer.nextElementSibling;
            if (successMessage && successMessage.classList.contains('alert-success')) {
                successMessage.remove();
            }
        });
    });
</script>
{% endblock %}
