from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timezone

db = SQLAlchemy()

class Employee(db.Model):
    __tablename__ = 'employee'

    # إضافة فهارس للحقول المستخدمة في البحث
    __table_args__ = (
        db.Index('idx_employee_full_name', 'full_name'),
        db.Index('idx_employee_job_title', 'job_title'),
        db.Index('idx_employee_work_location', 'work_location'),
        db.Index('idx_employee_employee_number', 'employee_number'),
        db.Index('idx_employee_department', 'department'),
    )

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True)
    password_hash = db.Column(db.String(120))
    full_name = db.Column(db.String(120))
    employee_number = db.Column(db.String(50), nullable=True, unique=True)  # الرقم الوظيفي
    department = db.Column(db.String(120), nullable=True)
    job_title = db.Column(db.String(120))
    work_location = db.Column(db.String(120))
    leave_balance = db.Column(db.Integer, default=36)
    is_admin = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    permissions = db.Column(db.String(255))
    children_count = db.Column(db.Integer, default=0)  # عدد الأطفال (مهم لإجازات الأمومة)

    leave_requests = db.relationship('LeaveRequest', backref='employee', lazy=True)  # Removed the second argument

    def __init__(self, **kwargs):
        super(Employee, self).__init__(**kwargs)
        if self.leave_balance is None:
            self.leave_balance = 36
        if self.is_active is None:
            self.is_active = True

    def set_password(self, password):
        if password:
            self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        if self.password_hash and password:
            return check_password_hash(self.password_hash, password)
        return False

    def has_permission(self, permission):
        if self.is_admin:
            return True
        if not self.permissions:
            return False
        return permission in self.permissions.split(',')

    def __repr__(self):
        return f'<Employee {self.full_name}>'



class LeaveType(db.Model):
    __tablename__ = 'leave_type'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    days_allowed = db.Column(db.Integer)
    description = db.Column(db.String(200))

class Document(db.Model):
    """نموذج المستندات المرفقة بالإجازات"""
    __tablename__ = 'document'
    id = db.Column(db.Integer, primary_key=True)
    leave_id = db.Column(db.Integer, db.ForeignKey('leave_request.id', ondelete='CASCADE'), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)  # مسار الملف
    file_name = db.Column(db.String(100), nullable=False)  # اسم الملف الأصلي
    file_type = db.Column(db.String(50), nullable=True)  # نوع الملف
    document_type = db.Column(db.String(50), nullable=True)  # نوع المستند (مرفوع/ممسوح ضوئياً)
    upload_date = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))  # تاريخ الرفع
    description = db.Column(db.String(200), nullable=True)  # وصف المستند

    def __repr__(self):
        return f'<Document {self.file_name}>'

class LeaveRequest(db.Model):
    __tablename__ = 'leave_request'
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    leave_type_id = db.Column(db.Integer, db.ForeignKey('leave_type.id'), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    days_count = db.Column(db.Integer, nullable=False)  # مدة الإجازة بالأيام
    departure_date = db.Column(db.Date, nullable=True)  # تاريخ الانفكاك
    return_date = db.Column(db.Date, nullable=True)  # تاريخ المباشرة
    children_count = db.Column(db.Integer, default=0)  # عدد الأطفال (للإجازات المتعلقة بالأمومة)

    # حقول الأمر الإداري
    order_date = db.Column(db.Date, nullable=True)  # تاريخ الأمر الإداري
    order_number = db.Column(db.String(50), nullable=True)  # رقم الأمر الإداري
    order_source = db.Column(db.String(100), nullable=True)  # الجهة المصدرة للأمر

    # حقول المستندات (للتوافق مع الإصدارات السابقة)
    document_path = db.Column(db.String(255), nullable=True)  # مسار ملف الكتاب/المستند
    document_name = db.Column(db.String(100), nullable=True)  # اسم الملف الأصلي
    document_type = db.Column(db.String(50), nullable=True)  # نوع المستند (مرفوع/ممسوح ضوئياً)
    document_upload_date = db.Column(db.DateTime, nullable=True)  # تاريخ رفع المستند

    comment = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    # العلاقات
    leave_type = db.relationship('LeaveType', backref='leave_requests')
    documents = db.relationship('Document', backref='leave_request', lazy=True, cascade='all, delete-orphan')

















