from flask import Flask, send_file, render_template, request, abort
import os
import mimetypes

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('viewer_index.html')

@app.route('/view')
def view_image():
    file_path = request.args.get('path')
    
    if not file_path or not os.path.exists(file_path):
        return render_template('viewer_error.html', 
                              error_message="الملف غير موجود أو تم حذفه")
    
    # تحديد نوع الملف
    mime_type, _ = mimetypes.guess_type(file_path)
    
    # التحقق من أن الملف هو صورة أو PDF
    if mime_type and (mime_type.startswith('image/') or mime_type == 'application/pdf'):
        return render_template('viewer.html', file_path=file_path, mime_type=mime_type)
    else:
        return render_template('viewer_error.html', 
                              error_message="نوع الملف غير مدعوم")

@app.route('/raw')
def raw_file():
    file_path = request.args.get('path')
    
    if not file_path or not os.path.exists(file_path):
        abort(404)
    
    # تحديد نوع الملف
    mime_type, _ = mimetypes.guess_type(file_path)
    
    # إرسال الملف
    return send_file(file_path, mimetype=mime_type)

@app.errorhandler(404)
def page_not_found(e):
    return render_template('viewer_error.html', error_message="الصفحة غير موجودة"), 404

@app.errorhandler(500)
def server_error(e):
    return render_template('viewer_error.html', error_message="حدث خطأ في الخادم"), 500

if __name__ == '__main__':
    # إنشاء مجلد templates إذا لم يكن موجوداً
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    # تشغيل التطبيق على المنفذ 5001
    app.run(host='0.0.0.0', port=5001, debug=True)
