"""
وحدة للكشف عن الماسحات الضوئية باستخدام طرق متعددة
"""
import sys
import os
import subprocess
import re
import json
import traceback
import pythoncom

def get_wmi_scanners():
    """
    الحصول على قائمة بالماسحات الضوئية باستخدام WMI
    """
    scanners = []
    try:
        # استخدام WMI للحصول على قائمة بالماسحات الضوئية
        print("جاري البحث عن الماسحات الضوئية باستخدام WMI...")
        command = 'powershell "Get-WmiObject Win32_PnPEntity | Where-Object{$_.PNPClass -eq \'Image\' -or $_.Name -like \'*scanner*\' -or $_.Name -like \'*TWAIN*\' -or $_.Name -like \'*CANON*\' -or $_.Name -like \'*HP*\' -or $_.Name -like \'*EPSON*\'} | Select-Object Name, DeviceID | ConvertTo-Json"'
        result = subprocess.run(command, shell=True, capture_output=True, text=True)

        if result.returncode == 0 and result.stdout.strip():
            # تحليل مخرجات JSON
            try:
                output = result.stdout.strip()
                print(f"تم الحصول على مخرجات من WMI: {output[:200]}...")

                # التعامل مع حالة وجود جهاز واحد فقط
                if output.startswith('{') and output.endswith('}'):
                    device = json.loads(output)
                    scanner_info = {
                        'id': '0',
                        'name': device.get('Name', 'جهاز غير معروف'),
                        'device_id': device.get('DeviceID', 'unknown'),
                        'source': 'wmi'
                    }
                    scanners.append(scanner_info)
                    print(f"تم العثور على ماسح ضوئي باستخدام WMI: {scanner_info['name']}")
                else:
                    try:
                        devices = json.loads(output)
                        if isinstance(devices, list):
                            for i, device in enumerate(devices):
                                scanner_info = {
                                    'id': str(i),
                                    'name': device.get('Name', 'جهاز غير معروف'),
                                    'device_id': device.get('DeviceID', 'unknown'),
                                    'source': 'wmi'
                                }
                                scanners.append(scanner_info)
                                print(f"تم العثور على ماسح ضوئي باستخدام WMI: {scanner_info['name']}")
                        else:
                            print(f"مخرجات WMI ليست قائمة: {type(devices)}")
                    except Exception as parse_error:
                        print(f"خطأ في تحليل مخرجات JSON من WMI (قائمة): {str(parse_error)}")
            except json.JSONDecodeError as json_error:
                print(f"خطأ في تحليل مخرجات JSON من WMI: {str(json_error)}")
                print(f"المخرجات: {result.stdout}")
        else:
            print("لم يتم العثور على أجهزة باستخدام WMI أو حدث خطأ")
            print(f"رمز الخروج: {result.returncode}")
            print(f"الخطأ: {result.stderr}")
    except Exception as e:
        print(f"خطأ في الحصول على الماسحات الضوئية باستخدام WMI: {str(e)}")
        traceback.print_exc()

    return scanners

def get_device_manager_scanners():
    """
    الحصول على قائمة بالماسحات الضوئية باستخدام مدير الأجهزة
    """
    scanners = []
    try:
        # استخدام مدير الأجهزة للحصول على قائمة بالماسحات الضوئية
        print("جاري البحث عن الماسحات الضوئية باستخدام مدير الأجهزة...")
        command = 'powershell "Get-PnpDevice -Class \'Image\' | Select-Object FriendlyName, InstanceId | ConvertTo-Json"'
        result = subprocess.run(command, shell=True, capture_output=True, text=True)

        if result.returncode == 0 and result.stdout.strip():
            # تحليل مخرجات JSON
            try:
                output = result.stdout.strip()
                print(f"تم الحصول على مخرجات من مدير الأجهزة: {output[:200]}...")

                # التعامل مع حالة وجود جهاز واحد فقط
                if output.startswith('{') and output.endswith('}'):
                    device = json.loads(output)
                    scanner_info = {
                        'id': '0',
                        'name': device.get('FriendlyName', 'جهاز غير معروف'),
                        'device_id': device.get('InstanceId', 'unknown'),
                        'source': 'device_manager'
                    }
                    scanners.append(scanner_info)
                    print(f"تم العثور على ماسح ضوئي باستخدام مدير الأجهزة: {scanner_info['name']}")
                else:
                    try:
                        devices = json.loads(output)
                        if isinstance(devices, list):
                            for i, device in enumerate(devices):
                                scanner_info = {
                                    'id': str(i),
                                    'name': device.get('FriendlyName', 'جهاز غير معروف'),
                                    'device_id': device.get('InstanceId', 'unknown'),
                                    'source': 'device_manager'
                                }
                                scanners.append(scanner_info)
                                print(f"تم العثور على ماسح ضوئي باستخدام مدير الأجهزة: {scanner_info['name']}")
                        else:
                            print(f"مخرجات مدير الأجهزة ليست قائمة: {type(devices)}")
                    except Exception as parse_error:
                        print(f"خطأ في تحليل مخرجات JSON من مدير الأجهزة (قائمة): {str(parse_error)}")
            except json.JSONDecodeError as json_error:
                print(f"خطأ في تحليل مخرجات JSON من مدير الأجهزة: {str(json_error)}")
                print(f"المخرجات: {result.stdout}")
        else:
            print("لم يتم العثور على أجهزة باستخدام مدير الأجهزة أو حدث خطأ")
            print(f"رمز الخروج: {result.returncode}")
            print(f"الخطأ: {result.stderr}")
    except Exception as e:
        print(f"خطأ في الحصول على الماسحات الضوئية باستخدام مدير الأجهزة: {str(e)}")
        traceback.print_exc()

    # محاولة استخدام طريقة بديلة إذا لم يتم العثور على أي ماسح ضوئي
    if not scanners:
        try:
            print("محاولة استخدام طريقة بديلة للبحث عن الماسحات الضوئية...")
            command = 'powershell "Get-PnpDevice | Where-Object {$_.FriendlyName -like \'*scan*\' -or $_.FriendlyName -like \'*CANON*\' -or $_.FriendlyName -like \'*HP*\' -or $_.FriendlyName -like \'*EPSON*\'} | Select-Object FriendlyName, InstanceId | ConvertTo-Json"'
            result = subprocess.run(command, shell=True, capture_output=True, text=True)

            if result.returncode == 0 and result.stdout.strip():
                try:
                    output = result.stdout.strip()
                    print(f"تم الحصول على مخرجات من الطريقة البديلة: {output[:200]}...")

                    if output.startswith('{') and output.endswith('}'):
                        device = json.loads(output)
                        scanner_info = {
                            'id': '0',
                            'name': device.get('FriendlyName', 'جهاز غير معروف'),
                            'device_id': device.get('InstanceId', 'unknown'),
                            'source': 'device_manager_alt'
                        }
                        scanners.append(scanner_info)
                        print(f"تم العثور على ماسح ضوئي باستخدام الطريقة البديلة: {scanner_info['name']}")
                    else:
                        devices = json.loads(output)
                        if isinstance(devices, list):
                            for i, device in enumerate(devices):
                                scanner_info = {
                                    'id': str(i),
                                    'name': device.get('FriendlyName', 'جهاز غير معروف'),
                                    'device_id': device.get('InstanceId', 'unknown'),
                                    'source': 'device_manager_alt'
                                }
                                scanners.append(scanner_info)
                                print(f"تم العثور على ماسح ضوئي باستخدام الطريقة البديلة: {scanner_info['name']}")
                except Exception as alt_error:
                    print(f"خطأ في تحليل مخرجات الطريقة البديلة: {str(alt_error)}")
        except Exception as alt_method_error:
            print(f"خطأ في استخدام الطريقة البديلة: {str(alt_method_error)}")

    return scanners

def get_twain_scanners():
    """
    الحصول على قائمة بالماسحات الضوئية باستخدام TWAIN
    """
    scanners = []
    try:
        # محاولة استيراد مكتبة TWAIN
        import twain

        # إنشاء مصدر TWAIN
        sm = twain.SourceManager(0)

        # الحصول على قائمة بالمصادر
        sources = sm.GetSourceList()

        # إضافة المصادر إلى القائمة
        for i, source in enumerate(sources):
            scanners.append({
                'id': str(i),
                'name': source,
                'device_id': f"twain_{i}",
                'source': 'twain'
            })
    except ImportError:
        print("مكتبة TWAIN غير متوفرة")
    except Exception as e:
        print(f"خطأ في الحصول على الماسحات الضوئية باستخدام TWAIN: {str(e)}")
        traceback.print_exc()

    return scanners

def get_wia_scanners():
    """
    الحصول على قائمة بالماسحات الضوئية باستخدام WIA
    """
    scanners = []
    try:
        # تهيئة COM قبل استخدام win32com
        pythoncom.CoInitialize()

        # محاولة استيراد مكتبة win32com
        import win32com.client

        # إنشاء مدير أجهزة WIA
        device_manager = win32com.client.Dispatch("WIA.DeviceManager")

        # الحصول على مجموعة الأجهزة
        devices = device_manager.DeviceInfos

        # طباعة معلومات تشخيصية
        print(f"عدد الأجهزة المكتشفة في WIA: {devices.Count}")

        # إنشاء قائمة بالماسحات الضوئية
        for i in range(1, devices.Count + 1):
            try:
                device_info = devices.Item(i)
                # طباعة معلومات تشخيصية عن الجهاز
                device_type = device_info.Type
                device_name = device_info.Properties("Name").Value
                print(f"جهاز WIA {i}: النوع={device_type}, الاسم={device_name}")

                # التحقق مما إذا كان الجهاز ماسحًا ضوئيًا
                if device_type == 1:  # 1 = ماسح ضوئي
                    # محاولة الحصول على معلومات إضافية
                    device_id = ""
                    try:
                        device_id = device_info.DeviceID
                    except:
                        device_id = f"wia_{i-1}"

                    scanners.append({
                        'id': str(i-1),
                        'name': device_name,
                        'device_id': device_id,
                        'source': 'wia'
                    })
                    print(f"تم إضافة ماسح ضوئي WIA: {device_name}, المعرف: {i-1}")
                else:
                    # إضافة أي جهاز قد يكون ماسحًا ضوئيًا
                    if "scan" in device_name.lower() or "hp" in device_name.lower() or "canon" in device_name.lower() or "epson" in device_name.lower():
                        scanners.append({
                            'id': str(i-1),
                            'name': f"{device_name} (قد يكون ماسحًا ضوئيًا)",
                            'device_id': f"possible_wia_{i-1}",
                            'source': 'wia'
                        })
                        print(f"تم إضافة جهاز WIA محتمل: {device_name}, المعرف: {i-1}")
            except Exception as e:
                print(f"خطأ في معالجة جهاز WIA {i}: {str(e)}")
    except ImportError:
        print("مكتبة win32com غير متوفرة")
    except Exception as e:
        print(f"خطأ في الحصول على الماسحات الضوئية باستخدام WIA: {str(e)}")
        traceback.print_exc()

    return scanners

def get_direct_powershell_scanners():
    """
    الحصول على قائمة بالماسحات الضوئية باستخدام PowerShell مباشرة
    """
    scanners = []
    try:
        print("جاري البحث عن الماسحات الضوئية باستخدام PowerShell مباشرة...")

        # استخدام أمر PowerShell للبحث عن الماسحات الضوئية بطريقة أكثر شمولية
        command = '''
        powershell "
        # البحث عن أجهزة المسح الضوئي باستخدام سجل النظام
        $scanners = @()

        # البحث في سجل النظام عن أجهزة المسح الضوئي
        $regPath = 'HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Class\\{6bdd1fc6-810f-11d0-bec7-08002be2092f}'
        if (Test-Path $regPath) {
            Get-ChildItem $regPath | ForEach-Object {
                $deviceDesc = $null
                try {
                    $deviceDesc = Get-ItemProperty -Path $_.PSPath -Name 'DeviceDesc' -ErrorAction SilentlyContinue
                } catch {}

                if ($deviceDesc -and $deviceDesc.DeviceDesc) {
                    $scanners += @{
                        'Name' = $deviceDesc.DeviceDesc;
                        'DeviceID' = $_.PSChildName;
                        'Source' = 'Registry'
                    }
                }
            }
        }

        # البحث عن أجهزة WIA
        try {
            $wia = New-Object -ComObject WIA.DeviceManager
            foreach ($device in $wia.DeviceInfos) {
                if ($device.Type -eq 1) {
                    $scanners += @{
                        'Name' = $device.Properties('Name').Value;
                        'DeviceID' = $device.DeviceID;
                        'Source' = 'WIA'
                    }
                }
            }
        } catch {}

        # تحويل النتائج إلى JSON
        ConvertTo-Json -InputObject $scanners
        "
        '''

        result = subprocess.run(command, shell=True, capture_output=True, text=True)

        if result.returncode == 0 and result.stdout.strip():
            try:
                output = result.stdout.strip()
                print(f"تم الحصول على مخرجات من PowerShell: {output[:200]}...")

                if output.startswith('[') and output.endswith(']'):
                    devices = json.loads(output)
                    for i, device in enumerate(devices):
                        scanner_info = {
                            'id': str(i),
                            'name': device.get('Name', 'جهاز غير معروف'),
                            'device_id': device.get('DeviceID', 'unknown'),
                            'source': device.get('Source', 'powershell')
                        }
                        scanners.append(scanner_info)
                        print(f"تم العثور على ماسح ضوئي باستخدام PowerShell: {scanner_info['name']}")
                elif output.startswith('{') and output.endswith('}'):
                    device = json.loads(output)
                    scanner_info = {
                        'id': '0',
                        'name': device.get('Name', 'جهاز غير معروف'),
                        'device_id': device.get('DeviceID', 'unknown'),
                        'source': device.get('Source', 'powershell')
                    }
                    scanners.append(scanner_info)
                    print(f"تم العثور على ماسح ضوئي باستخدام PowerShell: {scanner_info['name']}")
            except Exception as e:
                print(f"خطأ في تحليل مخرجات PowerShell: {str(e)}")
                print(f"المخرجات: {result.stdout}")
        else:
            print("لم يتم العثور على أجهزة باستخدام PowerShell أو حدث خطأ")
            print(f"رمز الخروج: {result.returncode}")
            print(f"الخطأ: {result.stderr}")
    except Exception as e:
        print(f"خطأ في الحصول على الماسحات الضوئية باستخدام PowerShell: {str(e)}")
        traceback.print_exc()

    return scanners

def get_all_scanners():
    """
    الحصول على قائمة بجميع الماسحات الضوئية باستخدام جميع الطرق المتاحة
    """
    all_scanners = []

    print("=== بدء البحث عن الماسحات الضوئية باستخدام جميع الطرق المتاحة ===")

    # الحصول على الماسحات الضوئية باستخدام PowerShell مباشرة
    powershell_scanners = get_direct_powershell_scanners()
    print(f"تم العثور على {len(powershell_scanners)} ماسح ضوئي باستخدام PowerShell مباشرة")
    all_scanners.extend(powershell_scanners)

    # الحصول على الماسحات الضوئية باستخدام WMI
    wmi_scanners = get_wmi_scanners()
    print(f"تم العثور على {len(wmi_scanners)} ماسح ضوئي باستخدام WMI")
    all_scanners.extend(wmi_scanners)

    # الحصول على الماسحات الضوئية باستخدام مدير الأجهزة
    device_manager_scanners = get_device_manager_scanners()
    print(f"تم العثور على {len(device_manager_scanners)} ماسح ضوئي باستخدام مدير الأجهزة")
    all_scanners.extend(device_manager_scanners)

    # الحصول على الماسحات الضوئية باستخدام TWAIN
    twain_scanners = get_twain_scanners()
    print(f"تم العثور على {len(twain_scanners)} ماسح ضوئي باستخدام TWAIN")
    all_scanners.extend(twain_scanners)

    # الحصول على الماسحات الضوئية باستخدام WIA
    wia_scanners = get_wia_scanners()
    print(f"تم العثور على {len(wia_scanners)} ماسح ضوئي باستخدام WIA")
    all_scanners.extend(wia_scanners)

    # إزالة التكرارات
    unique_scanners = []
    unique_names = set()

    for scanner in all_scanners:
        if scanner['name'] not in unique_names:
            unique_names.add(scanner['name'])
            unique_scanners.append(scanner)

    print(f"تم العثور على {len(unique_scanners)} ماسح ضوئي فريد")

    # إضافة ماسح ضوئي CANON DR-M260 يدويًا إذا لم يتم اكتشافه
    canon_found = False
    for scanner in unique_scanners:
        if "CANON DR-M260" in scanner['name'].upper():
            canon_found = True
            break

    if not canon_found:
        canon_scanner = {
            'id': "canon_dr_m260",
            'name': "CANON DR-M260 (مضاف يدويًا)",
            'device_id': "{6BDD1FC6-810F-11D0-BEC7-08002BE2092F}\\0005",
            'source': 'manual'
        }
        unique_scanners.append(canon_scanner)
        print("تم إضافة ماسح ضوئي CANON DR-M260 يدويًا")

    # إضافة ماسح ضوئي افتراضي إذا لم يتم العثور على أي ماسح ضوئي
    if not unique_scanners:
        default_scanner = {
            'id': 'default_scanner',
            'name': 'ماسح ضوئي افتراضي (للاختبار)',
            'device_id': 'default_scanner',
            'source': 'default'
        }
        unique_scanners.append(default_scanner)
        print("تم إضافة ماسح ضوئي افتراضي لأنه لم يتم العثور على أي ماسح ضوئي")

    print("=== انتهاء البحث عن الماسحات الضوئية ===")

    return unique_scanners

if __name__ == "__main__":
    # اختبار الوحدة
    scanners = get_all_scanners()
    print(f"تم العثور على {len(scanners)} ماسح ضوئي فريد")
    for scanner in scanners:
        print(f"المعرف: {scanner['id']}, الاسم: {scanner['name']}, المصدر: {scanner['source']}")
