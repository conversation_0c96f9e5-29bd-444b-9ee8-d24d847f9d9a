from app import app, db
from models import Document
import os
from sqlalchemy import inspect

def update_database():
    """تحديث قاعدة البيانات وإضافة الجداول الجديدة"""
    with app.app_context():
        # إنشاء جدول Document إذا لم يكن موجودًا
        inspector = inspect(db.engine)
        if 'document' not in inspector.get_table_names():
            print("إنشاء جدول 'document'...")
            Document.__table__.create(db.engine)
            print("تم إنشاء جدول 'document' بنجاح.")
        else:
            print("جدول 'document' موجود بالفعل.")

if __name__ == '__main__':
    # التأكد من وجود مجلد instance
    if not os.path.exists('instance'):
        os.makedirs('instance')

    update_database()