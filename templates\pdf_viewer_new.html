<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ document_name }}</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }

        .pdf-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .toolbar {
            background-color: #0d47a1;
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 100;
        }

        .toolbar h3 {
            margin: 0;
            font-size: 18px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 70%;
        }

        .toolbar-actions {
            display: flex;
            gap: 10px;
        }

        .toolbar-button {
            color: white;
            text-decoration: none;
            padding: 6px 12px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
        }

        .toolbar-button:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .pdf-content {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0;
            overflow: hidden;
            background-color: #f0f0f0;
        }

        .pdf-frame {
            width: 100%;
            height: 100%;
            border: none;
        }

        .pdf-error {
            text-align: center;
            padding: 20px;
            background-color: #ffebee;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 500px;
        }

        .pdf-error h3 {
            color: #d32f2f;
            margin-top: 0;
        }

        .pdf-error p {
            color: #555;
            margin-bottom: 20px;
        }

        .pdf-error-icon {
            font-size: 48px;
            color: #d32f2f;
            margin-bottom: 20px;
        }

        .loading-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #0d47a1;
            animation: spin 1s linear infinite;
            margin-bottom: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="pdf-container">
        <div class="toolbar">
            <h3>{{ document_name }}</h3>
            <div class="toolbar-actions">
                <button class="toolbar-button" onclick="printPdf()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <a href="{{ back_url }}" class="toolbar-button">
                    <i class="fas fa-arrow-right"></i> رجوع
                </a>
            </div>
        </div>
        <div class="pdf-content" id="pdfContent">
            <div class="loading-indicator">
                <div class="spinner"></div>
                <p>جاري تحميل الملف...</p>
            </div>
        </div>
    </div>

    <script>
        // تحميل ملف PDF باستخدام JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            const pdfContent = document.getElementById('pdfContent');
            const pdfUrl = "{{ document_url }}";
            
            // إنشاء عنصر iframe
            const iframe = document.createElement('iframe');
            iframe.className = 'pdf-frame';
            
            // معالجة حدث تحميل الملف
            iframe.onload = function() {
                // التحقق مما إذا كان الملف قد تم تحميله بنجاح
                try {
                    // محاولة الوصول إلى محتوى الإطار
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    
                    // إذا كان المحتوى فارغًا أو يحتوي على رسالة خطأ
                    if (!iframeDoc || iframeDoc.body.innerHTML.includes('error') || iframeDoc.body.innerHTML === '') {
                        throw new Error('فشل تحميل ملف PDF');
                    }
                } catch (e) {
                    // في حالة حدوث خطأ، عرض رسالة خطأ
                    pdfContent.innerHTML = `
                        <div class="pdf-error">
                            <div class="pdf-error-icon">⚠️</div>
                            <h3>حدث خطأ في معالجة الملف</h3>
                            <p>لم نتمكن من تحميل ملف PDF. يرجى المحاولة مرة أخرى أو التواصل مع مسؤول النظام.</p>
                            <button class="toolbar-button" onclick="window.location.reload()">
                                إعادة المحاولة
                            </button>
                        </div>
                    `;
                    return;
                }
            };
            
            // معالجة حدث خطأ تحميل الملف
            iframe.onerror = function() {
                pdfContent.innerHTML = `
                    <div class="pdf-error">
                        <div class="pdf-error-icon">⚠️</div>
                        <h3>حدث خطأ في معالجة الملف</h3>
                        <p>لم نتمكن من تحميل ملف PDF. يرجى المحاولة مرة أخرى أو التواصل مع مسؤول النظام.</p>
                        <button class="toolbar-button" onclick="window.location.reload()">
                            إعادة المحاولة
                        </button>
                    </div>
                `;
            };
            
            // تعيين مصدر الملف لبدء التحميل
            iframe.src = pdfUrl;
            
            // إضافة الإطار إلى المحتوى بعد مهلة قصيرة
            setTimeout(function() {
                pdfContent.innerHTML = '';
                pdfContent.appendChild(iframe);
            }, 500);
        });
        
        // وظيفة الطباعة
        function printPdf() {
            const iframe = document.querySelector('.pdf-frame');
            if (iframe) {
                try {
                    iframe.contentWindow.print();
                } catch (e) {
                    window.print();
                }
            }
        }
    </script>

    <!-- تضمين Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
        integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
</body>
</html>
