{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header">
            <h2>استيراد بيانات الموظفين</h2>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h5>تعليمات الاستيراد:</h5>
                <ul>
                    <li>يجب أن يكون الملف بصيغة Excel (.xlsx, .xls) أو CSV (.csv)</li>
                    <li>يجب أن يحتوي الملف على الأعمدة التالية: full_name, employee_number, job_title, work_location</li>
                    <li>سيتم تعيين رصيد الإجازات الافتراضي (36 يوم) لكل موظف</li>
                    <li>لن يتم إنشاء حسابات للموظفين، فقط المسؤول يمكنه الدخول للنظام</li>
                </ul>
            </div>

            <form method="POST" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="file" class="form-label">اختر ملف:</label>
                    <input type="file" id="file" name="file" class="form-control" accept=".xlsx,.xls,.csv" required>
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">استيراد البيانات</button>
                    <a href="{{ url_for('employees') }}" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>

            <div class="mt-4">
                <h5>نموذج ملف الاستيراد:</h5>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>full_name</th>
                                <th>employee_number</th>
                                <th>job_title</th>
                                <th>work_location</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>أحمد محمد</td>
                                <td>1001</td>
                                <td>مهندس برمجيات</td>
                                <td>الرياض</td>
                            </tr>
                            <tr>
                                <td>سارة أحمد</td>
                                <td>1002</td>
                                <td>محاسب</td>
                                <td>جدة</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="mt-2">
                    <a href="#" class="btn btn-sm btn-outline-primary" onclick="downloadSampleFile()">تحميل نموذج فارغ</a>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
    function downloadSampleFile() {
        // إنشاء بيانات CSV
        const csvContent = "full_name,employee_number,job_title,work_location\nأحمد محمد,1001,مهندس برمجيات,الرياض\nسارة أحمد,1002,محاسب,جدة";

        // إنشاء رابط تنزيل
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);

        link.setAttribute("href", url);
        link.setAttribute("download", "employees_template.csv");
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
</script>
{% endblock %}
{% endblock %}
