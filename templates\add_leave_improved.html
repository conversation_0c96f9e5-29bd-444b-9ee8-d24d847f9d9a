{% extends 'base.html' %}

{% block styles %}
<style>
    .bg-gradient-primary-to-secondary {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    }

    .card {
        transition: all 0.3s ease;
        overflow: hidden;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    /* إصلاح اتجاه النص في حقول التاريخ */
    input[type="date"] {
        -webkit-appearance: none;
        appearance: none;
        direction: rtl !important;
        unicode-bidi: bidi-override !important;
        text-align: right !important;
        font-family: Arial, sans-serif !important;
        background-color: #f8f9ff !important;
        border: 2px solid #d1d9ff !important;
        color: #333 !important;
        font-weight: 500 !important;
    }
    /* تطبيق الانعكاس لحقول التاريخ في WebKit */
    input[type="date"]::-webkit-datetime-edit,
    input[type="date"]::-webkit-datetime-edit-year-field,
    input[type="date"]::-webkit-datetime-edit-month-field,
    input[type="date"]::-webkit-datetime-edit-day-field,
    input[type="date"]::-webkit-datetime-edit-text {
        direction: rtl !important;
        text-align: right !important;
    }

    /* تنسيق أيقونة التقويم */
    input[type="date"]::-webkit-calendar-picker-indicator {
        opacity: 1;
        display: block;
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 24 24"><path fill="%234e73df" d="M20 3h-1V1h-2v2H7V1H5v2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 18H4V8h16v13z"/></svg>');
        width: 20px;
        height: 20px;
        cursor: pointer;
        position: absolute;
        left: 10px;
    }

    input[type="date"]:focus {
        background-color: #fff !important;
        border-color: #4e73df !important;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25) !important;
    }

    /* تحسين مظهر حقول الإدخال الأخرى */
    input[type="number"], select, textarea {
        background-color: #f8f9ff !important;
        border: 2px solid #d1d9ff !important;
        color: #333 !important;
        font-weight: 500 !important;
    }
    input[type="number"]:focus, select:focus, textarea:focus {
        background-color: #fff !important;
        border-color: #4e73df !important;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25) !important;
    }

    /* تحسين مظهر العناوين */
    .form-label {
        font-weight: bold !important;
        color: #2e4094 !important;
        font-size: 1.05rem !important;
    }

    /* تحسين مظهر الأزرار */
    .btn-primary {
        background-color: #4e73df;
        border-color: #4e73df;
    }

    .btn-primary:hover {
        background-color: #224abe;
        border-color: #224abe;
    }
</style>
{% endblock %}

{% block content %}
<div class="container" dir="rtl">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-gradient-primary-to-secondary text-white">
            <h2 class="mb-0"><i class="fas fa-calendar-plus me-2"></i> إضافة إجازة للموظف: {{ employee.full_name }}</h2>
        </div>
        <div class="card-body bg-light">
            <form method="POST">
                <div class="mb-3" dir="rtl">
                    <label for="leave_type_id" class="form-label">نوع الإجازة:</label>
                    <div class="input-group">
                        <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width:45px;"><i class="fas fa-list-alt"></i></span>
                        <select name="leave_type_id" id="leave_type_id" class="form-select" required>
                            <option value="">اختر نوع الإجازة</option>
                            {% for leave_type in leave_types %}
                                <option value="{{ leave_type.id }}" data-is-maternity="{{ '1' if 'أمومة' in leave_type.name or 'وضع' in leave_type.name else '0' }}">
                                    {{ leave_type.name }}{% if leave_type.description %} ({{ leave_type.description }}){% endif %}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div id="children-count-container" class="mb-3" style="display:none;" dir="rtl">
                    <label for="children_count" class="form-label">عدد الأطفال:</label>
                    <div class="input-group">
                        <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width:45px;"><i class="fas fa-baby"></i></span>
                        <input type="number" name="children_count" id="children_count" class="form-control" min="1" value="1">
                    </div>
                    <div class="form-text text-muted"><i class="fas fa-info-circle"></i> عدد الأطفال لإجازات الأمومة</div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3" dir="rtl">
                        <label for="start_date" class="form-label">تاريخ بدء الإجازة:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width:45px;"><i class="fas fa-calendar-day"></i></span>
                            <input type="date" name="start_date" id="start_date" class="form-control" required lang="ar" dir="rtl" placeholder="yyyy-mm-dd">
                        </div>
                    </div>
                    <div class="col-md-6 mb-3" dir="rtl">
                        <label for="days_count" class="form-label">مدة الإجازة (أيام):</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width:45px;"><i class="fas fa-calculator"></i></span>
                            <input type="number" name="days_count" id="days_count" class="form-control" min="1" value="1" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3" dir="rtl">
                        <label for="end_date" class="form-label">تاريخ انتهاء الإجازة:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width:45px;"><i class="fas fa-calendar-check"></i></span>
                            <input type="date" name="end_date" id="end_date" class="form-control" readonly lang="ar" dir="rtl" placeholder="yyyy-mm-dd">
                        </div>
                    </div>
                    <div class="col-md-6 mb-3" dir="rtl">
                        <label for="departure_date" class="form-label">تاريخ الانفكاك:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width:45px;"><i class="fas fa-sign-out-alt"></i></span>
                            <input type="date" name="departure_date" id="departure_date" class="form-control" lang="ar" dir="rtl" placeholder="yyyy-mm-dd">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3" dir="rtl">
                        <label for="return_date" class="form-label">تاريخ العودة:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width:45px;"><i class="fas fa-calendar-plus"></i></span>
                            <input type="date" name="return_date" id="return_date" class="form-control" lang="ar" dir="rtl" placeholder="yyyy-mm-dd">
                        </div>
                    </div>
                </div>

                <hr>
                <h5 class="mb-3 text-primary" dir="rtl"><i class="fas fa-file-alt me-2"></i> بيانات الأمر الإداري</h5>
                <div class="row">
                    <div class="col-md-4 mb-3" dir="rtl">
                        <label for="order_number" class="form-label">رقم الأمر:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width:45px;"><i class="fas fa-hashtag"></i></span>
                            <input type="text" name="order_number" id="order_number" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4 mb-3" dir="rtl">
                        <label for="order_date" class="form-label">تاريخ الأمر:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width:45px;"><i class="fas fa-calendar-alt"></i></span>
                            <input type="date" name="order_date" id="order_date" class="form-control" lang="ar" dir="rtl" placeholder="yyyy-mm-dd">
                        </div>
                    </div>
                    <div class="col-md-4 mb-3" dir="rtl">
                        <label for="order_source" class="form-label">الجهة المصدرة:</label>
                        <div class="input-group">
                            <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width:45px;"><i class="fas fa-building"></i></span>
                            <input type="text" name="order_source" id="order_source" class="form-control">
                        </div>
                    </div>
                </div>

                <div class="mb-3" dir="rtl">
                    <label for="comment" class="form-label">ملاحظات:</label>
                    <div class="input-group">
                        <span class="input-group-text bg-gradient-primary-to-secondary text-white" style="width:45px;"><i class="fas fa-comment-alt"></i></span>
                        <textarea name="comment" id="comment" class="form-control" rows="3"></textarea>
                    </div>
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-paper-plane me-2"></i> إضافة الإجازة
                    </button>
                    <a href="{{ url_for('employee_leaves', employee_id=employee.id) }}" class="btn btn-secondary btn-lg">
                        <i class="fas fa-times me-2"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/weekend_helper.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تعريف المتغيرات
        const leaveTypeSelect = document.getElementById('leave_type_id');
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');
        const daysCountInput = document.getElementById('days_count');
        const departureDateInput = document.getElementById('departure_date');
        const returnDateInput = document.getElementById('return_date');
        const childrenCountContainer = document.getElementById('children-count-container');
        const childrenCountInput = document.getElementById('children_count');

        // قاموس لتخزين مدة الإجازة لكل نوع
        const leaveDaysMap = {
            {% for leave_type in leave_types %}
                {% if leave_type.days_allowed %}
                    "{{ leave_type.id }}": {{ leave_type.days_allowed }},
                {% endif %}
            {% endfor %}
        };

        // دالة لحساب تاريخ النهاية
        function calculateEndDate(startDate, days) {
            if (!startDate || !days) return '';
            const start = new Date(startDate);
            const end = new Date(start);
            end.setDate(start.getDate() + parseInt(days) - 1);
            return end.toISOString().split('T')[0];
        }

        // دالة لتحديث تاريخ النهاية وتواريخ أخرى
        function updateEndDate() {
            const startDate = startDateInput.value;
            const daysCount = daysCountInput.value;

            if (startDate && daysCount) {
                // حساب تاريخ النهاية
                endDateInput.value = calculateEndDate(startDate, daysCount);

                // اقتراح تاريخ الانفكاك (نفس تاريخ البداية)
                if (!departureDateInput.value) {
                    departureDateInput.value = startDate;
                }

                // اقتراح تاريخ العودة (اليوم التالي لتاريخ النهاية)
                const endDate = new Date(endDateInput.value);
                let returnDate = new Date(endDate);
                returnDate.setDate(endDate.getDate() + 1);

                // التحقق مما إذا كان تاريخ العودة يصادف يوم الجمعة أو السبت
                // وتعديله إلى يوم الأحد التالي إذا كان كذلك
                returnDate = adjustReturnDateForWeekend(returnDate);

                // تنسيق التاريخ بالشكل الصحيح YYYY-MM-DD
                const formattedReturnDate = formatDate(returnDate);

                // تعيين تاريخ العودة إذا كان فارغًا
                if (!returnDateInput.value) {
                    returnDateInput.value = formattedReturnDate;
                }
            }
        }

        // دالة للتحقق من نوع الإجازة (أمومة أو لا)
        function checkIfMaternityLeave() {
            const selectedOption = leaveTypeSelect.options[leaveTypeSelect.selectedIndex];
            if (selectedOption && selectedOption.getAttribute('data-is-maternity') === '1') {
                childrenCountContainer.style.display = 'block';
                childrenCountInput.setAttribute('required', 'required');
            } else {
                childrenCountContainer.style.display = 'none';
                childrenCountInput.removeAttribute('required');
            }
        }

        // تحديث مدة الإجازة عند اختيار نوع الإجازة
        leaveTypeSelect.addEventListener('change', function() {
            const selectedLeaveTypeId = this.value;

            // التحقق من نوع الإجازة (أمومة أو لا)
            checkIfMaternityLeave();

            // تعيين مدة الإجازة حسب النوع
            if (selectedLeaveTypeId && leaveDaysMap[selectedLeaveTypeId]) {
                daysCountInput.value = leaveDaysMap[selectedLeaveTypeId];
                updateEndDate();
            }
        });

        // تحديث عند تغيير تاريخ البداية
        startDateInput.addEventListener('change', updateEndDate);

        // تحديث عند تغيير عدد الأيام
        daysCountInput.addEventListener('change', function() {
            // إعادة تعيين تاريخ العودة
            returnDateInput.value = '';
            updateEndDate();
        });

        daysCountInput.addEventListener('input', function() {
            // إعادة تعيين تاريخ العودة
            returnDateInput.value = '';
            updateEndDate();
        });

        // تشغيل عند تحميل الصفحة
        setTimeout(function() {
            updateEndDate();
            checkIfMaternityLeave();
        }, 200);
    });
</script>
{% endblock %}
