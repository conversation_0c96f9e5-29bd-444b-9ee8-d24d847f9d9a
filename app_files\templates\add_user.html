{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-lg border-0 rounded-lg mt-4">
                <div class="card-header bg-gradient-primary-to-secondary text-white text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i> إضافة مستخدم جديد
                    </h2>
                </div>
                <div class="card-body bg-light p-5">
                    <form method="POST">
                        <div class="mb-4">
                            <label for="username" class="form-label fw-bold">اسم المستخدم:</label>
                            <div class="input-group">
                                <span class="input-group-text bg-primary text-white">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" id="username" name="username" class="form-control form-control-lg" required>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="password" class="form-label fw-bold">كلمة المرور:</label>
                            <div class="input-group">
                                <span class="input-group-text bg-primary text-white">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" id="password" name="password" class="form-control form-control-lg" required>
                            </div>
                        </div>

                        <!-- حقول مخفية لإرسال القيم الافتراضية -->
                        <input type="hidden" name="full_name" value="مستخدم جديد">
                        <input type="hidden" name="job_title" value="موظف">
                        <input type="hidden" name="work_location" value="المقر الرئيسي">
                        <input type="hidden" name="leave_balance" value="36">
                        <input type="hidden" name="email" value="">
                        <input type="hidden" name="department" value="">

                        <div class="mb-4">
                            <label class="form-label fw-bold">نوع الحساب:</label>
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="form-check mb-2">
                                        <input type="radio" id="user_type_normal" name="user_type" value="normal" class="form-check-input" checked>
                                        <label for="user_type_normal" class="form-check-label">
                                            <span class="badge bg-info me-2">
                                                <i class="fas fa-user me-1"></i> مستخدم عادي
                                            </span>
                                            يمكنه الوصول إلى الميزات المحددة فقط
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" id="user_type_admin" name="user_type" value="admin" class="form-check-input">
                                        <label for="user_type_admin" class="form-check-label">
                                            <span class="badge bg-danger me-2">
                                                <i class="fas fa-user-shield me-1"></i> مدير النظام
                                            </span>
                                            يمتلك جميع الصلاحيات
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4" id="permissions_section">
                            <label class="form-label fw-bold">الصلاحيات:</label>
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="form-check mb-2">
                                        <input type="checkbox" id="perm_all" name="permissions" value="all" class="form-check-input">
                                        <label for="perm_all" class="form-check-label">
                                            <i class="fas fa-key text-danger me-1"></i> <strong>جميع الصلاحيات</strong>
                                        </label>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" id="perm_view_employees" name="permissions" value="view_employees" class="form-check-input permission-checkbox">
                                                <label for="perm_view_employees" class="form-check-label">
                                                    <i class="fas fa-eye text-primary me-1"></i> عرض الموظفين
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" id="perm_add_employees" name="permissions" value="add_employees" class="form-check-input permission-checkbox">
                                                <label for="perm_add_employees" class="form-check-label">
                                                    <i class="fas fa-user-plus text-success me-1"></i> إضافة موظفين
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" id="perm_edit_employees" name="permissions" value="edit_employees" class="form-check-input permission-checkbox">
                                                <label for="perm_edit_employees" class="form-check-label">
                                                    <i class="fas fa-user-edit text-warning me-1"></i> تعديل بيانات الموظفين
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" id="perm_delete_employees" name="permissions" value="delete_employees" class="form-check-input permission-checkbox">
                                                <label for="perm_delete_employees" class="form-check-label">
                                                    <i class="fas fa-user-times text-danger me-1"></i> حذف الموظفين
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" id="perm_view_leaves" name="permissions" value="view_leaves" class="form-check-input permission-checkbox">
                                                <label for="perm_view_leaves" class="form-check-label">
                                                    <i class="fas fa-calendar-alt text-info me-1"></i> عرض الإجازات
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" id="perm_add_leaves" name="permissions" value="add_leaves" class="form-check-input permission-checkbox">
                                                <label for="perm_add_leaves" class="form-check-label">
                                                    <i class="fas fa-calendar-plus text-success me-1"></i> إضافة إجازات
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" id="perm_edit_leaves" name="permissions" value="edit_leaves" class="form-check-input permission-checkbox">
                                                <label for="perm_edit_leaves" class="form-check-label">
                                                    <i class="fas fa-calendar-day text-warning me-1"></i> تعديل الإجازات
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" id="perm_delete_leaves" name="permissions" value="delete_leaves" class="form-check-input permission-checkbox">
                                                <label for="perm_delete_leaves" class="form-check-label">
                                                    <i class="fas fa-calendar-times text-danger me-1"></i> حذف الإجازات
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" id="perm_manage_leave_types" name="permissions" value="manage_leave_types" class="form-check-input permission-checkbox">
                                                <label for="perm_manage_leave_types" class="form-check-label">
                                                    <i class="fas fa-list-alt text-primary me-1"></i> إدارة أنواع الإجازات
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" id="perm_manage_documents" name="permissions" value="manage_documents" class="form-check-input permission-checkbox">
                                                <label for="perm_manage_documents" class="form-check-label">
                                                    <i class="fas fa-file-alt text-info me-1"></i> إدارة المستندات
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" id="perm_view_documents" name="permissions" value="view_documents" class="form-check-input permission-checkbox">
                                                <label for="perm_view_documents" class="form-check-label">
                                                    <i class="fas fa-file text-secondary me-1"></i> عرض المستندات
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" id="perm_search_leaves" name="permissions" value="search_leaves" class="form-check-input permission-checkbox">
                                                <label for="perm_search_leaves" class="form-check-label">
                                                    <i class="fas fa-search text-primary me-1"></i> البحث في الإجازات
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" id="perm_backup_database" name="permissions" value="backup_database" class="form-check-input permission-checkbox">
                                                <label for="perm_backup_database" class="form-check-label">
                                                    <i class="fas fa-database text-success me-1"></i> النسخ الاحتياطي
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" id="perm_update_return_status" name="permissions" value="update_return_status" class="form-check-input permission-checkbox">
                                                <label for="perm_update_return_status" class="form-check-label">
                                                    <i class="fas fa-undo text-warning me-1"></i> تحديث حالة العودة
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" id="perm_manage_users" name="permissions" value="manage_users" class="form-check-input permission-checkbox">
                                                <label for="perm_manage_users" class="form-check-label">
                                                    <i class="fas fa-users-cog text-danger me-1"></i> إدارة المستخدمين
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 mt-5">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i> إضافة المستخدم
                            </button>
                            <a href="{{ url_for('user_management') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i> العودة إلى قائمة المستخدمين
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .bg-gradient-primary-to-secondary {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    }

    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-5px);
    }

    .form-control:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const userTypeNormal = document.getElementById('user_type_normal');
        const userTypeAdmin = document.getElementById('user_type_admin');
        const permissionsSection = document.getElementById('permissions_section');
        const permAll = document.getElementById('perm_all');
        const permissionCheckboxes = document.querySelectorAll('.permission-checkbox');

        function togglePermissionsSection() {
            if (userTypeAdmin.checked) {
                permissionsSection.style.display = 'none';
            } else {
                permissionsSection.style.display = 'block';
            }
        }

        function togglePermissions() {
            permissionCheckboxes.forEach(function(checkbox) {
                checkbox.checked = permAll.checked;
                checkbox.disabled = permAll.checked;
            });
        }

        userTypeNormal.addEventListener('change', togglePermissionsSection);
        userTypeAdmin.addEventListener('change', togglePermissionsSection);
        permAll.addEventListener('change', togglePermissions);

        // تشغيل عند تحميل الصفحة
        togglePermissionsSection();
        togglePermissions();
    });
</script>
{% endblock %}
